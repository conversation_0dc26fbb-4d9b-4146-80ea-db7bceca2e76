import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  FlatList,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

interface FavoritePlacesScreenProps {}

interface FavoritePlace {
  id: string;
  name: string;
  description: string;
  category: {
    id: string;
    name: string;
    icon: string;
    color: string;
  };
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    formattedAddress: string;
  };
  rating: {
    average: number;
    count: number;
  };
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  photos: Array<{
    id: string;
    url: string;
    thumbnailUrl: string;
  }>;
  addedAt: string;
  lastVisited?: string;
  visitCount: number;
  distance?: number;
  estimatedTime?: string;
  isOpen?: boolean;
  tags: string[];
}

type SortOption = 'recent' | 'distance' | 'rating' | 'name' | 'visits';
type ViewMode = 'list' | 'map';

const FavoritePlacesScreen: React.FC<FavoritePlacesScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [places, setPlaces] = useState<FavoritePlace[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<FavoritePlace[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [error, setError] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'Все', icon: 'apps', color: '#666666' },
    { id: 'cafe', name: 'Кафе', icon: 'local-cafe', color: '#8D6E63' },
    { id: 'restaurant', name: 'Рестораны', icon: 'restaurant', color: '#FF5722' },
    { id: 'bar', name: 'Бары', icon: 'local-bar', color: '#9C27B0' },
    { id: 'park', name: 'Парки', icon: 'park', color: '#4CAF50' },
    { id: 'cinema', name: 'Кино', icon: 'movie', color: '#2196F3' },
    { id: 'shopping', name: 'Магазины', icon: 'shopping-bag', color: '#FF9800' },
  ];

  useFocusEffect(
    useCallback(() => {
      loadFavoritePlaces();
      getCurrentLocation();
    }, [])
  );

  useEffect(() => {
    filterAndSortPlaces();
  }, [places, searchQuery, sortBy, selectedCategory]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }
    } catch (error) {
      console.log('Location error:', error);
    }
  };

  const loadFavoritePlaces = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockPlaces: FavoritePlace[] = [
        {
          id: '1',
          name: 'Кафе "Встреча"',
          description: 'Уютное кафе с домашней атмосферой',
          category: {
            id: 'cafe',
            name: 'Кафе',
            icon: 'local-cafe',
            color: '#8D6E63'
          },
          location: {
            latitude: 55.7558,
            longitude: 37.6176,
            address: 'ул. Тверская, 15',
            city: 'Москва',
            formattedAddress: 'ул. Тверская, 15, Москва'
          },
          rating: {
            average: 4.6,
            count: 127
          },
          priceRange: '$$',
          photos: [
            {
              id: '1',
              url: 'https://example.com/photo1.jpg',
              thumbnailUrl: 'https://example.com/thumb1.jpg'
            }
          ],
          addedAt: '2024-01-10T10:00:00Z',
          lastVisited: '2024-01-12T15:30:00Z',
          visitCount: 3,
          distance: 0.8,
          estimatedTime: '10 мин',
          isOpen: true,
          tags: ['романтично', 'уютно', 'кофе']
        },
        {
          id: '2',
          name: 'Ресторан "Вкус"',
          description: 'Авторская кухня и изысканные блюда',
          category: {
            id: 'restaurant',
            name: 'Рестораны',
            icon: 'restaurant',
            color: '#FF5722'
          },
          location: {
            latitude: 55.7489,
            longitude: 37.6176,
            address: 'ул. Арбат, 25',
            city: 'Москва',
            formattedAddress: 'ул. Арбат, 25, Москва'
          },
          rating: {
            average: 4.8,
            count: 89
          },
          priceRange: '$$$',
          photos: [
            {
              id: '2',
              url: 'https://example.com/photo2.jpg',
              thumbnailUrl: 'https://example.com/thumb2.jpg'
            }
          ],
          addedAt: '2024-01-08T14:20:00Z',
          visitCount: 1,
          distance: 1.2,
          estimatedTime: '15 мин',
          isOpen: false,
          tags: ['изысканно', 'дорого', 'романтично']
        },
        {
          id: '3',
          name: 'Парк Горького',
          description: 'Центральный парк культуры и отдыха',
          category: {
            id: 'park',
            name: 'Парки',
            icon: 'park',
            color: '#4CAF50'
          },
          location: {
            latitude: 55.7312,
            longitude: 37.6016,
            address: 'Крымский Вал, 9',
            city: 'Москва',
            formattedAddress: 'Крымский Вал, 9, Москва'
          },
          rating: {
            average: 4.4,
            count: 256
          },
          priceRange: '$',
          photos: [
            {
              id: '3',
              url: 'https://example.com/photo3.jpg',
              thumbnailUrl: 'https://example.com/thumb3.jpg'
            }
          ],
          addedAt: '2024-01-05T09:15:00Z',
          lastVisited: '2024-01-11T11:00:00Z',
          visitCount: 5,
          distance: 2.1,
          estimatedTime: '25 мин',
          isOpen: true,
          tags: ['природа', 'прогулки', 'спорт']
        }
      ];

      setPlaces(mockPlaces);
    } catch (err: any) {
      setError('Ошибка загрузки избранных мест');
      console.error('Favorite places loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadFavoritePlaces();
    setRefreshing(false);
  }, []);

  const filterAndSortPlaces = () => {
    let filtered = [...places];

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(place =>
        place.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        place.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        place.location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        place.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(place => place.category.id === selectedCategory);
    }

    // Sort places
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        case 'rating':
          return b.rating.average - a.rating.average;
        case 'name':
          return a.name.localeCompare(b.name);
        case 'visits':
          return b.visitCount - a.visitCount;
        default:
          return 0;
      }
    });

    setFilteredPlaces(filtered);
  };

  const handleRemoveFromFavorites = async (placeId: string) => {
    Alert.alert(
      'Удалить из избранного',
      'Вы уверены, что хотите удалить это место из избранного?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 500));

              setPlaces(prev => prev.filter(place => place.id !== placeId));
              Alert.alert('Успех', 'Место удалено из избранного');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось удалить место');
            }
          }
        }
      ]
    );
  };

  const handlePlacePress = (place: FavoritePlace) => {
    navigation.navigate('PlaceDetail', { placeId: place.id });
  };

  const handleCreateMeeting = (place: FavoritePlace) => {
    navigation.navigate('CreateMeeting', {
      selectedPlace: {
        id: place.id,
        name: place.name,
        address: place.location.formattedAddress,
        latitude: place.location.latitude,
        longitude: place.location.longitude
      }
    });
  };

  const handleDirections = (place: FavoritePlace) => {
    if (userLocation) {
      const url = `https://www.openstreetmap.org/directions?from=${userLocation.latitude},${userLocation.longitude}&to=${place.location.latitude},${place.location.longitude}`;
      // TODO: Open in browser or maps app
      Alert.alert('Навигация', 'Открыть маршрут в картах?');
    }
  };

  const getSortLabel = (option: SortOption): string => {
    switch (option) {
      case 'recent': return 'По дате добавления';
      case 'distance': return 'По расстоянию';
      case 'rating': return 'По рейтингу';
      case 'name': return 'По названию';
      case 'visits': return 'По посещениям';
      default: return '';
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} мин. назад`;
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч. назад`;
    } else {
      const days = Math.floor(diffInMinutes / (24 * 60));
      return `${days} дн. назад`;
    }
  };

  const renderPlaceCard = ({ item: place }: { item: FavoritePlace }) => (
    <TouchableOpacity
      style={styles.placeCard}
      onPress={() => handlePlacePress(place)}
    >
      <View style={styles.placeImageContainer}>
        <View style={styles.placeholderImage}>
          <Icon name={place.category.icon} size={32} color={place.category.color} />
        </View>

        {place.isOpen !== undefined && (
          <View style={[styles.statusBadge, { backgroundColor: place.isOpen ? '#4CAF50' : '#F44336' }]}>
            <Text style={styles.statusBadgeText}>
              {place.isOpen ? 'Открыто' : 'Закрыто'}
            </Text>
          </View>
        )}
      </View>

      <View style={styles.placeInfo}>
        <View style={styles.placeHeader}>
          <Text style={styles.placeName} numberOfLines={1}>{place.name}</Text>
          <TouchableOpacity
            style={styles.favoriteButton}
            onPress={() => handleRemoveFromFavorites(place.id)}
          >
            <Icon name="favorite" size={20} color="#E91E63" />
          </TouchableOpacity>
        </View>

        <Text style={styles.placeDescription} numberOfLines={2}>
          {place.description}
        </Text>

        <View style={styles.placeDetails}>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{place.rating.average}</Text>
            <Text style={styles.ratingCount}>({place.rating.count})</Text>
          </View>

          <Text style={styles.priceRange}>{place.priceRange}</Text>

          {place.distance && (
            <View style={styles.distanceContainer}>
              <Icon name="location-on" size={16} color="#666666" />
              <Text style={styles.distanceText}>{place.distance} км</Text>
            </View>
          )}
        </View>

        <View style={styles.placeFooter}>
          <Text style={styles.addedDate}>
            Добавлено {formatTimeAgo(place.addedAt)}
          </Text>

          <View style={styles.placeActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDirections(place)}
            >
              <Icon name="directions" size={16} color="#E91E63" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleCreateMeeting(place)}
            >
              <Icon name="event" size={16} color="#E91E63" />
            </TouchableOpacity>
          </View>
        </View>

        {place.visitCount > 0 && (
          <View style={styles.visitInfo}>
            <Icon name="history" size={14} color="#666666" />
            <Text style={styles.visitText}>
              Посещений: {place.visitCount}
              {place.lastVisited && ` • Последний раз: ${formatTimeAgo(place.lastVisited)}`}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderMapView = () => (
    <View style={styles.mapContainer}>
      <MapView
        style={styles.map}
        initialRegion={{
          latitude: userLocation?.latitude || 55.7558,
          longitude: userLocation?.longitude || 37.6176,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {filteredPlaces.map((place) => (
          <Marker
            key={place.id}
            coordinate={{
              latitude: place.location.latitude,
              longitude: place.location.longitude,
            }}
            title={place.name}
            description={place.description}
            onPress={() => handlePlacePress(place)}
          >
            <View style={[styles.customMarker, { backgroundColor: place.category.color }]}>
              <Icon name={place.category.icon} size={20} color="#ffffff" />
            </View>
          </Marker>
        ))}
      </MapView>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка избранных мест...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadFavoritePlaces}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Избранные места</Text>
        <TouchableOpacity
          style={styles.viewModeButton}
          onPress={() => setViewMode(viewMode === 'list' ? 'map' : 'list')}
        >
          <Icon name={viewMode === 'list' ? 'map' : 'list'} size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Search and Filters */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск мест..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesContainer}
        contentContainerStyle={styles.categoriesContent}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryChip,
              (selectedCategory === category.id || (selectedCategory === null && category.id === 'all')) &&
              styles.categoryChipSelected
            ]}
            onPress={() => setSelectedCategory(category.id === 'all' ? null : category.id)}
          >
            <Icon
              name={category.icon}
              size={16}
              color={
                (selectedCategory === category.id || (selectedCategory === null && category.id === 'all'))
                  ? '#ffffff'
                  : category.color
              }
            />
            <Text style={[
              styles.categoryChipText,
              (selectedCategory === category.id || (selectedCategory === null && category.id === 'all')) &&
              styles.categoryChipTextSelected
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Sort and Stats */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            const sortOptions: SortOption[] = ['recent', 'distance', 'rating', 'name', 'visits'];
            const currentIndex = sortOptions.indexOf(sortBy);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            setSortBy(sortOptions[nextIndex]);
          }}
        >
          <Icon name="sort" size={16} color="#E91E63" />
          <Text style={styles.sortButtonText}>{getSortLabel(sortBy)}</Text>
        </TouchableOpacity>

        <Text style={styles.statsText}>
          {filteredPlaces.length} из {places.length} мест
        </Text>
      </View>

      {/* Content */}
      {filteredPlaces.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="favorite-border" size={64} color="#cccccc" />
          <Text style={styles.emptyTitle}>
            {searchQuery || selectedCategory ? 'Ничего не найдено' : 'Нет избранных мест'}
          </Text>
          <Text style={styles.emptyDescription}>
            {searchQuery || selectedCategory
              ? 'Попробуйте изменить параметры поиска'
              : 'Добавляйте места в избранное, чтобы они появились здесь'
            }
          </Text>
          {!searchQuery && !selectedCategory && (
            <TouchableOpacity
              style={styles.exploreButton}
              onPress={() => navigation.navigate('Places')}
            >
              <Text style={styles.exploreButtonText}>Исследовать места</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : viewMode === 'list' ? (
        <FlatList
          data={filteredPlaces}
          renderItem={renderPlaceCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#E91E63']}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderMapView()
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  viewModeButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  categoryChipSelected: {
    backgroundColor: '#E91E63',
  },
  categoryChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  categoryChipTextSelected: {
    color: '#ffffff',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  sortButtonText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '500',
  },
  statsText: {
    fontSize: 14,
    color: '#666666',
  },
  listContainer: {
    padding: 20,
    gap: 16,
  },
  placeCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  placeImageContainer: {
    height: 120,
    position: 'relative',
  },
  placeholderImage: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeInfo: {
    padding: 16,
  },
  placeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  placeName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  favoriteButton: {
    padding: 4,
  },
  placeDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  placeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  ratingCount: {
    fontSize: 14,
    color: '#666666',
  },
  priceRange: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  distanceText: {
    fontSize: 14,
    color: '#666666',
  },
  placeFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addedDate: {
    fontSize: 12,
    color: '#999999',
  },
  placeActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
  },
  visitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  visitText: {
    fontSize: 12,
    color: '#666666',
    flex: 1,
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  exploreButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default FavoritePlacesScreen;