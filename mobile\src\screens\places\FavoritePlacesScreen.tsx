import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  TextInput
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface FavoritePlacesScreenProps {}

interface Place {
  id: string;
  name: string;
  category: string;
  rating: number;
  distance: number;
  address: string;
  priceLevel: number;
  imageUrl: string;
  latitude: number;
  longitude: number;
  isOpen: boolean;
  openingHours?: string;
  addedToFavoritesAt: string;
}

const FavoritePlacesScreen: React.FC<FavoritePlacesScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [favoritePlaces, setFavoritePlaces] = useState<Place[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'Все', icon: 'apps' },
    { id: 'restaurant', name: 'Рестораны', icon: 'restaurant' },
    { id: 'cafe', name: 'Кафе', icon: 'local-cafe' },
    { id: 'bar', name: 'Бары', icon: 'local-bar' },
    { id: 'entertainment', name: 'Развлечения', icon: 'movie' },
    { id: 'park', name: 'Парки', icon: 'park' },
    { id: 'museum', name: 'Музеи', icon: 'museum' },
    { id: 'shopping', name: 'Шопинг', icon: 'shopping-bag' }
  ];

  useEffect(() => {
    loadFavoritePlaces();
  }, []);

  const loadFavoritePlaces = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockData: Place[] = [
        {
          id: '1',
          name: 'Кафе "Пушкин"',
          category: 'cafe',
          rating: 4.5,
          distance: 0.3,
          address: 'Тверской бульвар, 26А',
          priceLevel: 3,
          imageUrl: 'https://example.com/cafe1.jpg',
          latitude: 55.7558,
          longitude: 37.6176,
          isOpen: true,
          openingHours: '08:00 - 23:00',
          addedToFavoritesAt: '2024-01-10T10:30:00Z'
        },
        {
          id: '2',
          name: 'Парк Горького',
          category: 'park',
          rating: 4.6,
          distance: 1.2,
          address: 'Крымский Вал, 9',
          priceLevel: 1,
          imageUrl: 'https://example.com/park1.jpg',
          latitude: 55.7558,
          longitude: 37.6176,
          isOpen: true,
          openingHours: '06:00 - 24:00',
          addedToFavoritesAt: '2024-01-08T15:45:00Z'
        },
        {
          id: '3',
          name: 'Третьяковская галерея',
          category: 'museum',
          rating: 4.9,
          distance: 2.1,
          address: 'Лаврушинский пер., 10',
          priceLevel: 2,
          imageUrl: 'https://example.com/museum1.jpg',
          latitude: 55.7558,
          longitude: 37.6176,
          isOpen: true,
          openingHours: '10:00 - 18:00',
          addedToFavoritesAt: '2024-01-05T09:15:00Z'
        }
      ];

      setFavoritePlaces(mockData);
    } catch (error) {
      console.error('Error loading favorite places:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить избранные места');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadFavoritePlaces();
    setRefreshing(false);
  };

  const handleRemoveFromFavorites = (place: Place) => {
    Alert.alert(
      'Удалить из избранного',
      `Вы уверены, что хотите удалить "${place.name}" из избранного?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: () => removeFromFavorites(place.id)
        }
      ]
    );
  };

  const removeFromFavorites = async (placeId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setFavoritePlaces(prev => prev.filter(place => place.id !== placeId));
      Alert.alert('Успешно', 'Место удалено из избранного');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось удалить место из избранного');
    }
  };

  const handlePlacePress = (place: Place) => {
    navigation.navigate('PlaceDetails', { place });
  };

  const handleCreateMeeting = (place: Place) => {
    navigation.navigate('CreateMeeting', { selectedPlace: place });
  };

  const getPriceSymbol = (level: number): string => {
    return '₽'.repeat(level);
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const filteredPlaces = favoritePlaces.filter(place => {
    const matchesSearch = place.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         place.address.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || place.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderPlaceCard = ({ item }: { item: Place }) => (
    <TouchableOpacity
      style={styles.placeCard}
      onPress={() => handlePlacePress(item)}
    >
      <View style={styles.placeImageContainer}>
        <Icon name="place" size={40} color="#E91E63" />
      </View>

      <View style={styles.placeInfo}>
        <View style={styles.placeHeader}>
          <Text style={styles.placeName} numberOfLines={1}>{item.name}</Text>
          <View style={styles.placeStatus}>
            <View style={[styles.statusDot, { backgroundColor: item.isOpen ? '#4CAF50' : '#ff4444' }]} />
            <Text style={[styles.statusText, { color: item.isOpen ? '#4CAF50' : '#ff4444' }]}>
              {item.isOpen ? 'Открыто' : 'Закрыто'}
            </Text>
          </View>
        </View>

        <Text style={styles.placeAddress} numberOfLines={1}>{item.address}</Text>

        <View style={styles.placeDetails}>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>

          <Text style={styles.priceLevel}>{getPriceSymbol(item.priceLevel)}</Text>

          <Text style={styles.distance}>{item.distance} км</Text>
        </View>

        <Text style={styles.addedDate}>
          Добавлено {formatDate(item.addedToFavoritesAt)}
        </Text>
      </View>

      <View style={styles.actionsContainer}>
        <TouchableOpacity
          style={styles.meetingButton}
          onPress={() => handleCreateMeeting(item)}
        >
          <Icon name="event" size={20} color="#E91E63" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.removeButton}
          onPress={() => handleRemoveFromFavorites(item)}
        >
          <Icon name="favorite" size={20} color="#E91E63" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryFilter = () => (
    <View style={styles.categoriesContainer}>
      <FlatList
        data={categories}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.id && styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(item.id)}
          >
            <Icon
              name={item.icon}
              size={16}
              color={selectedCategory === item.id ? '#ffffff' : '#666666'}
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === item.id && styles.categoryTextActive
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContent}
      />
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="favorite-border" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Нет избранных мест</Text>
      <Text style={styles.emptyDescription}>
        Добавляйте места в избранное, чтобы быстро находить их здесь
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => navigation.navigate('PlacesSearch')}
      >
        <Text style={styles.exploreButtonText}>Найти места</Text>
      </TouchableOpacity>
    </View>
  );

  const renderSearchEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="search-off" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Места не найдены</Text>
      <Text style={styles.emptyDescription}>
        Попробуйте изменить поисковый запрос или фильтры
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка избранного...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Избранные места</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Search */}
      {favoritePlaces.length > 0 && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Icon name="search" size={20} color="#666666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Поиск по избранным..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Icon name="clear" size={20} color="#666666" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Categories */}
      {favoritePlaces.length > 0 && renderCategoryFilter()}

      {/* Stats */}
      {favoritePlaces.length > 0 && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsText}>
            {filteredPlaces.length} из {favoritePlaces.length} мест
          </Text>
        </View>
      )}

      {/* Places List */}
      <FlatList
        data={filteredPlaces}
        renderItem={renderPlaceCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ListEmptyComponent={
          favoritePlaces.length === 0 ? renderEmptyState() :
          searchQuery.length > 0 || selectedCategory !== 'all' ? renderSearchEmptyState() : null
        }
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
    marginLeft: 12,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: '#E91E63',
  },
  categoryText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 6,
  },
  categoryTextActive: {
    color: '#ffffff',
  },
  statsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  statsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  placeCard: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  placeImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  placeInfo: {
    flex: 1,
  },
  placeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  placeStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  placeAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 6,
  },
  placeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 4,
  },
  priceLevel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
    marginRight: 12,
  },
  distance: {
    fontSize: 14,
    color: '#666666',
  },
  addedDate: {
    fontSize: 12,
    color: '#999999',
    fontStyle: 'italic',
  },
  actionsContainer: {
    flexDirection: 'column',
    gap: 8,
  },
  meetingButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
  },
  removeButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  exploreButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default FavoritePlacesScreen;