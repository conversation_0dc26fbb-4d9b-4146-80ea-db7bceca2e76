import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Alert,
  Skeleton,
  Fab,
  Tooltip,
  Rating,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  Analytics as AnalyticsIcon,
  Star as StarIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Event as EventIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import Layout from '../../../components/Layout/Layout';
import { partnerService } from '../../../services/partnerService';
import { Venue, VenueStats } from '../../../types/venue.types';

interface PartnerVenuesPageProps {}

const PartnerVenuesPage: React.FC<PartnerVenuesPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [venues, setVenues] = useState<Venue[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<Venue | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [deleteDialog, setDeleteDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/partner/venues');
      return;
    }

    if (user?.role !== 'partner') {
      router.push('/partner/apply');
      return;
    }

    loadVenues();
  }, [isAuthenticated, user, router]);

  const loadVenues = async () => {
    try {
      setLoading(true);
      setError(null);

      const venuesData = await partnerService.getVenues();
      setVenues(venuesData);
    } catch (err: any) {
      setError('Ошибка загрузки заведений');
      console.error('Venues loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, venue: Venue) => {
    setMenuAnchor(event.currentTarget);
    setSelectedVenue(venue);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedVenue(null);
  };

  const handleCreateVenue = () => {
    router.push('/partner/venues/create');
  };

  const handleEditVenue = (venue: Venue) => {
    router.push(`/partner/venues/${venue.id}/edit`);
    handleMenuClose();
  };

  const handleViewVenue = (venue: Venue) => {
    router.push(`/partner/venues/${venue.id}`);
    handleMenuClose();
  };

  const handleViewAnalytics = (venue: Venue) => {
    router.push(`/partner/venues/${venue.id}/analytics`);
    handleMenuClose();
  };

  const handleDeleteVenue = () => {
    setDeleteDialog(true);
    handleMenuClose();
  };

  const confirmDeleteVenue = async () => {
    if (!selectedVenue) return;

    try {
      await partnerService.deleteVenue(selectedVenue.id);
      await loadVenues();
      setDeleteDialog(false);
      setSelectedVenue(null);
    } catch (error) {
      setError('Ошибка удаления заведения');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активно';
      case 'pending':
        return 'На модерации';
      case 'suspended':
        return 'Приостановлено';
      case 'draft':
        return 'Черновик';
      default:
        return status;
    }
  };

  const renderVenueCard = (venue: Venue) => (
    <Grid item xs={12} md={6} lg={4} key={venue.id}>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardMedia
          component="div"
          sx={{
            height: 200,
            backgroundColor: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative'
          }}
        >
          <LocationIcon sx={{ fontSize: 48, color: '#E91E63' }} />

          <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
            <Chip
              label={getStatusLabel(venue.status)}
              color={getStatusColor(venue.status)}
              size="small"
            />
          </Box>
        </CardMedia>

        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="h3" gutterBottom>
              {venue.name}
            </Typography>
            <IconButton
              size="small"
              onClick={(e) => handleMenuOpen(e, venue)}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>

          <Typography variant="body2" color="text.secondary" gutterBottom>
            {venue.category} • {venue.address}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Rating value={venue.rating} readOnly size="small" />
            <Typography variant="body2" sx={{ ml: 1 }}>
              {venue.rating} ({venue.reviewsCount} отзывов)
            </Typography>
          </Box>

          <Typography variant="body2" color="text.secondary" paragraph>
            {venue.description}
          </Typography>

          {/* Stats */}
          <Grid container spacing={2} sx={{ mb: 2 }}>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {venue.stats?.bookings || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Бронирований
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {venue.stats?.views || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Просмотров
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  {venue.stats?.favorites || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  В избранном
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Contact Info */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {venue.phone && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{venue.phone}</Typography>
              </Box>
            )}
            {venue.website && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <WebsiteIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" noWrap>{venue.website}</Typography>
              </Box>
            )}
          </Box>
        </CardContent>

        <CardActions>
          <Button
            size="small"
            startIcon={<ViewIcon />}
            onClick={() => handleViewVenue(venue)}
          >
            Просмотр
          </Button>
          <Button
            size="small"
            startIcon={<AnalyticsIcon />}
            onClick={() => handleViewAnalytics(venue)}
          >
            Аналитика
          </Button>
        </CardActions>
      </Card>
    </Grid>
  );

  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Мои заведения - Партнерский портал</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ mb: 4 }}>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={500} height={24} sx={{ mt: 1 }} />
          </Box>
          <Grid container spacing={3}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={6} lg={4} key={item}>
                <Skeleton variant="rectangular" height={400} />
              </Grid>
            ))}
          </Grid>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Мои заведения - Партнерский портал</title>
        <meta name="description" content="Управление заведениями в партнерском портале LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Мои заведения
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Управляйте своими заведениями и отслеживайте статистику
            </Typography>
          </Box>

          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateVenue}
            size="large"
          >
            Добавить заведение
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Venues Grid */}
        {venues.length > 0 ? (
          <Grid container spacing={3}>
            {venues.map(venue => renderVenueCard(venue))}
          </Grid>
        ) : (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <LocationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              У вас пока нет заведений
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Добавьте свое первое заведение, чтобы начать принимать бронирования
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateVenue}
              size="large"
            >
              Добавить заведение
            </Button>
          </Box>
        )}

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="add venue"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={handleCreateVenue}
        >
          <AddIcon />
        </Fab>

        {/* Context Menu */}
        <Menu
          anchorEl={menuAnchor}
          open={Boolean(menuAnchor)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => selectedVenue && handleViewVenue(selectedVenue)}>
            <ViewIcon sx={{ mr: 1 }} />
            Просмотр
          </MenuItem>
          <MenuItem onClick={() => selectedVenue && handleEditVenue(selectedVenue)}>
            <EditIcon sx={{ mr: 1 }} />
            Редактировать
          </MenuItem>
          <MenuItem onClick={() => selectedVenue && handleViewAnalytics(selectedVenue)}>
            <AnalyticsIcon sx={{ mr: 1 }} />
            Аналитика
          </MenuItem>
          <MenuItem onClick={handleDeleteVenue} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1 }} />
            Удалить
          </MenuItem>
        </Menu>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>
          <DialogTitle>Удалить заведение</DialogTitle>
          <DialogContent>
            <Typography>
              Вы уверены, что хотите удалить заведение "{selectedVenue?.name}"?
              Это действие нельзя отменить.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialog(false)}>Отмена</Button>
            <Button onClick={confirmDeleteVenue} color="error" variant="contained">
              Удалить
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default PartnerVenuesPage;