import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Modal,
  Clipboard,
  Dimensions
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as LocalAuthentication from 'expo-local-authentication';

const { width } = Dimensions.get('window');

interface TwoFactorSetupScreenProps {}

interface TwoFactorSettings {
  isEnabled: boolean;
  method: 'sms' | 'app' | 'email' | null;
  backupCodes: string[];
  lastUsed?: string;
  setupDate?: string;
  recoveryEmail?: string;
  recoveryPhone?: string;
}

interface AuthenticatorApp {
  id: string;
  name: string;
  icon: string;
  downloadUrl: string;
  isRecommended?: boolean;
}

const TwoFactorSetupScreen: React.FC<TwoFactorSetupScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<TwoFactorSettings | null>(null);
  const [currentStep, setCurrentStep] = useState<'overview' | 'method' | 'sms' | 'app' | 'backup' | 'verify'>('overview');
  const [selectedMethod, setSelectedMethod] = useState<'sms' | 'app' | 'email'>('app');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [qrCodeSecret, setQrCodeSecret] = useState('');
  const [showQRModal, setShowQRModal] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const codeInputRefs = useRef<TextInput[]>([]);

  const authenticatorApps: AuthenticatorApp[] = [
    {
      id: 'google',
      name: 'Google Authenticator',
      icon: 'security',
      downloadUrl: 'https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2',
      isRecommended: true
    },
    {
      id: 'microsoft',
      name: 'Microsoft Authenticator',
      icon: 'verified-user',
      downloadUrl: 'https://play.google.com/store/apps/details?id=com.azure.authenticator'
    },
    {
      id: 'authy',
      name: 'Authy',
      icon: 'lock',
      downloadUrl: 'https://play.google.com/store/apps/details?id=com.authy.authy'
    }
  ];

  useEffect(() => {
    loadTwoFactorSettings();
  }, []);

  const loadTwoFactorSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockSettings: TwoFactorSettings = {
        isEnabled: false,
        method: null,
        backupCodes: [],
        recoveryEmail: '<EMAIL>',
        recoveryPhone: '+7 (999) 123-45-67'
      };

      setSettings(mockSettings);
      setEmail(mockSettings.recoveryEmail || '');
      setPhoneNumber(mockSettings.recoveryPhone || '');
    } catch (err: any) {
      setError('Ошибка загрузки настроек 2FA');
      console.error('2FA settings loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (updatedSettings: Partial<TwoFactorSettings>) => {
    try {
      setSaving(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSettings(prev => prev ? { ...prev, ...updatedSettings } : null);

      // Save to AsyncStorage
      if (settings) {
        await AsyncStorage.setItem('twoFactorSettings', JSON.stringify({ ...settings, ...updatedSettings }));
      }

    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось сохранить настройки 2FA');
      throw error;
    } finally {
      setSaving(false);
    }
  };

  const generateBackupCodes = (): string[] => {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      codes.push(code);
    }
    return codes;
  };

  const generateQRSecret = (): string => {
    return Math.random().toString(36).substring(2, 18).toUpperCase();
  };

  const handleEnable2FA = async () => {
    try {
      // Check if device supports biometric authentication
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();

      if (!hasHardware || !isEnrolled) {
        Alert.alert(
          'Биометрия недоступна',
          'Для включения 2FA рекомендуется настроить биометрическую аутентификацию на устройстве'
        );
      }

      setCurrentStep('method');
    } catch (error) {
      console.error('Biometric check error:', error);
      setCurrentStep('method');
    }
  };

  const handleMethodSelect = (method: 'sms' | 'app' | 'email') => {
    setSelectedMethod(method);

    switch (method) {
      case 'sms':
        setCurrentStep('sms');
        break;
      case 'app':
        setCurrentStep('app');
        break;
      case 'email':
        setCurrentStep('verify');
        break;
    }
  };

  const handleSMSSetup = async () => {
    if (!phoneNumber || phoneNumber.length < 10) {
      Alert.alert('Ошибка', 'Введите корректный номер телефона');
      return;
    }

    try {
      setSaving(true);

      // TODO: Send SMS verification code
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert('SMS отправлено', 'Код подтверждения отправлен на ваш номер');
      setCurrentStep('verify');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить SMS');
    } finally {
      setSaving(false);
    }
  };

  const handleAppSetup = () => {
    const secret = generateQRSecret();
    setQrCodeSecret(secret);
    setShowQRModal(true);
  };

  const handleQRCodeScanned = () => {
    setShowQRModal(false);
    setCurrentStep('verify');
  };

  const handleVerifyCode = async () => {
    if (verificationCode.length !== 6) {
      Alert.alert('Ошибка', 'Введите 6-значный код');
      return;
    }

    try {
      setSaving(true);

      // TODO: Verify the code with backend
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate backup codes
      const codes = generateBackupCodes();
      setBackupCodes(codes);

      // Save 2FA settings
      await saveSettings({
        isEnabled: true,
        method: selectedMethod,
        backupCodes: codes,
        setupDate: new Date().toISOString()
      });

      setCurrentStep('backup');
    } catch (error) {
      Alert.alert('Ошибка', 'Неверный код подтверждения');
    } finally {
      setSaving(false);
    }
  };

  const handleDisable2FA = () => {
    Alert.alert(
      'Отключить 2FA',
      'Вы уверены, что хотите отключить двухфакторную аутентификацию? Это снизит безопасность вашего аккаунта.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отключить',
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              await saveSettings({
                isEnabled: false,
                method: null,
                backupCodes: []
              });
              Alert.alert('Успех', '2FA отключена');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось отключить 2FA');
            } finally {
              setSaving(false);
            }
          }
        }
      ]
    );
  };

  const copyBackupCodes = () => {
    const codesText = backupCodes.join('\n');
    Clipboard.setString(codesText);
    Alert.alert('Скопировано', 'Резервные коды скопированы в буфер обмена');
  };

  const handleCodeInput = (text: string, index: number) => {
    const newCode = verificationCode.split('');
    newCode[index] = text;
    setVerificationCode(newCode.join(''));

    // Auto-focus next input
    if (text && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек 2FA...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !settings) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error || 'Настройки не найдены'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadTwoFactorSettings}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const renderOverview = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Icon name="security" size={64} color="#E91E63" />
        <Text style={styles.stepTitle}>Двухфакторная аутентификация</Text>
        <Text style={styles.stepDescription}>
          Добавьте дополнительный уровень защиты для вашего аккаунта
        </Text>
      </View>

      {settings.isEnabled ? (
        <View style={styles.enabledContainer}>
          <View style={styles.statusCard}>
            <Icon name="check-circle" size={24} color="#4CAF50" />
            <Text style={styles.statusText}>2FA включена</Text>
          </View>

          <View style={styles.methodInfo}>
            <Text style={styles.methodLabel}>Метод:</Text>
            <Text style={styles.methodValue}>
              {settings.method === 'sms' ? 'SMS' :
               settings.method === 'app' ? 'Приложение-аутентификатор' :
               settings.method === 'email' ? 'Email' : 'Неизвестно'}
            </Text>
          </View>

          {settings.setupDate && (
            <View style={styles.methodInfo}>
              <Text style={styles.methodLabel}>Настроена:</Text>
              <Text style={styles.methodValue}>
                {new Date(settings.setupDate).toLocaleDateString('ru-RU')}
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={styles.showBackupButton}
            onPress={() => setShowBackupCodes(true)}
          >
            <Icon name="backup" size={20} color="#E91E63" />
            <Text style={styles.showBackupButtonText}>Показать резервные коды</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.disableButton}
            onPress={handleDisable2FA}
          >
            <Text style={styles.disableButtonText}>Отключить 2FA</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <View style={styles.disabledContainer}>
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <Icon name="shield" size={20} color="#4CAF50" />
              <Text style={styles.benefitText}>Защита от взлома аккаунта</Text>
            </View>
            <View style={styles.benefitItem}>
              <Icon name="verified-user" size={20} color="#4CAF50" />
              <Text style={styles.benefitText}>Дополнительная проверка при входе</Text>
            </View>
            <View style={styles.benefitItem}>
              <Icon name="backup" size={20} color="#4CAF50" />
              <Text style={styles.benefitText}>Резервные коды для восстановления</Text>
            </View>
          </View>

          <TouchableOpacity
            style={styles.enableButton}
            onPress={handleEnable2FA}
          >
            <Text style={styles.enableButtonText}>Включить 2FA</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderMethodSelection = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Выберите метод</Text>
        <Text style={styles.stepDescription}>
          Как вы хотите получать коды подтверждения?
        </Text>
      </View>

      <View style={styles.methodsList}>
        <TouchableOpacity
          style={[styles.methodCard, selectedMethod === 'app' && styles.methodCardSelected]}
          onPress={() => handleMethodSelect('app')}
        >
          <Icon name="smartphone" size={32} color={selectedMethod === 'app' ? '#E91E63' : '#666666'} />
          <View style={styles.methodContent}>
            <Text style={[styles.methodTitle, selectedMethod === 'app' && styles.methodTitleSelected]}>
              Приложение-аутентификатор
            </Text>
            <Text style={styles.methodDescription}>
              Рекомендуется. Работает без интернета
            </Text>
          </View>
          <View style={styles.recommendedBadge}>
            <Text style={styles.recommendedText}>Рекомендуется</Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.methodCard, selectedMethod === 'sms' && styles.methodCardSelected]}
          onPress={() => handleMethodSelect('sms')}
        >
          <Icon name="sms" size={32} color={selectedMethod === 'sms' ? '#E91E63' : '#666666'} />
          <View style={styles.methodContent}>
            <Text style={[styles.methodTitle, selectedMethod === 'sms' && styles.methodTitleSelected]}>
              SMS
            </Text>
            <Text style={styles.methodDescription}>
              Коды будут приходить на ваш телефон
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.methodCard, selectedMethod === 'email' && styles.methodCardSelected]}
          onPress={() => handleMethodSelect('email')}
        >
          <Icon name="email" size={32} color={selectedMethod === 'email' ? '#E91E63' : '#666666'} />
          <View style={styles.methodContent}>
            <Text style={[styles.methodTitle, selectedMethod === 'email' && styles.methodTitleSelected]}>
              Email
            </Text>
            <Text style={styles.methodDescription}>
              Коды будут приходить на вашу почту
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderSMSSetup = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Настройка SMS</Text>
        <Text style={styles.stepDescription}>
          Введите номер телефона для получения кодов
        </Text>
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Номер телефона</Text>
        <TextInput
          style={styles.phoneInput}
          value={phoneNumber}
          onChangeText={setPhoneNumber}
          placeholder="+7 (999) 123-45-67"
          keyboardType="phone-pad"
          autoComplete="tel"
        />
      </View>

      <TouchableOpacity
        style={styles.continueButton}
        onPress={handleSMSSetup}
        disabled={saving}
      >
        {saving ? (
          <ActivityIndicator size="small" color="#ffffff" />
        ) : (
          <Text style={styles.continueButtonText}>Отправить SMS</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderAppSetup = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Настройка приложения</Text>
        <Text style={styles.stepDescription}>
          Установите приложение-аутентификатор и отсканируйте QR-код
        </Text>
      </View>

      <View style={styles.appsList}>
        {authenticatorApps.map((app) => (
          <TouchableOpacity
            key={app.id}
            style={styles.appCard}
            onPress={() => {/* TODO: Open app store */}}
          >
            <Icon name={app.icon} size={24} color="#E91E63" />
            <Text style={styles.appName}>{app.name}</Text>
            {app.isRecommended && (
              <View style={styles.recommendedBadge}>
                <Text style={styles.recommendedText}>Рекомендуется</Text>
              </View>
            )}
            <Icon name="open-in-new" size={16} color="#666666" />
          </TouchableOpacity>
        ))}
      </View>

      <TouchableOpacity
        style={styles.qrButton}
        onPress={handleAppSetup}
      >
        <Icon name="qr-code" size={20} color="#E91E63" />
        <Text style={styles.qrButtonText}>Показать QR-код</Text>
      </TouchableOpacity>
    </View>
  );

  const renderVerification = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Подтверждение</Text>
        <Text style={styles.stepDescription}>
          Введите 6-значный код из {
            selectedMethod === 'sms' ? 'SMS' :
            selectedMethod === 'app' ? 'приложения-аутентификатора' :
            'email'
          }
        </Text>
      </View>

      <View style={styles.codeInputContainer}>
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <TextInput
            key={index}
            ref={(ref) => {
              if (ref) codeInputRefs.current[index] = ref;
            }}
            style={styles.codeInput}
            value={verificationCode[index] || ''}
            onChangeText={(text) => handleCodeInput(text, index)}
            keyboardType="numeric"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      <TouchableOpacity
        style={styles.verifyButton}
        onPress={handleVerifyCode}
        disabled={saving || verificationCode.length !== 6}
      >
        {saving ? (
          <ActivityIndicator size="small" color="#ffffff" />
        ) : (
          <Text style={styles.verifyButtonText}>Подтвердить</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.resendButton}
        onPress={() => {
          if (selectedMethod === 'sms') {
            handleSMSSetup();
          } else {
            // TODO: Resend email code
          }
        }}
      >
        <Text style={styles.resendButtonText}>Отправить код повторно</Text>
      </TouchableOpacity>
    </View>
  );

  const renderBackupCodes = () => (
    <View style={styles.stepContainer}>
      <View style={styles.stepHeader}>
        <Icon name="backup" size={48} color="#4CAF50" />
        <Text style={styles.stepTitle}>2FA настроена!</Text>
        <Text style={styles.stepDescription}>
          Сохраните эти резервные коды в безопасном месте. Каждый код можно использовать только один раз.
        </Text>
      </View>

      <View style={styles.backupCodesContainer}>
        {backupCodes.map((code, index) => (
          <View key={index} style={styles.backupCodeItem}>
            <Text style={styles.backupCodeText}>{code}</Text>
          </View>
        ))}
      </View>

      <TouchableOpacity
        style={styles.copyButton}
        onPress={copyBackupCodes}
      >
        <Icon name="content-copy" size={20} color="#E91E63" />
        <Text style={styles.copyButtonText}>Скопировать коды</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.finishButton}
        onPress={() => {
          setCurrentStep('overview');
          Alert.alert('Успех', '2FA успешно настроена!');
        }}
      >
        <Text style={styles.finishButtonText}>Завершить</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            if (currentStep === 'overview') {
              navigation.goBack();
            } else {
              setCurrentStep('overview');
            }
          }}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {currentStep === 'overview' ? 'Двухфакторная аутентификация' :
           currentStep === 'method' ? 'Выбор метода' :
           currentStep === 'sms' ? 'Настройка SMS' :
           currentStep === 'app' ? 'Настройка приложения' :
           currentStep === 'verify' ? 'Подтверждение' :
           'Резервные коды'}
        </Text>
        <View style={styles.headerRight}>
          {saving && <ActivityIndicator size="small" color="#E91E63" />}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {currentStep === 'overview' && renderOverview()}
        {currentStep === 'method' && renderMethodSelection()}
        {currentStep === 'sms' && renderSMSSetup()}
        {currentStep === 'app' && renderAppSetup()}
        {currentStep === 'verify' && renderVerification()}
        {currentStep === 'backup' && renderBackupCodes()}
      </ScrollView>

      {/* QR Code Modal */}
      <Modal
        visible={showQRModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowQRModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.qrModalContent}>
            <View style={styles.qrModalHeader}>
              <Text style={styles.qrModalTitle}>Отсканируйте QR-код</Text>
              <TouchableOpacity onPress={() => setShowQRModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <View style={styles.qrCodeContainer}>
              {/* QR Code placeholder - in production, use a QR code library */}
              <View style={styles.qrCodePlaceholder}>
                <Icon name="qr-code" size={120} color="#E91E63" />
              </View>

              <Text style={styles.qrCodeSecret}>
                Секретный ключ: {qrCodeSecret}
              </Text>

              <TouchableOpacity
                style={styles.copySecretButton}
                onPress={() => {
                  Clipboard.setString(qrCodeSecret);
                  Alert.alert('Скопировано', 'Секретный ключ скопирован');
                }}
              >
                <Icon name="content-copy" size={16} color="#E91E63" />
                <Text style={styles.copySecretButtonText}>Скопировать ключ</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.qrInstructions}>
              <Text style={styles.qrInstructionTitle}>Инструкция:</Text>
              <Text style={styles.qrInstructionText}>
                1. Откройте приложение-аутентификатор{'\n'}
                2. Нажмите "Добавить аккаунт" или "+"  {'\n'}
                3. Отсканируйте этот QR-код{'\n'}
                4. Или введите секретный ключ вручную
              </Text>
            </View>

            <TouchableOpacity
              style={styles.qrContinueButton}
              onPress={handleQRCodeScanned}
            >
              <Text style={styles.qrContinueButtonText}>Готово</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Backup Codes Modal */}
      <Modal
        visible={showBackupCodes}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowBackupCodes(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.backupModalContent}>
            <View style={styles.backupModalHeader}>
              <Text style={styles.backupModalTitle}>Резервные коды</Text>
              <TouchableOpacity onPress={() => setShowBackupCodes(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <Text style={styles.backupModalDescription}>
              Используйте эти коды для входа, если у вас нет доступа к основному методу 2FA
            </Text>

            <View style={styles.backupCodesContainer}>
              {settings.backupCodes.map((code, index) => (
                <View key={index} style={styles.backupCodeItem}>
                  <Text style={styles.backupCodeText}>{code}</Text>
                </View>
              ))}
            </View>

            <TouchableOpacity
              style={styles.copyButton}
              onPress={() => {
                const codesText = settings.backupCodes.join('\n');
                Clipboard.setString(codesText);
                Alert.alert('Скопировано', 'Резервные коды скопированы');
              }}
            >
              <Icon name="content-copy" size={20} color="#E91E63" />
              <Text style={styles.copyButtonText}>Скопировать все коды</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
    alignItems: 'flex-end',
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    padding: 20,
  },
  stepHeader: {
    alignItems: 'center',
    marginBottom: 32,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  enabledContainer: {
    alignItems: 'center',
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fff8',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#4CAF50',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
    marginLeft: 8,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  methodLabel: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
  },
  methodValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  showBackupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginTop: 20,
    marginBottom: 12,
  },
  showBackupButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 8,
  },
  disableButton: {
    backgroundColor: '#F44336',
    borderRadius: 12,
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginTop: 8,
  },
  disableButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
  },
  disabledContainer: {
    alignItems: 'center',
  },
  benefitsList: {
    marginBottom: 32,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  benefitText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  enableButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
    width: '100%',
  },
  enableButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    textAlign: 'center',
  },
  methodsList: {
    gap: 16,
  },
  methodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  methodCardSelected: {
    borderColor: '#E91E63',
    backgroundColor: '#E91E63' + '05',
  },
  methodContent: {
    flex: 1,
    marginLeft: 16,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  methodTitleSelected: {
    color: '#E91E63',
  },
  methodDescription: {
    fontSize: 14,
    color: '#666666',
  },
  recommendedBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  recommendedText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  inputContainer: {
    marginBottom: 32,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  phoneInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: '#333333',
  },
  continueButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  appsList: {
    gap: 12,
    marginBottom: 32,
  },
  appCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    position: 'relative',
  },
  appName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  qrButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  qrButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  codeInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  codeInput: {
    width: 45,
    height: 55,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 12,
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
  },
  verifyButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  verifyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  resendButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  resendButtonText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '500',
  },
  backupCodesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 24,
    justifyContent: 'center',
  },
  backupCodeItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    width: '45%',
    alignItems: 'center',
  },
  backupCodeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    fontFamily: 'monospace',
  },
  copyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 16,
    marginBottom: 16,
    gap: 8,
  },
  copyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  finishButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  finishButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qrModalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  qrModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  qrModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  qrCodeContainer: {
    alignItems: 'center',
    padding: 20,
  },
  qrCodePlaceholder: {
    width: 200,
    height: 200,
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  qrCodeSecret: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 12,
    fontFamily: 'monospace',
  },
  copySecretButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  copySecretButtonText: {
    fontSize: 12,
    color: '#E91E63',
    fontWeight: '500',
  },
  qrInstructions: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  qrInstructionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  qrInstructionText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  qrContinueButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginHorizontal: 20,
    marginBottom: 20,
  },
  qrContinueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  backupModalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  backupModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backupModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  backupModalDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    lineHeight: 20,
  },
});

export default TwoFactorSetupScreen;