import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Switch,
  Modal,
  TextInput
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import TouchID from 'react-native-touch-id';

interface SecurityScreenProps {}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  biometricEnabled: boolean;
  passwordLastChanged: string;
  loginNotifications: boolean;
  suspiciousActivityAlerts: boolean;
  deviceManagement: boolean;
  autoLogout: boolean;
  autoLogoutTime: number; // minutes
  encryptMessages: boolean;
  hideOnlineStatus: boolean;
  blockScreenshots: boolean;
}

interface ActiveSession {
  id: string;
  deviceName: string;
  deviceType: string;
  location: string;
  lastActive: string;
  isCurrent: boolean;
  ipAddress: string;
}

const SecurityScreen: React.FC<SecurityScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const [settings, setSettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    biometricEnabled: false,
    passwordLastChanged: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    loginNotifications: true,
    suspiciousActivityAlerts: true,
    deviceManagement: true,
    autoLogout: false,
    autoLogoutTime: 30,
    encryptMessages: true,
    hideOnlineStatus: false,
    blockScreenshots: false
  });

  const [activeSessions, setActiveSessions] = useState<ActiveSession[]>([]);

  useEffect(() => {
    loadSecuritySettings();
    loadActiveSessions();
  }, []);

  const loadSecuritySettings = async () => {
    try {
      setLoading(true);

      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('securitySettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }

      // Check biometric availability
      try {
        const biometryType = await TouchID.isSupported();
        if (biometryType) {
          // Biometric is available
        }
      } catch (error) {
        console.log('Biometric not supported');
      }
    } catch (error) {
      console.error('Error loading security settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadActiveSessions = async () => {
    try {
      // TODO: Implement actual API call
      const mockSessions: ActiveSession[] = [
        {
          id: '1',
          deviceName: 'iPhone 14 Pro',
          deviceType: 'mobile',
          location: 'Москва, Россия',
          lastActive: new Date().toISOString(),
          isCurrent: true,
          ipAddress: '*************'
        },
        {
          id: '2',
          deviceName: 'MacBook Pro',
          deviceType: 'desktop',
          location: 'Москва, Россия',
          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isCurrent: false,
          ipAddress: '*************'
        },
        {
          id: '3',
          deviceName: 'Chrome Browser',
          deviceType: 'web',
          location: 'Санкт-Петербург, Россия',
          lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isCurrent: false,
          ipAddress: '***********'
        }
      ];

      setActiveSessions(mockSessions);
    } catch (error) {
      console.error('Error loading active sessions:', error);
    }
  };

  const saveSettings = async (newSettings: SecuritySettings) => {
    try {
      setSaving(true);

      // Save to AsyncStorage
      await AsyncStorage.setItem('securitySettings', JSON.stringify(newSettings));

      // TODO: Send to API
      // await securityService.updateSettings(newSettings);

      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving security settings:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (key: keyof SecuritySettings, value: boolean | number) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const handleBiometricToggle = async (enabled: boolean) => {
    if (enabled) {
      try {
        await TouchID.authenticate('Подтвердите вход с помощью биометрии', {
          title: 'Биометрическая аутентификация',
          subtitle: 'Используйте отпечаток пальца или Face ID',
          fallbackLabel: 'Использовать пароль',
          cancelLabel: 'Отмена'
        });

        handleToggle('biometricEnabled', true);
      } catch (error) {
        Alert.alert('Ошибка', 'Не удалось настроить биометрическую аутентификацию');
      }
    } else {
      handleToggle('biometricEnabled', false);
    }
  };

  const handleChangePassword = async () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      Alert.alert('Ошибка', 'Заполните все поля');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Ошибка', 'Новые пароли не совпадают');
      return;
    }

    if (newPassword.length < 8) {
      Alert.alert('Ошибка', 'Пароль должен содержать минимум 8 символов');
      return;
    }

    try {
      setSaving(true);

      // TODO: Implement actual API call
      // await authService.changePassword(currentPassword, newPassword);

      const newSettings = {
        ...settings,
        passwordLastChanged: new Date().toISOString()
      };

      await saveSettings(newSettings);

      setShowPasswordModal(false);
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      Alert.alert('Успех', 'Пароль успешно изменен');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить пароль');
    } finally {
      setSaving(false);
    }
  };

  const handleTerminateSession = (sessionId: string) => {
    Alert.alert(
      'Завершить сессию',
      'Вы уверены, что хотите завершить эту сессию?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Завершить',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual API call
              setActiveSessions(prev => prev.filter(session => session.id !== sessionId));
              Alert.alert('Успех', 'Сессия завершена');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось завершить сессию');
            }
          }
        }
      ]
    );
  };

  const handleTerminateAllSessions = () => {
    Alert.alert(
      'Завершить все сессии',
      'Это действие завершит все активные сессии, кроме текущей. Вы уверены?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Завершить все',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual API call
              setActiveSessions(prev => prev.filter(session => session.isCurrent));
              Alert.alert('Успех', 'Все сессии завершены');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось завершить сессии');
            }
          }
        }
      ]
    );
  };

  const formatLastActive = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Сейчас активна';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} мин. назад`;
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч. назад`;
    } else {
      const days = Math.floor(diffInMinutes / (24 * 60));
      return `${days} дн. назад`;
    }
  };

  const getDeviceIcon = (deviceType: string): string => {
    switch (deviceType) {
      case 'mobile':
        return 'smartphone';
      case 'desktop':
        return 'computer';
      case 'web':
        return 'web';
      default:
        return 'device-unknown';
    }
  };

  const renderSettingItem = (
    title: string,
    description: string,
    value: boolean,
    onToggle: (value: boolean) => void,
    icon?: string,
    disabled: boolean = false
  ) => (
    <View style={[styles.settingItem, disabled && styles.settingItemDisabled]}>
      <View style={styles.settingContent}>
        {icon && (
          <Icon
            name={icon}
            size={24}
            color={disabled ? '#cccccc' : '#E91E63'}
            style={styles.settingIcon}
          />
        )}
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, disabled && styles.disabledText]}>
            {title}
          </Text>
          <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        disabled={disabled}
        trackColor={{ false: '#e0e0e0', true: '#E91E63' + '40' }}
        thumbColor={value ? '#E91E63' : '#ffffff'}
      />
    </View>
  );

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Безопасность</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Password Section */}
        {renderSection(
          'Пароль и аутентификация',
          <View>
            <TouchableOpacity
              style={styles.passwordItem}
              onPress={() => setShowPasswordModal(true)}
            >
              <View style={styles.passwordContent}>
                <Icon name="lock" size={24} color="#E91E63" style={styles.settingIcon} />
                <View style={styles.passwordText}>
                  <Text style={styles.settingTitle}>Изменить пароль</Text>
                  <Text style={styles.settingDescription}>
                    Последнее изменение: {new Date(settings.passwordLastChanged).toLocaleDateString('ru-RU')}
                  </Text>
                </View>
              </View>
              <Icon name="chevron-right" size={20} color="#666666" />
            </TouchableOpacity>

            {renderSettingItem(
              'Двухфакторная аутентификация',
              'Дополнительная защита вашего аккаунта',
              settings.twoFactorEnabled,
              (value) => {
                if (value) {
                  navigation.navigate('TwoFactorSetup');
                } else {
                  handleToggle('twoFactorEnabled', value);
                }
              },
              'security'
            )}

            {renderSettingItem(
              'Биометрическая аутентификация',
              'Вход с помощью отпечатка пальца или Face ID',
              settings.biometricEnabled,
              handleBiometricToggle,
              'fingerprint'
            )}
          </View>
        )}

        {/* Security Alerts */}
        {renderSection(
          'Уведомления о безопасности',
          <View>
            {renderSettingItem(
              'Уведомления о входе',
              'Получать уведомления при входе с нового устройства',
              settings.loginNotifications,
              (value) => handleToggle('loginNotifications', value),
              'login'
            )}

            {renderSettingItem(
              'Подозрительная активность',
              'Уведомления о подозрительных действиях в аккаунте',
              settings.suspiciousActivityAlerts,
              (value) => handleToggle('suspiciousActivityAlerts', value),
              'warning'
            )}
          </View>
        )}

        {/* Privacy & Security */}
        {renderSection(
          'Приватность и защита',
          <View>
            {renderSettingItem(
              'Шифрование сообщений',
              'Все сообщения шифруются end-to-end',
              settings.encryptMessages,
              (value) => handleToggle('encryptMessages', value),
              'enhanced-encryption'
            )}

            {renderSettingItem(
              'Скрыть статус онлайн',
              'Другие пользователи не увидят, когда вы в сети',
              settings.hideOnlineStatus,
              (value) => handleToggle('hideOnlineStatus', value),
              'visibility-off'
            )}

            {renderSettingItem(
              'Блокировка скриншотов',
              'Запретить создание скриншотов в приложении',
              settings.blockScreenshots,
              (value) => handleToggle('blockScreenshots', value),
              'screenshot'
            )}
          </View>
        )}

        {/* Auto Logout */}
        {renderSection(
          'Автоматический выход',
          <View>
            {renderSettingItem(
              'Автоматический выход',
              `Выходить из аккаунта через ${settings.autoLogoutTime} минут неактивности`,
              settings.autoLogout,
              (value) => handleToggle('autoLogout', value),
              'logout'
            )}

            {settings.autoLogout && (
              <View style={styles.timeoutSelector}>
                <Text style={styles.timeoutLabel}>Время до выхода:</Text>
                <View style={styles.timeoutButtons}>
                  {[15, 30, 60, 120].map((minutes) => (
                    <TouchableOpacity
                      key={minutes}
                      style={[
                        styles.timeoutButton,
                        settings.autoLogoutTime === minutes && styles.timeoutButtonActive
                      ]}
                      onPress={() => handleToggle('autoLogoutTime', minutes)}
                    >
                      <Text
                        style={[
                          styles.timeoutButtonText,
                          settings.autoLogoutTime === minutes && styles.timeoutButtonTextActive
                        ]}
                      >
                        {minutes} мин
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}
          </View>
        )}

        {/* Active Sessions */}
        {renderSection(
          'Активные сессии',
          <View>
            <View style={styles.sessionsHeader}>
              <Text style={styles.sessionsTitle}>
                Устройства ({activeSessions.length})
              </Text>
              {activeSessions.length > 1 && (
                <TouchableOpacity
                  style={styles.terminateAllButton}
                  onPress={handleTerminateAllSessions}
                >
                  <Text style={styles.terminateAllButtonText}>Завершить все</Text>
                </TouchableOpacity>
              )}
            </View>

            {activeSessions.map((session) => (
              <View key={session.id} style={styles.sessionItem}>
                <View style={styles.sessionIcon}>
                  <Icon
                    name={getDeviceIcon(session.deviceType)}
                    size={24}
                    color="#E91E63"
                  />
                </View>

                <View style={styles.sessionDetails}>
                  <View style={styles.sessionHeader}>
                    <Text style={styles.sessionName}>{session.deviceName}</Text>
                    {session.isCurrent && (
                      <View style={styles.currentBadge}>
                        <Text style={styles.currentBadgeText}>Текущая</Text>
                      </View>
                    )}
                  </View>

                  <Text style={styles.sessionLocation}>{session.location}</Text>
                  <Text style={styles.sessionLastActive}>
                    {formatLastActive(session.lastActive)}
                  </Text>
                  <Text style={styles.sessionIp}>IP: {session.ipAddress}</Text>
                </View>

                {!session.isCurrent && (
                  <TouchableOpacity
                    style={styles.terminateButton}
                    onPress={() => handleTerminateSession(session.id)}
                  >
                    <Icon name="close" size={20} color="#F44336" />
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Change Password Modal */}
      <Modal
        visible={showPasswordModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPasswordModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Изменить пароль</Text>
              <TouchableOpacity onPress={() => setShowPasswordModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Текущий пароль"
                value={currentPassword}
                onChangeText={setCurrentPassword}
                secureTextEntry
              />

              <TextInput
                style={styles.passwordInput}
                placeholder="Новый пароль"
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry
              />

              <TextInput
                style={styles.passwordInput}
                placeholder="Подтвердите новый пароль"
                value={confirmPassword}
                onChangeText={setConfirmPassword}
                secureTextEntry
              />

              <Text style={styles.passwordHint}>
                Пароль должен содержать минимум 8 символов
              </Text>
            </View>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowPasswordModal(false)}
              >
                <Text style={styles.modalCancelButtonText}>Отмена</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalSaveButton}
                onPress={handleChangePassword}
                disabled={saving}
              >
                {saving ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text style={styles.modalSaveButtonText}>Сохранить</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {saving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="small" color="#E91E63" />
          <Text style={styles.savingText}>Сохранение...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItemDisabled: {
    opacity: 0.5,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  disabledText: {
    color: '#cccccc',
  },
  passwordItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  passwordContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  passwordText: {
    flex: 1,
  },
  timeoutSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f8f8',
  },
  timeoutLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 12,
  },
  timeoutButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  timeoutButton: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingVertical: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  timeoutButtonActive: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  timeoutButtonText: {
    fontSize: 14,
    color: '#666666',
  },
  timeoutButtonTextActive: {
    color: '#ffffff',
    fontWeight: '600',
  },
  sessionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sessionsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  terminateAllButton: {
    backgroundColor: '#F44336' + '10',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  terminateAllButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#F44336',
  },
  sessionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sessionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sessionDetails: {
    flex: 1,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  sessionName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginRight: 8,
  },
  currentBadge: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  currentBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  sessionLocation: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  sessionLastActive: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 2,
  },
  sessionIp: {
    fontSize: 12,
    color: '#999999',
  },
  terminateButton: {
    backgroundColor: '#F44336' + '10',
    borderRadius: 20,
    padding: 8,
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: '90%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  modalBody: {
    padding: 20,
  },
  passwordInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  passwordHint: {
    fontSize: 12,
    color: '#666666',
    marginTop: -8,
  },
  modalActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  modalSaveButton: {
    flex: 1,
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  modalSaveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  savingText: {
    fontSize: 16,
    color: '#E91E63',
    marginLeft: 8,
  },
});

export default SecurityScreen;