import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  RefreshControl
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ChatsListScreenProps {}

interface ChatItem {
  id: string;
  userId: string;
  userName: string;
  userAvatar: string;
  lastMessage: {
    id: string;
    text: string;
    timestamp: string;
    isRead: boolean;
    senderId: string;
  };
  unreadCount: number;
  isOnline: boolean;
  isTyping: boolean;
  isPinned: boolean;
  isBlocked: boolean;
}

const ChatsListScreen: React.FC<ChatsListScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [chats, setChats] = useState<ChatItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadChats();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      loadChats();
    }, [])
  );

  const loadChats = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockChats: ChatItem[] = [
        {
          id: '1',
          userId: 'user1',
          userName: 'Анна',
          userAvatar: 'https://example.com/avatar1.jpg',
          lastMessage: {
            id: 'msg1',
            text: 'Привет! Как дела?',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            isRead: false,
            senderId: 'user1'
          },
          unreadCount: 2,
          isOnline: true,
          isTyping: false,
          isPinned: true,
          isBlocked: false
        },
        {
          id: '2',
          userId: 'user2',
          userName: 'Мария',
          userAvatar: 'https://example.com/avatar2.jpg',
          lastMessage: {
            id: 'msg2',
            text: 'Спасибо за приятный вечер!',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            isRead: true,
            senderId: 'current_user'
          },
          unreadCount: 0,
          isOnline: false,
          isTyping: false,
          isPinned: false,
          isBlocked: false
        },
        {
          id: '3',
          userId: 'user3',
          userName: 'Елена',
          userAvatar: 'https://example.com/avatar3.jpg',
          lastMessage: {
            id: 'msg3',
            text: 'Увидимся завтра в кафе?',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            isRead: false,
            senderId: 'user3'
          },
          unreadCount: 1,
          isOnline: true,
          isTyping: true,
          isPinned: false,
          isBlocked: false
        }
      ];

      setChats(mockChats);
    } catch (err: any) {
      setError('Ошибка загрузки чатов');
      console.error('Chats loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadChats();
    setRefreshing(false);
  };

  const handleChatPress = (chat: ChatItem) => {
    if (chat.isBlocked) {
      Alert.alert('Пользователь заблокирован', 'Вы не можете отправлять сообщения заблокированному пользователю');
      return;
    }

    navigation.navigate('Chat', {
      chatId: chat.id,
      userId: chat.userId,
      userName: chat.userName,
      userAvatar: chat.userAvatar
    });
  };

  const handleChatLongPress = (chat: ChatItem) => {
    Alert.alert(
      chat.userName,
      'Выберите действие',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: chat.isPinned ? 'Открепить' : 'Закрепить',
          onPress: () => togglePinChat(chat.id)
        },
        {
          text: 'Удалить чат',
          style: 'destructive',
          onPress: () => deleteChat(chat.id)
        },
        {
          text: chat.isBlocked ? 'Разблокировать' : 'Заблокировать',
          style: 'destructive',
          onPress: () => toggleBlockUser(chat.userId)
        }
      ]
    );
  };

  const togglePinChat = async (chatId: string) => {
    try {
      // TODO: Implement actual API call
      setChats(prev => prev.map(chat =>
        chat.id === chatId ? { ...chat, isPinned: !chat.isPinned } : chat
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить статус закрепления');
    }
  };

  const deleteChat = async (chatId: string) => {
    try {
      // TODO: Implement actual API call
      setChats(prev => prev.filter(chat => chat.id !== chatId));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось удалить чат');
    }
  };

  const toggleBlockUser = async (userId: string) => {
    try {
      // TODO: Implement actual API call
      setChats(prev => prev.map(chat =>
        chat.userId === userId ? { ...chat, isBlocked: !chat.isBlocked } : chat
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить статус блокировки');
    }
  };

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} мин`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} ч`;
    } else {
      return date.toLocaleDateString('ru-RU', { day: 'numeric', month: 'short' });
    }
  };

  const filteredChats = chats.filter(chat =>
    chat.userName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage.text.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedChats = filteredChats.sort((a, b) => {
    // Сначала закрепленные
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;

    // Затем по времени последнего сообщения
    return new Date(b.lastMessage.timestamp).getTime() - new Date(a.lastMessage.timestamp).getTime();
  });

  const renderChatItem = ({ item }: { item: ChatItem }) => (
    <TouchableOpacity
      style={[styles.chatItem, item.isPinned && styles.pinnedChatItem]}
      onPress={() => handleChatPress(item)}
      onLongPress={() => handleChatLongPress(item)}
    >
      <View style={styles.avatarContainer}>
        <View style={styles.avatarPlaceholder}>
          <Icon name="person" size={32} color="#E91E63" />
        </View>
        {item.isOnline && <View style={styles.onlineIndicator} />}
        {item.isPinned && (
          <View style={styles.pinIndicator}>
            <Icon name="push-pin" size={12} color="#E91E63" />
          </View>
        )}
      </View>

      <View style={styles.chatContent}>
        <View style={styles.chatHeader}>
          <Text style={styles.userName} numberOfLines={1}>
            {item.userName}
          </Text>
          <View style={styles.chatMeta}>
            <Text style={styles.timestamp}>
              {formatTimestamp(item.lastMessage.timestamp)}
            </Text>
            {item.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>
                  {item.unreadCount > 99 ? '99+' : item.unreadCount}
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.messageRow}>
          {item.isTyping ? (
            <Text style={styles.typingIndicator}>печатает...</Text>
          ) : (
            <Text
              style={[
                styles.lastMessage,
                !item.lastMessage.isRead && item.lastMessage.senderId !== 'current_user' && styles.unreadMessage
              ]}
              numberOfLines={1}
            >
              {item.lastMessage.senderId === 'current_user' && 'Вы: '}
              {item.lastMessage.text}
            </Text>
          )}

          {item.lastMessage.senderId === 'current_user' && (
            <Icon
              name={item.lastMessage.isRead ? "done-all" : "done"}
              size={16}
              color={item.lastMessage.isRead ? "#4CAF50" : "#666666"}
              style={styles.messageStatus}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="chat-bubble-outline" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Нет сообщений</Text>
      <Text style={styles.emptyDescription}>
        Начните общение с людьми, которые вам понравились
      </Text>
      <TouchableOpacity
        style={styles.exploreButton}
        onPress={() => navigation.navigate('Discover')}
      >
        <Text style={styles.exploreButtonText}>Найти людей</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка чатов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Сообщения</Text>
        <TouchableOpacity
          style={styles.searchButton}
          onPress={() => navigation.navigate('SearchChats')}
        >
          <Icon name="search" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск сообщений..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Chats List */}
      <FlatList
        data={sortedChats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  searchButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
    marginLeft: 12,
  },
  listContainer: {
    paddingBottom: 20,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  pinnedChatItem: {
    backgroundColor: '#E91E63' + '05',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  pinIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  chatMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#666666',
  },
  unreadBadge: {
    backgroundColor: '#E91E63',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadCount: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  lastMessage: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  unreadMessage: {
    fontWeight: '600',
    color: '#333333',
  },
  typingIndicator: {
    fontSize: 14,
    color: '#E91E63',
    fontStyle: 'italic',
    flex: 1,
  },
  messageStatus: {
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  exploreButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  exploreButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default ChatsListScreen;