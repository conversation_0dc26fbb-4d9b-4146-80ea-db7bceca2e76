import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Business as BusinessIcon,
  Restaurant as RestaurantIcon,
  LocalCafe as CafeIcon,
  LocalBar as BarIcon,
  CheckCircle as CheckIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout/Layout';
import { partnerService } from '../../services/partnerService';

interface PartnerApplicationForm {
  // Business Info
  businessName: string;
  businessType: string;
  businessDescription: string;
  businessAddress: string;
  businessPhone: string;
  businessEmail: string;
  businessWebsite?: string;

  // Contact Person
  contactName: string;
  contactPosition: string;
  contactPhone: string;
  contactEmail: string;

  // Business Details
  establishedYear: number;
  numberOfLocations: number;
  averageCapacity: number;
  priceRange: string;

  // Agreement
  agreeToTerms: boolean;
  agreeToCommission: boolean;
}

const validationSchema = yup.object({
  businessName: yup.string().required('Название заведения обязательно'),
  businessType: yup.string().required('Тип заведения обязателен'),
  businessDescription: yup.string().required('Описание обязательно').min(50, 'Минимум 50 символов'),
  businessAddress: yup.string().required('Адрес обязателен'),
  businessPhone: yup.string().required('Телефон обязателен'),
  businessEmail: yup.string().email('Некорректный email').required('Email обязателен'),
  contactName: yup.string().required('Имя контактного лица обязательно'),
  contactPosition: yup.string().required('Должность обязательна'),
  contactPhone: yup.string().required('Телефон контактного лица обязателен'),
  contactEmail: yup.string().email('Некорректный email').required('Email контактного лица обязателен'),
  establishedYear: yup.number().required('Год основания обязателен').min(1900, 'Некорректный год'),
  numberOfLocations: yup.number().required('Количество локаций обязательно').min(1, 'Минимум 1 локация'),
  averageCapacity: yup.number().required('Вместимость обязательна').min(1, 'Минимум 1 место'),
  priceRange: yup.string().required('Ценовая категория обязательна'),
  agreeToTerms: yup.boolean().oneOf([true], 'Необходимо согласиться с условиями'),
  agreeToCommission: yup.boolean().oneOf([true], 'Необходимо согласиться с комиссией')
});

const steps = ['Информация о заведении', 'Контактные данные', 'Детали бизнеса', 'Соглашение'];

const PartnerApplicationPage: React.FC = () => {
  const router = useRouter();
  const [activeStep, setActiveStep] = useState(0);
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    trigger,
    watch
  } = useForm<PartnerApplicationForm>({
    resolver: yupResolver(validationSchema),
    mode: 'onChange'
  });

  const businessTypes = [
    { value: 'restaurant', label: 'Ресторан', icon: RestaurantIcon },
    { value: 'cafe', label: 'Кафе', icon: CafeIcon },
    { value: 'bar', label: 'Бар/Паб', icon: BarIcon },
    { value: 'other', label: 'Другое', icon: BusinessIcon }
  ];

  const priceRanges = [
    { value: 'budget', label: '₽ - Бюджетно (до 1000₽)' },
    { value: 'moderate', label: '₽₽ - Умеренно (1000-3000₽)' },
    { value: 'expensive', label: '₽₽₽ - Дорого (3000-5000₽)' },
    { value: 'luxury', label: '₽₽₽₽ - Люкс (от 5000₽)' }
  ];

  const benefits = [
    'Доступ к активной аудитории пользователей',
    'Увеличение количества бронирований',
    'Аналитика и отчеты по посещаемости',
    'Продвижение в приложении',
    'Техническая поддержка 24/7',
    'Гибкие условия сотрудничества'
  ];

  const handleNext = async () => {
    const fieldsToValidate = getFieldsForStep(activeStep);
    const isStepValid = await trigger(fieldsToValidate);

    if (isStepValid) {
      setActiveStep((prevStep) => prevStep + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const getFieldsForStep = (step: number): (keyof PartnerApplicationForm)[] => {
    switch (step) {
      case 0:
        return ['businessName', 'businessType', 'businessDescription', 'businessAddress', 'businessPhone', 'businessEmail'];
      case 1:
        return ['contactName', 'contactPosition', 'contactPhone', 'contactEmail'];
      case 2:
        return ['establishedYear', 'numberOfLocations', 'averageCapacity', 'priceRange'];
      case 3:
        return ['agreeToTerms', 'agreeToCommission'];
      default:
        return [];
    }
  };

  const onSubmit = async (data: PartnerApplicationForm) => {
    try {
      setSubmitting(true);
      setSubmitError(null);

      await partnerService.submitApplication(data);

      router.push('/partner/application-success');
    } catch (error: any) {
      setSubmitError(error.message || 'Ошибка при отправке заявки');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Controller
                name="businessName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Название заведения"
                    fullWidth
                    error={!!errors.businessName}
                    helperText={errors.businessName?.message}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControl component="fieldset" error={!!errors.businessType}>
                <FormLabel component="legend">Тип заведения *</FormLabel>
                <Controller
                  name="businessType"
                  control={control}
                  render={({ field }) => (
                    <RadioGroup {...field} row>
                      {businessTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <FormControlLabel
                            key={type.value}
                            value={type.value}
                            control={<Radio />}
                            label={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <IconComponent fontSize="small" />
                                {type.label}
                              </Box>
                            }
                          />
                        );
                      })}
                    </RadioGroup>
                  )}
                />
                {errors.businessType && (
                  <Typography variant="caption" color="error">
                    {errors.businessType.message}
                  </Typography>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="businessDescription"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Описание заведения"
                    fullWidth
                    multiline
                    rows={4}
                    error={!!errors.businessDescription}
                    helperText={errors.businessDescription?.message || 'Расскажите о вашем заведении, атмосфере, кухне'}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="businessAddress"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Адрес заведения"
                    fullWidth
                    error={!!errors.businessAddress}
                    helperText={errors.businessAddress?.message}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="businessPhone"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Телефон заведения"
                    fullWidth
                    error={!!errors.businessPhone}
                    helperText={errors.businessPhone?.message}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Controller
                name="businessEmail"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Email заведения"
                    type="email"
                    fullWidth
                    error={!!errors.businessEmail}
                    helperText={errors.businessEmail?.message}
                    required
                  />
                )}
              />
            </Grid>

            <Grid item xs={12}>
              <Controller
                name="businessWebsite"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Веб-сайт (необязательно)"
                    fullWidth
                    placeholder="https://example.com"
                  />
                )}
              />
            </Grid>
          </Grid>
        );