import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Skeleton
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  AttachMoney as AttachMoneyIcon,
  Event as EventIcon,
  Star as StarIcon,
  Visibility as VisibilityIcon,
  Restaurant as RestaurantIcon,
  MoreVert as MoreVertIcon,
  Add as AddIcon,
  Analytics as AnalyticsIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, Pie, Cell, BarChart, Bar } from 'recharts';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { partnerService } from '../../services/partnerService';

interface DashboardPageProps {}

interface DashboardStats {
  totalRevenue: number;
  revenueChange: number;
  totalVisitors: number;
  visitorsChange: number;
  totalBookings: number;
  bookingsChange: number;
  averageRating: number;
  ratingChange: number;
  activeVenues: number;
  totalEvents: number;
}

interface RecentBooking {
  id: string;
  customerName: string;
  venueName: string;
  date: string;
  time: string;
  guests: number;
  status: 'confirmed' | 'pending' | 'cancelled';
  amount: number;
}

interface PopularVenue {
  id: string;
  name: string;
  category: string;
  bookings: number;
  revenue: number;
  rating: number;
  image: string;
}

interface RevenueData {
  date: string;
  revenue: number;
  bookings: number;
}

interface VisitorData {
  date: string;
  visitors: number;
  views: number;
}

const DashboardPage: React.FC<DashboardPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentBookings, setRecentBookings] = useState<RecentBooking[]>([]);
  const [popularVenues, setPopularVenues] = useState<PopularVenue[]>([]);
  const [revenueData, setRevenueData] = useState<RevenueData[]>([]);
  const [visitorData, setVisitorData] = useState<VisitorData[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/partner/dashboard');
      return;
    }

    if (user?.role !== 'partner') {
      router.push('/partner/apply');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, user, router, selectedPeriod]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // TODO: Implement actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock data
      const mockStats: DashboardStats = {
        totalRevenue: 125000,
        revenueChange: 12.5,
        totalVisitors: 2847,
        visitorsChange: -3.2,
        totalBookings: 156,
        bookingsChange: 8.7,
        averageRating: 4.6,
        ratingChange: 0.2,
        activeVenues: 3,
        totalEvents: 12
      };

      const mockRecentBookings: RecentBooking[] = [
        {
          id: '1',
          customerName: 'Анна Петрова',
          venueName: 'Кафе "Встреча"',
          date: new Date().toISOString(),
          time: '19:00',
          guests: 2,
          status: 'confirmed',
          amount: 3500
        },
        {
          id: '2',
          customerName: 'Михаил Сидоров',
          venueName: 'Ресторан "Романтика"',
          date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          time: '20:30',
          guests: 4,
          status: 'pending',
          amount: 8000
        },
        {
          id: '3',
          customerName: 'Елена Козлова',
          venueName: 'Кафе "Встреча"',
          date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          time: '18:00',
          guests: 2,
          status: 'confirmed',
          amount: 2800
        }
      ];

      const mockPopularVenues: PopularVenue[] = [
        {
          id: '1',
          name: 'Кафе "Встреча"',
          category: 'Кафе',
          bookings: 89,
          revenue: 67500,
          rating: 4.7,
          image: '/images/venues/cafe1.jpg'
        },
        {
          id: '2',
          name: 'Ресторан "Романтика"',
          category: 'Ресторан',
          bookings: 45,
          revenue: 45000,
          rating: 4.8,
          image: '/images/venues/restaurant1.jpg'
        },
        {
          id: '3',
          name: 'Лаунж "Атмосфера"',
          category: 'Лаунж',
          bookings: 22,
          revenue: 12500,
          rating: 4.3,
          image: '/images/venues/lounge1.jpg'
        }
      ];

      // Generate mock chart data
      const mockRevenueData: RevenueData[] = [];
      const mockVisitorData: VisitorData[] = [];

      for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);

        mockRevenueData.push({
          date: date.toISOString().split('T')[0],
          revenue: Math.floor(Math.random() * 5000) + 2000,
          bookings: Math.floor(Math.random() * 10) + 3
        });

        mockVisitorData.push({
          date: date.toISOString().split('T')[0],
          visitors: Math.floor(Math.random() * 100) + 50,
          views: Math.floor(Math.random() * 300) + 150
        });
      }

      setStats(mockStats);
      setRecentBookings(mockRecentBookings);
      setPopularVenues(mockPopularVenues);
      setRevenueData(mockRevenueData);
      setVisitorData(mockVisitorData);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePeriodChange = (period: '7d' | '30d' | '90d') => {
    setSelectedPeriod(period);
    setAnchorEl(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Подтверждено';
      case 'pending':
        return 'Ожидает';
      case 'cancelled':
        return 'Отменено';
      default:
        return status;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'short'
    });
  };

  const formatDateTime = (dateString: string, time: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    let dateLabel = '';
    if (date.toDateString() === today.toDateString()) {
      dateLabel = 'Сегодня';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      dateLabel = 'Завтра';
    } else {
      dateLabel = date.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'short'
      });
    }

    return `${dateLabel} в ${time}`;
  };

  const renderStatCard = (
    title: string,
    value: string | number,
    change: number,
    icon: React.ReactNode,
    color: string = 'primary'
  ) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div">
              {value}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {change > 0 ? (
                <TrendingUpIcon sx={{ color: 'success.main', mr: 0.5 }} fontSize="small" />
              ) : (
                <TrendingDownIcon sx={{ color: 'error.main', mr: 0.5 }} fontSize="small" />
              )}
              <Typography
                variant="body2"
                sx={{
                  color: change > 0 ? 'success.main' : 'error.main',
                  fontWeight: 'medium'
                }}
              >
                {Math.abs(change)}%
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                за месяц
              </Typography>
            </Box>
          </Box>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Layout>
      <Head>
        <title>Дашборд партнера - LikesLove</title>
        <meta name="description" content="Панель управления партнера LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Добро пожаловать, {user?.name || 'Партнер'}!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Управляйте своими заведениями и отслеживайте статистику
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<AnalyticsIcon />}
              onClick={() => router.push('/partner/analytics')}
            >
              Аналитика
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => router.push('/partner/venues/create')}
            >
              Добавить заведение
            </Button>
          </Box>
        </Box>

        {loading ? (
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} sm={6} md={3} key={item}>
                <Card>
                  <CardContent>
                    <Skeleton variant="text" width="60%" />
                    <Skeleton variant="text" width="40%" height={40} />
                    <Skeleton variant="text" width="80%" />
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          <>
            {/* Stats Cards */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                {renderStatCard(
                  'Общий доход',
                  formatCurrency(stats?.totalRevenue || 0),
                  stats?.revenueChange || 0,
                  <AttachMoneyIcon />,
                  'success'
                )}
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                {renderStatCard(
                  'Посетители',
                  stats?.totalVisitors || 0,
                  stats?.visitorsChange || 0,
                  <PeopleIcon />,
                  'info'
                )}
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                {renderStatCard(
                  'Бронирования',
                  stats?.totalBookings || 0,
                  stats?.bookingsChange || 0,
                  <EventIcon />,
                  'warning'
                )}
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                {renderStatCard(
                  'Средний рейтинг',
                  stats?.averageRating?.toFixed(1) || '0.0',
                  stats?.ratingChange || 0,
                  <StarIcon />,
                  'secondary'
                )}
              </Grid>
            </Grid>

            {/* Charts */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {/* Revenue Chart */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader
                    title="Доходы"
                    action={
                      <Box>
                        <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
                          <MoreVertIcon />
                        </IconButton>
                        <Menu
                          anchorEl={anchorEl}
                          open={Boolean(anchorEl)}
                          onClose={() => setAnchorEl(null)}
                        >
                          <MenuItem onClick={() => handlePeriodChange('7d')}>
                            Последние 7 дней
                          </MenuItem>
                          <MenuItem onClick={() => handlePeriodChange('30d')}>
                            Последние 30 дней
                          </MenuItem>
                          <MenuItem onClick={() => handlePeriodChange('90d')}>
                            Последние 90 дней
                          </MenuItem>
                        </Menu>
                      </Box>
                    }
                  />
                  <CardContent>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart data={revenueData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="date"
                            tickFormatter={(value) => formatDate(value)}
                          />
                          <YAxis tickFormatter={(value) => formatCurrency(value)} />
                          <Tooltip
                            labelFormatter={(value) => formatDate(value)}
                            formatter={(value: number) => [formatCurrency(value), 'Доход']}
                          />
                          <Area
                            type="monotone"
                            dataKey="revenue"
                            stroke="#1976d2"
                            fill="#1976d2"
                            fillOpacity={0.1}
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Visitors Chart */}
              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader title="Посетители" />
                  <CardContent>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={visitorData}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="date"
                            tickFormatter={(value) => formatDate(value)}
                          />
                          <YAxis />
                          <Tooltip
                            labelFormatter={(value) => formatDate(value)}
                          />
                          <Line
                            type="monotone"
                            dataKey="visitors"
                            stroke="#f57c00"
                            strokeWidth={2}
                            dot={{ fill: '#f57c00' }}
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Recent Bookings & Popular Venues */}
            <Grid container spacing={3}>
              {/* Recent Bookings */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardHeader
                    title="Последние бронирования"
                    action={
                      <Button
                        size="small"
                        onClick={() => router.push('/partner/bookings')}
                      >
                        Все бронирования
                      </Button>
                    }
                  />
                  <CardContent sx={{ p: 0 }}>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Клиент</TableCell>
                            <TableCell>Заведение</TableCell>
                            <TableCell>Дата и время</TableCell>
                            <TableCell>Гости</TableCell>
                            <TableCell>Статус</TableCell>
                            <TableCell align="right">Сумма</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {recentBookings.map((booking) => (
                            <TableRow key={booking.id}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                                    {booking.customerName.charAt(0)}
                                  </Avatar>
                                  {booking.customerName}
                                </Box>
                              </TableCell>
                              <TableCell>{booking.venueName}</TableCell>
                              <TableCell>
                                {formatDateTime(booking.date, booking.time)}
                              </TableCell>
                              <TableCell>{booking.guests}</TableCell>
                              <TableCell>
                                <Chip
                                  label={getStatusLabel(booking.status)}
                                  color={getStatusColor(booking.status) as any}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell align="right">
                                {formatCurrency(booking.amount)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>

              {/* Popular Venues */}
              <Grid item xs={12} md={4}>
                <Card>
                  <CardHeader
                    title="Популярные заведения"
                    action={
                      <Button
                        size="small"
                        onClick={() => router.push('/partner/venues')}
                      >
                        Все заведения
                      </Button>
                    }
                  />
                  <CardContent>
                    {popularVenues.map((venue, index) => (
                      <Box
                        key={venue.id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mb: index < popularVenues.length - 1 ? 2 : 0
                        }}
                      >
                        <Avatar
                          sx={{ mr: 2, bgcolor: 'primary.main' }}
                          src={venue.image}
                        >
                          <RestaurantIcon />
                        </Avatar>

                        <Box sx={{ flex: 1 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            {venue.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {venue.category} • {venue.bookings} бронирований
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <StarIcon sx={{ color: 'warning.main', fontSize: 16, mr: 0.5 }} />
                            <Typography variant="body2">
                              {venue.rating}
                            </Typography>
                          </Box>
                        </Box>

                        <Box sx={{ textAlign: 'right' }}>
                          <Typography variant="subtitle2" color="success.main">
                            {formatCurrency(venue.revenue)}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </>
        )}
      </Container>
    </Layout>
  );
};

export default DashboardPage;