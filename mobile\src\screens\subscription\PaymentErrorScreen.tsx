import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Animated,
  Linking
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface PaymentErrorScreenProps {}

interface RouteParams {
  errorCode?: string;
  errorMessage?: string;
  subscriptionType?: string;
  amount?: string;
}

const PaymentErrorScreen: React.FC<PaymentErrorScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {
    errorCode = 'PAYMENT_FAILED',
    errorMessage = 'Произошла ошибка при обработке платежа',
    subscriptionType = 'Premium',
    amount = '999'
  } = (route.params as RouteParams) || {};

  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleTryAgain = () => {
    navigation.goBack();
  };

  const handleContactSupport = () => {
    const email = '<EMAIL>';
    const subject = `Ошибка платежа: ${errorCode}`;
    const body = `Здравствуйте! У меня возникла проблема с оплатой подписки ${subscriptionType} на сумму ${amount} ₽.\n\nКод ошибки: ${errorCode}\nСообщение: ${errorMessage}\n\nПожалуйста, помогите решить эту проблему.`;

    Linking.openURL(`mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handleGoHome = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'MainTabs' }],
    });
  };

  const getErrorDetails = () => {
    switch (errorCode) {
      case 'INSUFFICIENT_FUNDS':
        return {
          title: 'Недостаточно средств',
          description: 'На вашей карте недостаточно средств для совершения платежа',
          suggestions: [
            'Проверьте баланс карты',
            'Попробуйте другую карту',
            'Обратитесь в банк'
          ]
        };
      case 'CARD_DECLINED':
        return {
          title: 'Карта отклонена',
          description: 'Ваш банк отклонил транзакцию',
          suggestions: [
            'Проверьте данные карты',
            'Убедитесь, что карта активна',
            'Обратитесь в банк'
          ]
        };
      case 'NETWORK_ERROR':
        return {
          title: 'Ошибка сети',
          description: 'Проблема с подключением к интернету',
          suggestions: [
            'Проверьте интернет-соединение',
            'Попробуйте позже',
            'Перезапустите приложение'
          ]
        };
      default:
        return {
          title: 'Ошибка платежа',
          description: errorMessage,
          suggestions: [
            'Попробуйте еще раз',
            'Проверьте данные карты',
            'Обратитесь в поддержку'
          ]
        };
    }
  };

  const errorDetails = getErrorDetails();

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#ff4444" />

      <View style={styles.content}>
        {/* Error Animation */}
        <Animated.View
          style={[
            styles.animationContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={styles.errorIcon}>
            <Icon name="error" size={60} color="#ffffff" />
          </View>
        </Animated.View>

        {/* Error Message */}
        <Animated.View
          style={[
            styles.messageContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Text style={styles.errorTitle}>{errorDetails.title}</Text>
          <Text style={styles.errorDescription}>
            {errorDetails.description}
          </Text>
        </Animated.View>

        {/* Error Details */}
        <Animated.View
          style={[
            styles.detailsContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={styles.detailsCard}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Подписка:</Text>
              <Text style={styles.detailValue}>{subscriptionType}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Сумма:</Text>
              <Text style={styles.detailValue}>{amount} ₽</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Код ошибки:</Text>
              <Text style={styles.detailValue}>{errorCode}</Text>
            </View>
          </View>
        </Animated.View>

        {/* Suggestions */}
        <Animated.View
          style={[
            styles.suggestionsContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Text style={styles.suggestionsTitle}>Что можно попробовать:</Text>
          {errorDetails.suggestions.map((suggestion, index) => (
            <View key={index} style={styles.suggestionItem}>
              <Icon name="lightbulb-outline" size={20} color="#ffffff" />
              <Text style={styles.suggestionText}>{suggestion}</Text>
            </View>
          ))}
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View
          style={[
            styles.buttonsContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <TouchableOpacity style={styles.primaryButton} onPress={handleTryAgain}>
            <Text style={styles.primaryButtonText}>Попробовать снова</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={handleContactSupport}>
            <Icon name="support-agent" size={20} color="#ffffff" />
            <Text style={styles.secondaryButtonText}>Связаться с поддержкой</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.tertiaryButton} onPress={handleGoHome}>
            <Text style={styles.tertiaryButtonText}>Вернуться на главную</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ff4444',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  animationContainer: {
    marginBottom: 40,
  },
  errorIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#ffffff',
  },
  messageContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  errorTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 12,
    textAlign: 'center',
  },
  errorDescription: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
  },
  detailsContainer: {
    width: '100%',
    marginBottom: 30,
  },
  detailsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  suggestionsContainer: {
    width: '100%',
    marginBottom: 40,
  },
  suggestionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 16,
    textAlign: 'center',
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  suggestionText: {
    fontSize: 14,
    color: '#ffffff',
    marginLeft: 12,
    flex: 1,
  },
  buttonsContainer: {
    width: '100%',
  },
  primaryButton: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ff4444',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 8,
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  tertiaryButtonText: {
    fontSize: 16,
    color: '#ffffff',
    opacity: 0.8,
  },
});

export default PaymentErrorScreen;