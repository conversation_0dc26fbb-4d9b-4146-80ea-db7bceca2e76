import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Image,
  RefreshControl
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface SelectRecipientScreenProps {}

interface RouteParams {
  onSelect: (recipient: Recipient) => void;
}

interface Recipient {
  id: string;
  name: string;
  avatar?: string;
  age: number;
  isOnline: boolean;
  lastSeen?: string;
  mutualFriends?: number;
  isMatch: boolean;
  hasConversation: boolean;
}

const SelectRecipientScreen: React.FC<SelectRecipientScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { onSelect } = (route.params as RouteParams) || { onSelect: () => {} };

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [recipients, setRecipients] = useState<Recipient[]>([]);
  const [filter, setFilter] = useState<'all' | 'matches' | 'conversations'>('all');

  useFocusEffect(
    useCallback(() => {
      loadRecipients();
    }, [])
  );

  const loadRecipients = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockRecipients: Recipient[] = [
        {
          id: '1',
          name: 'Анна',
          avatar: 'https://example.com/avatar1.jpg',
          age: 25,
          isOnline: true,
          mutualFriends: 3,
          isMatch: true,
          hasConversation: true
        },
        {
          id: '2',
          name: 'Михаил',
          age: 30,
          isOnline: false,
          lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          mutualFriends: 1,
          isMatch: true,
          hasConversation: false
        },
        {
          id: '3',
          name: 'Елена',
          avatar: 'https://example.com/avatar3.jpg',
          age: 28,
          isOnline: true,
          mutualFriends: 5,
          isMatch: true,
          hasConversation: true
        },
        {
          id: '4',
          name: 'Дмитрий',
          age: 32,
          isOnline: false,
          lastSeen: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isMatch: false,
          hasConversation: false
        },
        {
          id: '5',
          name: 'Ольга',
          avatar: 'https://example.com/avatar5.jpg',
          age: 26,
          isOnline: true,
          mutualFriends: 2,
          isMatch: true,
          hasConversation: true
        }
      ];
      
      setRecipients(mockRecipients);
    } catch (error) {
      console.error('Error loading recipients:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRecipients();
    setRefreshing(false);
  };

  const handleRecipientSelect = (recipient: Recipient) => {
    onSelect(recipient);
    navigation.goBack();
  };

  const formatLastSeen = (lastSeenString: string): string => {
    const lastSeen = new Date(lastSeenString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} мин назад`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч назад`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} дн назад`;
    }
  };

  const filteredRecipients = recipients.filter(recipient => {
    // Filter by search query
    if (searchQuery && !recipient.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    // Filter by category
    switch (filter) {
      case 'matches':
        return recipient.isMatch;
      case 'conversations':
        return recipient.hasConversation;
      default:
        return true;
    }
  });

  const renderFilterButton = (filterType: 'all' | 'matches' | 'conversations', label: string, count?: number) => (
    <TouchableOpacity
      key={filterType}
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterType && styles.filterButtonTextActive
      ]}>
        {label} {count !== undefined && `(${count})`}
      </Text>
    </TouchableOpacity>
  );

  const renderRecipient = (recipient: Recipient) => (
    <TouchableOpacity
      key={recipient.id}
      style={styles.recipientCard}
      onPress={() => handleRecipientSelect(recipient)}
    >
      <View style={styles.avatarContainer}>
        {recipient.avatar ? (
          <Image source={{ uri: recipient.avatar }} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Icon name="person" size={24} color="#666666" />
          </View>
        )}
        {recipient.isOnline && <View style={styles.onlineIndicator} />}
      </View>
      
      <View style={styles.recipientInfo}>
        <View style={styles.recipientHeader}>
          <Text style={styles.recipientName}>{recipient.name}, {recipient.age}</Text>
          <View style={styles.recipientBadges}>
            {recipient.isMatch && (
              <View style={styles.matchBadge}>
                <Icon name="favorite" size={12} color="#E91E63" />
              </View>
            )}
            {recipient.hasConversation && (
              <View style={styles.conversationBadge}>
                <Icon name="message" size={12} color="#4CAF50" />
              </View>
            )}
          </View>
        </View>
        
        <View style={styles.recipientDetails}>
          <Text style={styles.recipientStatus}>
            {recipient.isOnline ? 'В сети' : 
             recipient.lastSeen ? formatLastSeen(recipient.lastSeen) : 'Не в сети'}
          </Text>
          {recipient.mutualFriends && recipient.mutualFriends > 0 && (
            <Text style={styles.mutualFriends}>
              {recipient.mutualFriends} общих знакомых
            </Text>
          )}
        </View>
      </View>
      
      <Icon name="chevron-right" size={24} color="#cccccc" />
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка контактов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const matchesCount = recipients.filter(r => r.isMatch).length;
  const conversationsCount = recipients.filter(r => r.hasConversation).length;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Выбрать получателя</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск по имени..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#999999"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersContent}
        >
          {renderFilterButton('all', 'Все', recipients.length)}
          {renderFilterButton('matches', 'Совпадения', matchesCount)}
          {renderFilterButton('conversations', 'Переписки', conversationsCount)}
        </ScrollView>
      </View>

      {/* Recipients List */}
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredRecipients.length > 0 ? (
          <View style={styles.recipientsList}>
            {filteredRecipients.map(renderRecipient)}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="person-search" size={64} color="#cccccc" />
            <Text style={styles.emptyTitle}>
              {searchQuery ? 'Никого не найдено' : 
               filter === 'matches' ? 'Нет совпадений' :
               filter === 'conversations' ? 'Нет переписок' :
               'Нет контактов'}
            </Text>
            <Text style={styles.emptyDescription}>
              {searchQuery 
                ? 'Попробуйте изменить поисковый запрос'
                : filter === 'matches' 
                ? 'Начните ставить лайки, чтобы найти совпадения'
                : filter === 'conversations'
                ? 'Начните общаться с совпадениями'
                : 'У вас пока нет контактов для отправки подарков'
              }
            </Text>
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f8f8',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  filtersContainer: {
    backgroundColor: '#f8f8f8',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterButtonTextActive: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
  },
  recipientsList: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  recipientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  recipientInfo: {
    flex: 1,
  },
  recipientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  recipientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  recipientBadges: {
    flexDirection: 'row',
    gap: 4,
  },
  matchBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  conversationBadge: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#4CAF50' + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recipientDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  recipientStatus: {
    fontSize: 14,
    color: '#4CAF50',
  },
  mutualFriends: {
    fontSize: 12,
    color: '#2196F3',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomPadding: {
    height: 40,
  },
});

export default SelectRecipientScreen;
