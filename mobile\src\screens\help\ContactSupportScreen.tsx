import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Alert,
  Linking
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ContactSupportScreenProps {}

interface RouteParams {
  method?: 'email' | 'chat' | 'phone';
  subject?: string;
}

interface SupportMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: string;
  status?: 'sent' | 'delivered' | 'read';
}

interface ContactMethod {
  id: string;
  type: 'email' | 'chat' | 'phone' | 'faq';
  title: string;
  description: string;
  icon: string;
  availability: string;
  responseTime: string;
}

const ContactSupportScreen: React.FC<ContactSupportScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { method = 'chat', subject = '' } = (route.params as RouteParams) || {};

  const [loading, setLoading] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState(method);
  const [messageText, setMessageText] = useState('');
  const [messages, setMessages] = useState<SupportMessage[]>([]);
  const [isChatActive, setIsChatActive] = useState(false);
  const [userInfo, setUserInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });

  const contactMethods: ContactMethod[] = [
    {
      id: 'chat',
      type: 'chat',
      title: 'Онлайн чат',
      description: 'Быстрая помощь от наших операторов',
      icon: 'chat',
      availability: 'Круглосуточно',
      responseTime: 'В течение 5 минут'
    },
    {
      id: 'email',
      type: 'email',
      title: 'Email поддержка',
      description: 'Подробный ответ на ваш email',
      icon: 'email',
      availability: 'Круглосуточно',
      responseTime: 'В течение 2 часов'
    },
    {
      id: 'phone',
      type: 'phone',
      title: 'Телефон',
      description: 'Прямая связь с оператором',
      icon: 'phone',
      availability: '9:00 - 21:00 МСК',
      responseTime: 'Мгновенно'
    },
    {
      id: 'faq',
      type: 'faq',
      title: 'База знаний',
      description: 'Ответы на частые вопросы',
      icon: 'help',
      availability: 'Круглосуточно',
      responseTime: 'Мгновенно'
    }
  ];

  useFocusEffect(
    useCallback(() => {
      loadUserInfo();
      if (selectedMethod === 'chat') {
        loadChatHistory();
      }
    }, [selectedMethod])
  );

  const loadUserInfo = async () => {
    try {
      const savedUserInfo = await AsyncStorage.getItem('userInfo');
      if (savedUserInfo) {
        setUserInfo(JSON.parse(savedUserInfo));
      }
    } catch (error) {
      console.error('Error loading user info:', error);
    }
  };

  const loadChatHistory = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual chat history from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockMessages: SupportMessage[] = [
        {
          id: '1',
          text: 'Здравствуйте! Как дела с вашим вопросом?',
          isUser: false,
          timestamp: new Date(Date.now() - 60000).toISOString(),
          status: 'read'
        }
      ];
      
      setMessages(mockMessages);
      setIsChatActive(true);
    } catch (error) {
      console.error('Error loading chat history:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!messageText.trim()) return;

    const newMessage: SupportMessage = {
      id: Date.now().toString(),
      text: messageText.trim(),
      isUser: true,
      timestamp: new Date().toISOString(),
      status: 'sent'
    };

    setMessages(prev => [...prev, newMessage]);
    setMessageText('');

    try {
      // TODO: Send message to API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate operator response
      setTimeout(() => {
        const response: SupportMessage = {
          id: (Date.now() + 1).toString(),
          text: 'Спасибо за ваше сообщение! Наш оператор ответит вам в ближайшее время.',
          isUser: false,
          timestamp: new Date().toISOString()
        };
        setMessages(prev => [...prev, response]);
      }, 2000);
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить сообщение');
    }
  };

  const handleMethodSelect = (methodType: string) => {
    setSelectedMethod(methodType);
    
    switch (methodType) {
      case 'phone':
        handlePhoneCall();
        break;
      case 'email':
        handleEmailContact();
        break;
      case 'faq':
        navigation.navigate('HelpCenter');
        break;
      case 'chat':
        loadChatHistory();
        break;
    }
  };

  const handlePhoneCall = () => {
    const phoneNumber = '+7 (800) 555-35-35';
    Alert.alert(
      'Позвонить в поддержку',
      `Номер: ${phoneNumber}\nВремя работы: 9:00 - 21:00 МСК`,
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Позвонить', onPress: () => Linking.openURL(`tel:${phoneNumber}`) }
      ]
    );
  };

  const handleEmailContact = () => {
    const email = '<EMAIL>';
    const emailSubject = subject || 'Вопрос по приложению';
    const emailBody = `Здравствуйте!\n\nОпишите вашу проблему:\n\n\n\nС уважением,\n${userInfo.name || 'Пользователь'}`;
    
    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;
    Linking.openURL(mailtoUrl);
  };

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ru-RU', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderContactMethod = (method: ContactMethod) => (
    <TouchableOpacity
      key={method.id}
      style={[
        styles.methodCard,
        selectedMethod === method.type && styles.methodCardSelected
      ]}
      onPress={() => handleMethodSelect(method.type)}
    >
      <View style={styles.methodIcon}>
        <Icon 
          name={method.icon} 
          size={24} 
          color={selectedMethod === method.type ? '#ffffff' : '#E91E63'} 
        />
      </View>
      
      <View style={styles.methodInfo}>
        <Text style={[
          styles.methodTitle,
          selectedMethod === method.type && styles.methodTitleSelected
        ]}>
          {method.title}
        </Text>
        <Text style={[
          styles.methodDescription,
          selectedMethod === method.type && styles.methodDescriptionSelected
        ]}>
          {method.description}
        </Text>
        <View style={styles.methodDetails}>
          <Text style={[
            styles.methodAvailability,
            selectedMethod === method.type && styles.methodAvailabilitySelected
          ]}>
            {method.availability}
          </Text>
          <Text style={[
            styles.methodResponseTime,
            selectedMethod === method.type && styles.methodResponseTimeSelected
          ]}>
            Ответ: {method.responseTime}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMessage = (message: SupportMessage) => (
    <View
      key={message.id}
      style={[
        styles.messageContainer,
        message.isUser ? styles.userMessage : styles.supportMessage
      ]}
    >
      <Text style={[
        styles.messageText,
        message.isUser ? styles.userMessageText : styles.supportMessageText
      ]}>
        {message.text}
      </Text>
      <View style={styles.messageFooter}>
        <Text style={[
          styles.messageTime,
          message.isUser ? styles.userMessageTime : styles.supportMessageTime
        ]}>
          {formatTime(message.timestamp)}
        </Text>
        {message.isUser && message.status && (
          <Icon 
            name={message.status === 'read' ? 'done-all' : 'done'} 
            size={12} 
            color="#4CAF50" 
          />
        )}
      </View>
    </View>
  );

  const renderChatInterface = () => (
    <View style={styles.chatContainer}>
      <ScrollView style={styles.messagesContainer} showsVerticalScrollIndicator={false}>
        {messages.map(renderMessage)}
      </ScrollView>
      
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.messageInput}
          placeholder="Введите сообщение..."
          value={messageText}
          onChangeText={setMessageText}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          style={[
            styles.sendButton,
            !messageText.trim() && styles.sendButtonDisabled
          ]}
          onPress={handleSendMessage}
          disabled={!messageText.trim()}
        >
          <Icon name="send" size={20} color="#ffffff" />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Связаться с поддержкой</Text>
        <View style={styles.headerRight} />
      </View>

      {selectedMethod === 'chat' && isChatActive ? (
        renderChatInterface()
      ) : (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Contact Methods */}
          <View style={styles.methodsSection}>
            <Text style={styles.sectionTitle}>Выберите способ связи</Text>
            {contactMethods.map(renderContactMethod)}
          </View>

          {/* Support Info */}
          <View style={styles.infoSection}>
            <View style={styles.infoCard}>
              <Icon name="schedule" size={24} color="#4CAF50" />
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Время работы поддержки</Text>
                <Text style={styles.infoDescription}>
                  Чат и email: круглосуточно{'\n'}
                  Телефон: 9:00 - 21:00 МСК
                </Text>
              </View>
            </View>

            <View style={styles.infoCard}>
              <Icon name="language" size={24} color="#2196F3" />
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Языки поддержки</Text>
                <Text style={styles.infoDescription}>
                  Русский, English
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      )}

      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#E91E63" />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  methodsSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  methodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  methodCardSelected: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  methodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  methodInfo: {
    flex: 1,
  },
  methodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  methodTitleSelected: {
    color: '#ffffff',
  },
  methodDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  methodDescriptionSelected: {
    color: '#ffffff' + 'CC',
  },
  methodDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  methodAvailability: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '500',
  },
  methodAvailabilitySelected: {
    color: '#ffffff',
  },
  methodResponseTime: {
    fontSize: 12,
    color: '#999999',
  },
  methodResponseTimeSelected: {
    color: '#ffffff' + '99',
  },
  infoSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderTopWidth: 8,
    borderTopColor: '#f5f5f5',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  infoContent: {
    marginLeft: 16,
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  infoDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  messageContainer: {
    marginBottom: 16,
    maxWidth: '80%',
  },
  userMessage: {
    alignSelf: 'flex-end',
  },
  supportMessage: {
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
    padding: 12,
    borderRadius: 12,
  },
  userMessageText: {
    backgroundColor: '#E91E63',
    color: '#ffffff',
  },
  supportMessageText: {
    backgroundColor: '#f0f0f0',
    color: '#333333',
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
    paddingHorizontal: 4,
  },
  messageTime: {
    fontSize: 12,
    marginRight: 4,
  },
  userMessageTime: {
    color: '#666666',
  },
  supportMessageTime: {
    color: '#999999',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  messageInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    maxHeight: 100,
    marginRight: 12,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E91E63',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ContactSupportScreen;
