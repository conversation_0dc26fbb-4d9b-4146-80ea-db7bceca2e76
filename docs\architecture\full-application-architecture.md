# ПОЛНАЯ АРХИТЕКТУРА ПРИЛОЖЕНИЯ LIKES & LOVE

## 1. ОБЩАЯ СТРУКТУРА ПРИЛОЖЕНИЯ

### 1.1 Платформы
1. **Web Application (PWA)**
   - Next.js 15.3.3
   - Material-UI + Ant Design
   - Полная функциональность desktop/mobile web

2. **Mobile Application**
   - React Native 0.75.4 + Expo 53
   - Android, Huawei поддержка
   - Native функции (камера, геолокация, push)

3. **Admin Panel**
   - React Admin 5.4.3
   - Управление пользователями и контентом
   - Модерация и аналитика

### 1.2 Backend Services
1. **API Gateway (NestJS)**
   - REST API + GraphQL
   - WebSocket для real-time
   - Микросервисная архитектура

2. **Базы данных**
   - PostgreSQL 15 (основная)
   - Redis (кэш и сессии)
   - MinIO (файловое хранилище)

## 2. ФУНКЦИОНАЛЬНЫЕ МОДУЛИ

### 2.1 Модуль аутентификации
**Страницы:**
- `/auth/login` - Вход в систему
- `/auth/register` - Регистрация
- `/auth/forgot-password` - Восстановление пароля
- `/auth/reset-password` - Сброс пароля
- `/auth/verify-email` - Подтверждение email
- `/auth/verify-phone` - Подтверждение телефона
- `/auth/2fa-setup` - Настройка 2FA
- `/auth/2fa-verify` - Проверка 2FA
- `/auth/social-callback` - Callback для соц. сетей
- `/auth/gosuslugi-verify` - Верификация через Госуслуги

**Функции:**
- Email/телефон регистрация
- Социальные сети (VK, Google, Яндекс, Сбер)
- Госуслуги интеграция
- 2FA через TOTP
- JWT + Refresh tokens
- Восстановление доступа

### 2.2 Модуль профилей
**Страницы:**
- `/profile` - Мой профиль
- `/profile/edit` - Редактирование профиля
- `/profile/photos` - Управление фотографиями
- `/profile/verification` - Верификация профиля
- `/profile/settings` - Настройки профиля
- `/profile/privacy` - Настройки приватности
- `/profile/preferences` - Предпочтения для поиска
- `/profile/interests` - Интересы и хобби
- `/profile/video-intro` - Видео-презентация
- `/users/:id` - Просмотр профиля пользователя

**Функции:**
- Базовая информация (имя, возраст, пол)
- Расширенная информация (образование, работа, дети)
- До 9 фотографий с обрезкой
- Видео-презентация (30 сек)
- Интересы и хобби (теги)
- Верификация через документы
- Настройки приватности
- Статистика профиля

### 2.3 Модуль знакомств
**Страницы:**
- `/discover` - Главная страница поиска (swipe)
- `/discover/filters` - Фильтры поиска
- `/discover/map` - Поиск на карте
- `/discover/events` - События поблизости
- `/discover/places` - Популярные места
- `/likes` - Полученные лайки
- `/likes/sent` - Отправленные лайки
- `/matches` - Мои совпадения
- `/matches/:id` - Детали совпадения
- `/boost` - Boost профиля

**Функции:**
- Swipe интерфейс (like/pass)
- Super Like функция
- Геолокационный поиск
- Умные фильтры (возраст, интересы, цели)
- Алгоритм совместимости
- Boost система
- Просмотр кто лайкнул (premium)
- Отмена последнего действия

### 2.4 Модуль сообщений
**Страницы:**
- `/messages` - Список чатов
- `/messages/:matchId` - Чат с пользователем
- `/messages/:matchId/info` - Информация о чате
- `/messages/:matchId/media` - Медиа файлы чата
- `/messages/requests` - Запросы сообщений
- `/messages/archived` - Архивированные чаты

**Функции:**
- Текстовые сообщения
- Фото/видео сообщения
- Голосовые сообщения
- Стикеры и эмодзи
- Статусы прочтения
- Индикатор набора текста
- Поиск по сообщениям
- Архивирование чатов

### 2.5 Модуль видеозвонков
**Страницы:**
- `/calls` - История звонков
- `/calls/:id` - Активный звонок
- `/calls/incoming` - Входящий звонок
- `/calls/settings` - Настройки звонков
- `/calls/schedule` - Запланированные звонки

**Функции:**
- WebRTC видеозвонки
- Голосовые звонки
- Размытие фона
- Виртуальные фоны
- Запись звонков (с согласия)
- Планирование звонков
- Статистика звонков

### 2.6 Модуль социальных функций
**Страницы:**
- `/feed` - Лента активности
- `/feed/create` - Создание поста
- `/stories` - Stories пользователей
- `/stories/create` - Создание story
- `/events` - События и мероприятия
- `/events/:id` - Детали события
- `/events/create` - Создание события
- `/groups` - Группы по интересам
- `/groups/:id` - Страница группы
- `/groups/create` - Создание группы

**Функции:**
- Посты с фото/видео
- Stories на 24 часа
- Лайки и комментарии
- Хэштеги и упоминания
- События для встреч offline
- Группы по интересам
- Приглашения на события

### 2.7 Модуль безопасности
**Страницы:**
- `/safety` - Центр безопасности
- `/safety/tips` - Советы по безопасности
- `/safety/report` - Пожаловаться
- `/safety/block` - Заблокированные пользователи
- `/safety/emergency` - Экстренная помощь
- `/safety/verification` - Проверка пользователей

**Функции:**
- Система жалоб
- Блокировка пользователей
- AI модерация контента
- Проверка фото на подлинность
- Экстренная кнопка SOS
- Советы по безопасным встречам
- История действий

### 2.8 Модуль платежей
**Страницы:**
- `/subscription` - Подписки
- `/subscription/plans` - Тарифные планы
- `/subscription/payment` - Оплата
- `/subscription/history` - История платежей
- `/coins` - Внутренняя валюта
- `/coins/purchase` - Покупка монет
- `/gifts` - Магазин подарков

**Функции:**
- Premium подписки (месяц/год)
- VIP статус
- Покупка Boost
- Покупка Super Likes
- Внутренняя валюта (монеты)
- Виртуальные подарки
- История транзакций
- Автопродление

### 2.9 Модуль геолокации
**Страницы:**
- `/places` - Места поблизости
- `/places/:id` - Детали места
- `/places/checkin` - Check-in
- `/places/popular` - Популярные места
- `/indoor` - Indoor навигация
- `/indoor/:mallId` - Навигация в ТЦ
- `/map` - Карта пользователей

**Функции:**
- GPS позиционирование
- Indoor навигация (WiFi/Bluetooth)
- Check-in в местах
- Рекомендации мест для встреч
- Геофенсинг уведомления
- История посещений
- Популярные места встреч

### 2.10 Модуль уведомлений
**Страницы:**
- `/notifications` - Все уведомления
- `/notifications/settings` - Настройки уведомлений
- `/notifications/schedule` - Расписание уведомлений

**Функции:**
- Push уведомления
- Email уведомления
- SMS для критичных событий
- In-app уведомления
- Настройка типов уведомлений
- Тихие часы
- Дайджесты активности

### 2.11 Модуль настроек
**Страницы:**
- `/settings` - Общие настройки
- `/settings/account` - Настройки аккаунта
- `/settings/privacy` - Приватность
- `/settings/notifications` - Уведомления
- `/settings/security` - Безопасность
- `/settings/payments` - Платежные данные
- `/settings/language` - Язык и регион
- `/settings/help` - Помощь и поддержка
- `/settings/about` - О приложении

**Функции:**
- Управление аккаунтом
- Смена пароля
- Настройка 2FA
- Управление сессиями
- Экспорт данных
- Удаление аккаунта
- Выбор языка
- Темная/светлая тема

### 2.12 Модуль поддержки
**Страницы:**
- `/help` - Центр помощи
- `/help/faq` - Частые вопросы
- `/help/contact` - Связаться с поддержкой
- `/help/tickets` - Мои обращения
- `/help/guides` - Руководства

**Функции:**
- База знаний
- FAQ по категориям
- Чат с поддержкой
- Система тикетов
- Видео-руководства
- Обратная связь

## 3. АДМИНИСТРАТИВНАЯ ПАНЕЛЬ

### 3.1 Управление пользователями
**Страницы:**
- `/admin/users` - Список пользователей
- `/admin/users/:id` - Детали пользователя
- `/admin/users/verification` - Очередь верификации
- `/admin/users/banned` - Заблокированные
- `/admin/users/reports` - Жалобы на пользователей

### 3.2 Модерация контента
**Страницы:**
- `/admin/moderation/photos` - Модерация фото
- `/admin/moderation/videos` - Модерация видео
- `/admin/moderation/posts` - Модерация постов
- `/admin/moderation/messages` - Проблемные сообщения
- `/admin/moderation/ai-queue` - AI очередь

### 3.3 Аналитика
**Страницы:**
- `/admin/analytics/dashboard` - Главная панель
- `/admin/analytics/users` - Аналитика пользователей
- `/admin/analytics/revenue` - Финансовая аналитика
- `/admin/analytics/engagement` - Вовлеченность
- `/admin/analytics/retention` - Удержание

### 3.4 Управление системой
**Страницы:**
- `/admin/settings` - Настройки системы
- `/admin/payments` - Управление платежами
- `/admin/notifications` - Массовые уведомления
- `/admin/events` - Управление событиями
- `/admin/support` - Поддержка пользователей

## 4. МОБИЛЬНОЕ ПРИЛОЖЕНИЕ

### 4.1 Специфичные экраны
- Onboarding flow (5 экранов)
- Permissions requests (геолокация, камера, уведомления)
- Biometric authentication
- Deep linking handlers
- Push notification handlers
- Background location updates
- Offline mode screens

### 4.2 Native функции
- Камера с фильтрами
- Галерея с множественным выбором
- Контакты для приглашений
- Календарь для событий
- Native share
- App rating prompts
- In-app purchases

## 5. ТЕХНИЧЕСКИЕ СТРАНИЦЫ

### 5.1 Служебные страницы
- `/404` - Страница не найдена
- `/500` - Ошибка сервера
- `/maintenance` - Техническое обслуживание
- `/offline` - Offline режим
- `/unsupported` - Браузер не поддерживается
- `/blocked` - Доступ заблокирован

### 5.2 Legal страницы
- `/terms` - Условия использования
- `/privacy` - Политика конфиденциальности
- `/cookies` - Политика cookies
- `/legal/dmca` - DMCA политика
- `/legal/gdpr` - GDPR информация
- `/legal/age` - Возрастные ограничения

## 6. ИНТЕГРАЦИИ И API

### 6.1 Внешние сервисы
- Госуслуги API
- Яндекс.Карты API
- VK OAuth
- Google OAuth
- Сбер ID
- YooKassa
- Stripe
- Firebase
- Agora WebRTC
- Sentry

### 6.2 Внутренние API
- REST API v1
- GraphQL endpoint
- WebSocket server
- Media upload service
- Notification service
- Analytics service
- AI moderation service

## 7. РАЗВЕРТЫВАНИЕ

### 7.1 Environments
- Development
- Staging
- Production
- Disaster Recovery

### 7.2 Инфраструктура
- Kubernetes clusters
- PostgreSQL clusters
- Redis clusters
- MinIO storage
- CDN (Cloudflare)
- Load balancers
- Monitoring stack

## 8. БЕЗОПАСНОСТЬ

### 8.1 Уровни защиты
- WAF (Web Application Firewall)
- DDoS protection
- Rate limiting
- IP whitelisting
- Geo-blocking
- Bot detection
- Fraud detection

### 8.2 Compliance
- GDPR compliance
- Russian data localization
- PCI DSS (платежи)
- Age verification
- Content moderation
- Data retention policies
