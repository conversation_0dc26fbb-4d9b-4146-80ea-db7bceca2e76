import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Switch,
  Modal,
  FlatList,
  Image,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import ImagePicker from 'react-native-image-picker';
import MapView, { Marker } from 'react-native-maps';

interface CreateEventScreenProps {}

interface EventFormData {
  title: string;
  description: string;
  category: string;
  date: Date;
  time: Date;
  location: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  price: string;
  maxAttendees: string;
  isOnline: boolean;
  onlineLink?: string;
  imageUri?: string;
  tags: string[];
  ageRestriction?: string;
  requirements?: string;
}

const CreateEventScreen: React.FC<CreateEventScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [formData, setFormData] = useState<EventFormData>({
    title: '',
    description: '',
    category: '',
    date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    time: new Date(),
    location: {
      name: '',
      address: '',
      latitude: 55.7558,
      longitude: 37.6176
    },
    price: '',
    maxAttendees: '',
    isOnline: false,
    tags: [],
    ageRestriction: '',
    requirements: ''
  });

  const categories = [
    { id: 'dating', name: 'Знакомства', icon: 'favorite', color: '#E91E63' },
    { id: 'party', name: 'Вечеринки', icon: 'celebration', color: '#FF5722' },
    { id: 'culture', name: 'Культура', icon: 'theater-comedy', color: '#9C27B0' },
    { id: 'sport', name: 'Спорт', icon: 'sports', color: '#4CAF50' },
    { id: 'food', name: 'Еда', icon: 'restaurant', color: '#FF9800' },
    { id: 'business', name: 'Бизнес', icon: 'business', color: '#2196F3' },
    { id: 'education', name: 'Обучение', icon: 'school', color: '#607D8B' },
    { id: 'hobby', name: 'Хобби', icon: 'palette', color: '#795548' }
  ];

  const popularTags = [
    'знакомства', 'общение', 'вечер', 'выходные', 'развлечения',
    'музыка', 'танцы', 'игры', 'кулинария', 'искусство',
    'спорт', 'фитнес', 'йога', 'медитация', 'книги',
    'фильмы', 'театр', 'концерт', 'выставка', 'мастер-класс'
  ];

  const handleInputChange = (field: keyof EventFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLocationChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      location: {
        ...prev.location,
        [field]: value
      }
    }));
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      handleInputChange('date', selectedDate);
    }
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(false);
    if (selectedTime) {
      handleInputChange('time', selectedTime);
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    handleInputChange('category', categoryId);
    setShowCategoryModal(false);
  };

  const handleImagePicker = () => {
    const options = {
      title: 'Выберите изображение',
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };

    ImagePicker.showImagePicker(options, (response) => {
      if (response.didCancel || response.error) {
        return;
      }

      if (response.uri) {
        handleInputChange('imageUri', response.uri);
      }
    });
  };

  const handleTagToggle = (tag: string) => {
    const currentTags = formData.tags;
    if (currentTags.includes(tag)) {
      handleInputChange('tags', currentTags.filter(t => t !== tag));
    } else if (currentTags.length < 5) {
      handleInputChange('tags', [...currentTags, tag]);
    } else {
      Alert.alert('Ограничение', 'Можно выбрать максимум 5 тегов');
    }
  };

  const validateForm = (): boolean => {
    if (!formData.title.trim()) {
      Alert.alert('Ошибка', 'Введите название события');
      return false;
    }

    if (!formData.description.trim()) {
      Alert.alert('Ошибка', 'Введите описание события');
      return false;
    }

    if (!formData.category) {
      Alert.alert('Ошибка', 'Выберите категорию события');
      return false;
    }

    if (!formData.isOnline && !formData.location.name.trim()) {
      Alert.alert('Ошибка', 'Укажите место проведения или выберите онлайн формат');
      return false;
    }

    if (formData.isOnline && !formData.onlineLink?.trim()) {
      Alert.alert('Ошибка', 'Укажите ссылку для онлайн события');
      return false;
    }

    if (formData.price && isNaN(Number(formData.price))) {
      Alert.alert('Ошибка', 'Цена должна быть числом');
      return false;
    }

    if (formData.maxAttendees && isNaN(Number(formData.maxAttendees))) {
      Alert.alert('Ошибка', 'Количество участников должно быть числом');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Успех',
        'Событие создано и отправлено на модерацию',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось создать событие');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const formatTime = (time: Date): string => {
    return time.toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryInfo = (categoryId: string) => {
    return categories.find(cat => cat.id === categoryId);
  };

  const renderFormSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderInput = (
    placeholder: string,
    value: string,
    onChangeText: (text: string) => void,
    multiline: boolean = false,
    keyboardType: any = 'default'
  ) => (
    <TextInput
      style={[styles.input, multiline && styles.textArea]}
      placeholder={placeholder}
      value={value}
      onChangeText={onChangeText}
      multiline={multiline}
      numberOfLines={multiline ? 4 : 1}
      keyboardType={keyboardType}
    />
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Создать событие</Text>
        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#E91E63" />
          ) : (
            <Text style={styles.saveButtonText}>Создать</Text>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Info */}
        {renderFormSection(
          'Основная информация',
          <View>
            {renderInput(
              'Название события',
              formData.title,
              (text) => handleInputChange('title', text)
            )}

            {renderInput(
              'Описание события',
              formData.description,
              (text) => handleInputChange('description', text),
              true
            )}

            {/* Category */}
            <TouchableOpacity
              style={styles.selectButton}
              onPress={() => setShowCategoryModal(true)}
            >
              <View style={styles.selectContent}>
                {formData.category ? (
                  <View style={styles.selectedCategory}>
                    <Icon
                      name={getCategoryInfo(formData.category)?.icon || 'category'}
                      size={20}
                      color={getCategoryInfo(formData.category)?.color || '#666666'}
                    />
                    <Text style={styles.selectedCategoryText}>
                      {getCategoryInfo(formData.category)?.name}
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.selectPlaceholder}>Выберите категорию</Text>
                )}
              </View>
              <Icon name="chevron-right" size={20} color="#666666" />
            </TouchableOpacity>

            {/* Image */}
            <TouchableOpacity
              style={styles.imageButton}
              onPress={handleImagePicker}
            >
              {formData.imageUri ? (
                <Image source={{ uri: formData.imageUri }} style={styles.selectedImage} />
              ) : (
                <View style={styles.imagePlaceholder}>
                  <Icon name="add-a-photo" size={32} color="#666666" />
                  <Text style={styles.imagePlaceholderText}>Добавить фото</Text>
                </View>
              )}
            </TouchableOpacity>
          </View>
        )}

        {/* Date & Time */}
        {renderFormSection(
          'Дата и время',
          <View>
            <View style={styles.dateTimeRow}>
              <TouchableOpacity
                style={[styles.dateTimeButton, { flex: 1, marginRight: 8 }]}
                onPress={() => setShowDatePicker(true)}
              >
                <Icon name="event" size={20} color="#E91E63" />
                <Text style={styles.dateTimeText}>{formatDate(formData.date)}</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.dateTimeButton, { flex: 1, marginLeft: 8 }]}
                onPress={() => setShowTimePicker(true)}
              >
                <Icon name="schedule" size={20} color="#E91E63" />
                <Text style={styles.dateTimeText}>{formatTime(formData.time)}</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Location */}
        {renderFormSection(
          'Место проведения',
          <View>
            <View style={styles.onlineToggle}>
              <Text style={styles.onlineToggleText}>Онлайн событие</Text>
              <Switch
                value={formData.isOnline}
                onValueChange={(value) => handleInputChange('isOnline', value)}
                trackColor={{ false: '#e0e0e0', true: '#E91E63' + '40' }}
                thumbColor={formData.isOnline ? '#E91E63' : '#ffffff'}
              />
            </View>

            {formData.isOnline ? (
              renderInput(
                'Ссылка на онлайн событие (Zoom, Meet и т.д.)',
                formData.onlineLink || '',
                (text) => handleInputChange('onlineLink', text)
              )
            ) : (
              <View>
                {renderInput(
                  'Название места',
                  formData.location.name,
                  (text) => handleLocationChange('name', text)
                )}
                {renderInput(
                  'Адрес',
                  formData.location.address,
                  (text) => handleLocationChange('address', text)
                )}

                <TouchableOpacity
                  style={styles.mapButton}
                  onPress={() => setShowLocationModal(true)}
                >
                  <Icon name="location-on" size={20} color="#E91E63" />
                  <Text style={styles.mapButtonText}>Выбрать на карте</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {/* Details */}
        {renderFormSection(
          'Детали',
          <View>
            <View style={styles.detailsRow}>
              {renderInput(
                'Цена (₽)',
                formData.price,
                (text) => handleInputChange('price', text),
                false,
                'numeric'
              )}
              {renderInput(
                'Макс. участников',
                formData.maxAttendees,
                (text) => handleInputChange('maxAttendees', text),
                false,
                'numeric'
              )}
            </View>

            {renderInput(
              'Возрастное ограничение',
              formData.ageRestriction || '',
              (text) => handleInputChange('ageRestriction', text),
              false,
              'numeric'
            )}

            {renderInput(
              'Требования к участникам (опционально)',
              formData.requirements || '',
              (text) => handleInputChange('requirements', text),
              true
            )}
          </View>
        )}

        {/* Tags */}
        {renderFormSection(
          'Теги (до 5 штук)',
          <View>
            <View style={styles.tagsContainer}>
              {popularTags.map((tag) => (
                <TouchableOpacity
                  key={tag}
                  style={[
                    styles.tagButton,
                    formData.tags.includes(tag) && styles.tagButtonSelected
                  ]}
                  onPress={() => handleTagToggle(tag)}
                >
                  <Text
                    style={[
                      styles.tagButtonText,
                      formData.tags.includes(tag) && styles.tagButtonTextSelected
                    ]}
                  >
                    {tag}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.tagsCounter}>
              Выбрано: {formData.tags.length}/5
            </Text>
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={formData.date}
          mode="date"
          display="default"
          onChange={handleDateChange}
          minimumDate={new Date()}
        />
      )}

      {/* Time Picker */}
      {showTimePicker && (
        <DateTimePicker
          value={formData.time}
          mode="time"
          display="default"
          onChange={handleTimeChange}
        />
      )}

      {/* Category Modal */}
      <Modal
        visible={showCategoryModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCategoryModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Выберите категорию</Text>
              <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <FlatList
              data={categories}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.categoryItem}
                  onPress={() => handleCategorySelect(item.id)}
                >
                  <Icon name={item.icon} size={24} color={item.color} />
                  <Text style={styles.categoryItemText}>{item.name}</Text>
                  {formData.category === item.id && (
                    <Icon name="check" size={20} color="#E91E63" />
                  )}
                </TouchableOpacity>
              )}
              keyExtractor={(item) => item.id}
            />
          </View>
        </View>
      </Modal>

      {/* Location Modal */}
      <Modal
        visible={showLocationModal}
        animationType="slide"
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={styles.mapModal}>
          <View style={styles.mapHeader}>
            <TouchableOpacity onPress={() => setShowLocationModal(false)}>
              <Icon name="arrow-back" size={24} color="#333333" />
            </TouchableOpacity>
            <Text style={styles.mapTitle}>Выберите место</Text>
            <TouchableOpacity onPress={() => setShowLocationModal(false)}>
              <Text style={styles.mapSaveText}>Готово</Text>
            </TouchableOpacity>
          </View>

          <MapView
            style={styles.map}
            initialRegion={{
              latitude: formData.location.latitude,
              longitude: formData.location.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }}
            onPress={(event) => {
              const { latitude, longitude } = event.nativeEvent.coordinate;
              handleLocationChange('latitude', latitude);
              handleLocationChange('longitude', longitude);
            }}
          >
            <Marker
              coordinate={{
                latitude: formData.location.latitude,
                longitude: formData.location.longitude,
              }}
              draggable
              onDragEnd={(event) => {
                const { latitude, longitude } = event.nativeEvent.coordinate;
                handleLocationChange('latitude', latitude);
                handleLocationChange('longitude', longitude);
              }}
            />
          </MapView>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
    marginHorizontal: 20,
  },
  input: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333333',
    marginHorizontal: 20,
    marginBottom: 12,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  selectButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 20,
    marginBottom: 12,
  },
  selectContent: {
    flex: 1,
  },
  selectPlaceholder: {
    fontSize: 16,
    color: '#999999',
  },
  selectedCategory: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedCategoryText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  imageButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 12,
    overflow: 'hidden',
  },
  imagePlaceholder: {
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholderText: {
    fontSize: 14,
    color: '#666666',
    marginTop: 8,
  },
  selectedImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  dateTimeRow: {
    flexDirection: 'row',
    marginHorizontal: 20,
  },
  dateTimeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  dateTimeText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  onlineToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginBottom: 12,
  },
  onlineToggleText: {
    fontSize: 16,
    color: '#333333',
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 12,
    marginHorizontal: 20,
    marginBottom: 12,
  },
  mapButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    marginHorizontal: 20,
    gap: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    marginBottom: 12,
  },
  tagButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagButtonSelected: {
    backgroundColor: '#E91E63',
  },
  tagButtonText: {
    fontSize: 12,
    color: '#666666',
  },
  tagButtonTextSelected: {
    color: '#ffffff',
  },
  tagsCounter: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 8,
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  categoryItemText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  mapModal: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  mapHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  mapTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  mapSaveText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  map: {
    flex: 1,
  },
});

export default CreateEventScreen;