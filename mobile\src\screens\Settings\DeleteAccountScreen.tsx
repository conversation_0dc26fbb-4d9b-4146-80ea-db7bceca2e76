import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Alert,
  Switch
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface DeleteAccountScreenProps {}

interface DeletionReason {
  id: string;
  title: string;
  description: string;
}

const DeleteAccountScreen: React.FC<DeleteAccountScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false);
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [customReason, setCustomReason] = useState('');
  const [confirmationText, setConfirmationText] = useState('');
  const [deleteData, setDeleteData] = useState(true);
  const [step, setStep] = useState<'reasons' | 'confirmation' | 'final'>('reasons');

  const deletionReasons: DeletionReason[] = [
    {
      id: 'found_someone',
      title: 'Нашел(а) кого-то',
      description: 'Больше не нужно приложение для знакомств'
    },
    {
      id: 'not_working',
      title: 'Приложение не работает',
      description: 'Технические проблемы или ошибки'
    },
    {
      id: 'privacy_concerns',
      title: 'Проблемы с конфиденциальностью',
      description: 'Беспокойство о безопасности данных'
    },
    {
      id: 'too_expensive',
      title: 'Слишком дорого',
      description: 'Не устраивает стоимость подписки'
    },
    {
      id: 'harassment',
      title: 'Домогательства',
      description: 'Получаю нежелательные сообщения'
    },
    {
      id: 'no_matches',
      title: 'Нет совпадений',
      description: 'Не получается найти подходящих людей'
    },
    {
      id: 'other',
      title: 'Другая причина',
      description: 'Укажите свою причину'
    }
  ];

  const handleReasonSelect = (reasonId: string) => {
    setSelectedReason(reasonId);
  };

  const handleContinue = () => {
    if (!selectedReason) {
      Alert.alert('Ошибка', 'Пожалуйста, выберите причину удаления аккаунта');
      return;
    }

    if (selectedReason === 'other' && !customReason.trim()) {
      Alert.alert('Ошибка', 'Пожалуйста, укажите причину удаления');
      return;
    }

    setStep('confirmation');
  };

  const handleDeleteAccount = async () => {
    if (confirmationText.toLowerCase() !== 'удалить') {
      Alert.alert('Ошибка', 'Для подтверждения введите слово "удалить"');
      return;
    }

    Alert.alert(
      'Последнее предупреждение',
      'Вы действительно хотите удалить свой аккаунт? Это действие нельзя отменить.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить навсегда',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              
              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 3000));
              
              // Clear all local data
              await AsyncStorage.clear();
              
              setStep('final');
            } catch (error) {
              console.error('Error deleting account:', error);
              Alert.alert('Ошибка', 'Не удалось удалить аккаунт. Попробуйте позже.');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const handleContactSupport = () => {
    navigation.navigate('ContactSupport', {
      subject: 'Проблемы с удалением аккаунта',
      reason: selectedReason === 'other' ? customReason : 
              deletionReasons.find(r => r.id === selectedReason)?.title
    });
  };

  const renderReason = (reason: DeletionReason) => {
    const isSelected = selectedReason === reason.id;
    
    return (
      <TouchableOpacity
        key={reason.id}
        style={[
          styles.reasonCard,
          isSelected && styles.reasonCardSelected
        ]}
        onPress={() => handleReasonSelect(reason.id)}
      >
        <View style={styles.reasonContent}>
          <Text style={[
            styles.reasonTitle,
            isSelected && styles.reasonTitleSelected
          ]}>
            {reason.title}
          </Text>
          <Text style={[
            styles.reasonDescription,
            isSelected && styles.reasonDescriptionSelected
          ]}>
            {reason.description}
          </Text>
        </View>
        
        <View style={[
          styles.radioButton,
          isSelected && styles.radioButtonSelected
        ]}>
          {isSelected && <View style={styles.radioButtonInner} />}
        </View>
      </TouchableOpacity>
    );
  };

  const renderReasonsStep = () => (
    <>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Почему вы хотите удалить аккаунт?</Text>
        <Text style={styles.stepDescription}>
          Ваш отзыв поможет нам улучшить приложение
        </Text>
      </View>

      <View style={styles.reasonsList}>
        {deletionReasons.map(renderReason)}
      </View>

      {selectedReason === 'other' && (
        <View style={styles.customReasonSection}>
          <Text style={styles.customReasonLabel}>Укажите причину:</Text>
          <TextInput
            style={styles.customReasonInput}
            placeholder="Опишите причину удаления аккаунта..."
            value={customReason}
            onChangeText={setCustomReason}
            multiline
            numberOfLines={3}
            maxLength={500}
            textAlignVertical="top"
          />
          <Text style={styles.characterCount}>{customReason.length}/500</Text>
        </View>
      )}

      <View style={styles.stepFooter}>
        <TouchableOpacity
          style={[
            styles.continueButton,
            (!selectedReason || (selectedReason === 'other' && !customReason.trim())) && 
            styles.continueButtonDisabled
          ]}
          onPress={handleContinue}
          disabled={!selectedReason || (selectedReason === 'other' && !customReason.trim())}
        >
          <Text style={styles.continueButtonText}>Продолжить</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  const renderConfirmationStep = () => (
    <>
      <View style={styles.stepHeader}>
        <Text style={styles.stepTitle}>Подтверждение удаления</Text>
        <Text style={styles.stepDescription}>
          Это действие нельзя отменить. Все ваши данные будут удалены навсегда.
        </Text>
      </View>

      <View style={styles.warningSection}>
        <Icon name="warning" size={48} color="#F44336" />
        <Text style={styles.warningTitle}>Что будет удалено:</Text>
        <View style={styles.warningList}>
          <Text style={styles.warningItem}>• Ваш профиль и все фотографии</Text>
          <Text style={styles.warningItem}>• Все сообщения и совпадения</Text>
          <Text style={styles.warningItem}>• История активности</Text>
          <Text style={styles.warningItem}>• Подписки и платежи</Text>
          <Text style={styles.warningItem}>• Все настройки и предпочтения</Text>
        </View>
      </View>

      <View style={styles.dataSection}>
        <View style={styles.dataOption}>
          <View style={styles.dataOptionContent}>
            <Text style={styles.dataOptionTitle}>Удалить все данные</Text>
            <Text style={styles.dataOptionDescription}>
              Полностью удалить всю информацию с серверов
            </Text>
          </View>
          <Switch
            value={deleteData}
            onValueChange={setDeleteData}
            trackColor={{ false: '#f0f0f0', true: '#F44336' + '40' }}
            thumbColor={deleteData ? '#F44336' : '#cccccc'}
          />
        </View>
      </View>

      <View style={styles.confirmationSection}>
        <Text style={styles.confirmationLabel}>
          Для подтверждения введите слово "удалить":
        </Text>
        <TextInput
          style={styles.confirmationInput}
          placeholder="удалить"
          value={confirmationText}
          onChangeText={setConfirmationText}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>

      <View style={styles.stepFooter}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => setStep('reasons')}
        >
          <Text style={styles.backButtonText}>Назад</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.deleteButton,
            (confirmationText.toLowerCase() !== 'удалить' || loading) && 
            styles.deleteButtonDisabled
          ]}
          onPress={handleDeleteAccount}
          disabled={confirmationText.toLowerCase() !== 'удалить' || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Text style={styles.deleteButtonText}>Удалить аккаунт</Text>
          )}
        </TouchableOpacity>
      </View>
    </>
  );

  const renderFinalStep = () => (
    <View style={styles.finalContainer}>
      <Icon name="check-circle" size={80} color="#4CAF50" />
      <Text style={styles.finalTitle}>Аккаунт удален</Text>
      <Text style={styles.finalDescription}>
        Ваш аккаунт и все данные были успешно удалены. 
        Спасибо за то, что пользовались нашим приложением.
      </Text>
      
      <TouchableOpacity
        style={styles.finalButton}
        onPress={() => {
          // Navigate to auth screen or restart app
          navigation.reset({
            index: 0,
            routes: [{ name: 'Auth' }],
          });
        }}
      >
        <Text style={styles.finalButtonText}>Закрыть приложение</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      {step !== 'final' && (
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.headerBackButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Удаление аккаунта</Text>
          <TouchableOpacity 
            style={styles.supportButton}
            onPress={handleContactSupport}
          >
            <Icon name="help" size={24} color="#666666" />
          </TouchableOpacity>
        </View>
      )}

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {step === 'reasons' && renderReasonsStep()}
        {step === 'confirmation' && renderConfirmationStep()}
        {step === 'final' && renderFinalStep()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerBackButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  supportButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepHeader: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 12,
  },
  stepDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 20,
  },
  reasonsList: {
    paddingVertical: 16,
  },
  reasonCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  reasonCardSelected: {
    borderColor: '#F44336',
    backgroundColor: '#F44336' + '05',
  },
  reasonContent: {
    flex: 1,
  },
  reasonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  reasonTitleSelected: {
    color: '#F44336',
  },
  reasonDescription: {
    fontSize: 14,
    color: '#666666',
  },
  reasonDescriptionSelected: {
    color: '#F44336' + 'CC',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: '#F44336',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#F44336',
  },
  customReasonSection: {
    paddingVertical: 16,
  },
  customReasonLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  customReasonInput: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    minHeight: 80,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 8,
  },
  stepFooter: {
    flexDirection: 'row',
    paddingVertical: 24,
    gap: 12,
  },
  continueButton: {
    flex: 1,
    backgroundColor: '#F44336',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  continueButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  continueButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  warningSection: {
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
    padding: 24,
    marginVertical: 16,
  },
  warningTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#F44336',
    marginTop: 16,
    marginBottom: 16,
  },
  warningList: {
    alignSelf: 'stretch',
  },
  warningItem: {
    fontSize: 14,
    color: '#F57C00',
    marginBottom: 8,
    lineHeight: 20,
  },
  dataSection: {
    paddingVertical: 16,
  },
  dataOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  dataOptionContent: {
    flex: 1,
  },
  dataOptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  dataOptionDescription: {
    fontSize: 14,
    color: '#666666',
  },
  confirmationSection: {
    paddingVertical: 16,
  },
  confirmationLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  confirmationInput: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  backButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
  deleteButton: {
    flex: 2,
    backgroundColor: '#F44336',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  deleteButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  deleteButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  finalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  finalTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333333',
    marginTop: 24,
    marginBottom: 16,
    textAlign: 'center',
  },
  finalDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 40,
  },
  finalButton: {
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  finalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default DeleteAccountScreen;
