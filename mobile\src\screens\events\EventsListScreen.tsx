import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

const { width } = Dimensions.get('window');

interface EventsListScreenProps {}

interface EventItem {
  id: string;
  title: string;
  description: string;
  category: string;
  date: string;
  time: string;
  location: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  organizer: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
  };
  imageUrl: string;
  price: number;
  currency: string;
  attendeesCount: number;
  maxAttendees: number;
  isAttending: boolean;
  isFavorite: boolean;
  tags: string[];
  ageRestriction?: number;
  isOnline: boolean;
}

const EventsListScreen: React.FC<EventsListScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [events, setEvents] = useState<EventItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  const categories = [
    { id: 'all', name: 'Все', icon: 'apps' },
    { id: 'dating', name: 'Знакомства', icon: 'favorite' },
    { id: 'party', name: 'Вечеринки', icon: 'celebration' },
    { id: 'culture', name: 'Культура', icon: 'theater-comedy' },
    { id: 'sport', name: 'Спорт', icon: 'sports' },
    { id: 'food', name: 'Еда', icon: 'restaurant' },
    { id: 'business', name: 'Бизнес', icon: 'business' },
    { id: 'education', name: 'Обучение', icon: 'school' }
  ];

  useEffect(() => {
    loadEvents();
  }, [selectedCategory]);

  useFocusEffect(
    React.useCallback(() => {
      loadEvents();
    }, [])
  );

  const loadEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockEvents: EventItem[] = [
        {
          id: '1',
          title: 'Speed Dating в центре города',
          description: 'Встреча для знакомства с интересными людьми в уютной атмосфере',
          category: 'dating',
          date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          time: '19:00',
          location: {
            name: 'Кафе "Встреча"',
            address: 'ул. Тверская, 15',
            latitude: 55.7558,
            longitude: 37.6176
          },
          organizer: {
            id: 'org1',
            name: 'LikesLove Events',
            avatar: 'https://example.com/organizer1.jpg',
            isVerified: true
          },
          imageUrl: 'https://example.com/event1.jpg',
          price: 1500,
          currency: 'RUB',
          attendeesCount: 12,
          maxAttendees: 20,
          isAttending: false,
          isFavorite: false,
          tags: ['знакомства', 'общение', 'вечер'],
          ageRestriction: 18,
          isOnline: false
        },
        {
          id: '2',
          title: 'Кулинарный мастер-класс для пар',
          description: 'Готовим вместе и знакомимся за приготовлением вкусных блюд',
          category: 'food',
          date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          time: '18:30',
          location: {
            name: 'Кулинарная студия "Вкус"',
            address: 'ул. Арбат, 25',
            latitude: 55.7558,
            longitude: 37.6176
          },
          organizer: {
            id: 'org2',
            name: 'Кулинарные встречи',
            avatar: 'https://example.com/organizer2.jpg',
            isVerified: true
          },
          imageUrl: 'https://example.com/event2.jpg',
          price: 2500,
          currency: 'RUB',
          attendeesCount: 8,
          maxAttendees: 16,
          isAttending: true,
          isFavorite: true,
          tags: ['кулинария', 'пары', 'мастер-класс'],
          ageRestriction: 21,
          isOnline: false
        },
        {
          id: '3',
          title: 'Онлайн игра "Правда или действие"',
          description: 'Веселая онлайн игра для знакомства и общения',
          category: 'party',
          date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
          time: '20:00',
          location: {
            name: 'Zoom',
            address: 'Онлайн',
            latitude: 0,
            longitude: 0
          },
          organizer: {
            id: 'org3',
            name: 'Онлайн развлечения',
            avatar: 'https://example.com/organizer3.jpg',
            isVerified: false
          },
          imageUrl: 'https://example.com/event3.jpg',
          price: 0,
          currency: 'RUB',
          attendeesCount: 25,
          maxAttendees: 50,
          isAttending: false,
          isFavorite: false,
          tags: ['онлайн', 'игры', 'общение'],
          ageRestriction: 18,
          isOnline: true
        }
      ];

      setEvents(mockEvents);
    } catch (err: any) {
      setError('Ошибка загрузки событий');
      console.error('Events loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadEvents();
    setRefreshing(false);
  };

  const handleEventPress = (event: EventItem) => {
    navigation.navigate('EventDetail', { eventId: event.id });
  };

  const handleAttendEvent = async (eventId: string) => {
    try {
      // TODO: Implement actual API call
      setEvents(prev => prev.map(event =>
        event.id === eventId
          ? {
              ...event,
              isAttending: !event.isAttending,
              attendeesCount: event.isAttending
                ? event.attendeesCount - 1
                : event.attendeesCount + 1
            }
          : event
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить статус участия');
    }
  };

  const handleFavoriteEvent = async (eventId: string) => {
    try {
      // TODO: Implement actual API call
      setEvents(prev => prev.map(event =>
        event.id === eventId
          ? { ...event, isFavorite: !event.isFavorite }
          : event
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось добавить в избранное');
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Сегодня';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Завтра';
    } else {
      return date.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'long'
      });
    }
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.location.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || event.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const renderCategoryFilter = () => (
    <View style={styles.categoriesContainer}>
      <FlatList
        data={categories}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryButton,
              selectedCategory === item.id && styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(item.id)}
          >
            <Icon
              name={item.icon}
              size={16}
              color={selectedCategory === item.id ? '#ffffff' : '#666666'}
            />
            <Text
              style={[
                styles.categoryText,
                selectedCategory === item.id && styles.categoryTextActive
              ]}
            >
              {item.name}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoriesContent}
      />
    </View>
  );

  const renderEventCard = ({ item }: { item: EventItem }) => (
    <TouchableOpacity
      style={styles.eventCard}
      onPress={() => handleEventPress(item)}
    >
      <View style={styles.eventImageContainer}>
        <View style={styles.eventImagePlaceholder}>
          <Icon name="event" size={40} color="#E91E63" />
        </View>

        {item.isOnline && (
          <View style={styles.onlineBadge}>
            <Icon name="videocam" size={12} color="#ffffff" />
            <Text style={styles.onlineText}>Online</Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleFavoriteEvent(item.id)}
        >
          <Icon
            name={item.isFavorite ? 'favorite' : 'favorite-border'}
            size={20}
            color={item.isFavorite ? '#E91E63' : '#ffffff'}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.eventContent}>
        <View style={styles.eventHeader}>
          <Text style={styles.eventTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <View style={styles.priceContainer}>
            {item.price > 0 ? (
              <Text style={styles.eventPrice}>{item.price}₽</Text>
            ) : (
              <Text style={styles.eventPriceFree}>Бесплатно</Text>
            )}
          </View>
        </View>

        <Text style={styles.eventDescription} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.eventDetails}>
          <View style={styles.eventDateTime}>
            <Icon name="schedule" size={14} color="#666666" />
            <Text style={styles.eventDateTimeText}>
              {formatDate(item.date)} в {item.time}
            </Text>
          </View>

          <View style={styles.eventLocation}>
            <Icon name="location-on" size={14} color="#666666" />
            <Text style={styles.eventLocationText} numberOfLines={1}>
              {item.location.name}
            </Text>
          </View>
        </View>

        <View style={styles.eventFooter}>
          <View style={styles.attendeesInfo}>
            <Icon name="people" size={16} color="#666666" />
            <Text style={styles.attendeesText}>
              {item.attendeesCount}/{item.maxAttendees}
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.attendButton,
              item.isAttending && styles.attendButtonActive
            ]}
            onPress={() => handleAttendEvent(item.id)}
          >
            <Icon
              name={item.isAttending ? 'check' : 'add'}
              size={16}
              color={item.isAttending ? '#ffffff' : '#E91E63'}
            />
            <Text
              style={[
                styles.attendButtonText,
                item.isAttending && styles.attendButtonTextActive
              ]}
            >
              {item.isAttending ? 'Участвую' : 'Участвовать'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="event-note" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Нет событий</Text>
      <Text style={styles.emptyDescription}>
        Здесь будут отображаться интересные события для знакомств
      </Text>
      <TouchableOpacity
        style={styles.createEventButton}
        onPress={() => navigation.navigate('CreateEvent')}
      >
        <Text style={styles.createEventButtonText}>Создать событие</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка событий...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>События</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateEvent')}
        >
          <Icon name="add" size={24} color="#E91E63" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск событий..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories */}
      {renderCategoryFilter()}

      {/* Events List */}
      <FlatList
        data={filteredEvents}
        renderItem={renderEventCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  createButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
    marginLeft: 12,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
  },
  categoryButtonActive: {
    backgroundColor: '#E91E63',
  },
  categoryText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 6,
  },
  categoryTextActive: {
    color: '#ffffff',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  eventCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  eventImageContainer: {
    height: 120,
    position: 'relative',
  },
  eventImagePlaceholder: {
    flex: 1,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    backgroundColor: '#4CAF50',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 4,
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    padding: 8,
  },
  eventContent: {
    padding: 16,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    marginRight: 12,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  eventPrice: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  eventPriceFree: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  eventDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  eventDetails: {
    marginBottom: 12,
  },
  eventDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  eventDateTimeText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventLocationText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
    flex: 1,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  attendeesInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  attendeesText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
  },
  attendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  attendButtonActive: {
    backgroundColor: '#E91E63',
  },
  attendButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 4,
  },
  attendButtonTextActive: {
    color: '#ffffff',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  createEventButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  createEventButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default EventsListScreen;