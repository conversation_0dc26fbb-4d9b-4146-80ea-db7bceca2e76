import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  Linking,
  Share,
  Modal
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';
import { LinearGradient } from 'expo-linear-gradient';

const { width, height } = Dimensions.get('window');

interface PlaceDetailScreenProps {}

interface Place {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  category: {
    id: string;
    name: string;
    icon: string;
    color: string;
  };
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    district?: string;
    formattedAddress: string;
  };
  contact: {
    phone?: string;
    email?: string;
    website?: string;
    socialMedia?: {
      instagram?: string;
      facebook?: string;
      vk?: string;
    };
  };
  workingHours: {
    [key: string]: {
      isOpen: boolean;
      openTime?: string;
      closeTime?: string;
    };
  };
  rating: {
    average: number;
    count: number;
    distribution: {
      5: number;
      4: number;
      3: number;
      2: number;
      1: number;
    };
    aspects: {
      atmosphere: number;
      service: number;
      cleanliness: number;
      value: number;
      location: number;
    };
  };
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  averageCheck?: {
    min: number;
    max: number;
    currency: string;
  };
  photos: Array<{
    id: string;
    url: string;
    thumbnailUrl: string;
    caption?: string;
    category: string;
  }>;
  amenities: Array<{
    id: string;
    name: string;
    icon: string;
    category: string;
    isAvailable: boolean;
  }>;
  tags: string[];
  features: {
    capacity?: number;
    reservationRequired?: boolean;
    acceptsCards: boolean;
    acceptsCash: boolean;
    hasWifi: boolean;
    hasParking: boolean;
    isAccessible: boolean;
    allowsSmoking: boolean;
    isPetFriendly: boolean;
    hasDelivery: boolean;
    hasTakeaway: boolean;
  };
  verification: {
    isVerified: boolean;
    verifiedAt?: string;
    claimedBy?: string;
  };
  statistics: {
    views: number;
    favorites: number;
    checkins: number;
    shares: number;
    meetingsHeld: number;
    eventsHeld: number;
  };
  isFavorite: boolean;
  isCheckedIn: boolean;
  distance?: number;
  estimatedTime?: string;
}

interface Review {
  id: string;
  authorName: string;
  authorAvatar?: string;
  rating: number;
  title?: string;
  comment: string;
  aspects: {
    atmosphere: number;
    service: number;
    cleanliness: number;
    value: number;
    location: number;
  };
  photos: string[];
  visitDate?: string;
  createdAt: string;
  isVerified: boolean;
  helpfulCount: number;
}

const PlaceDetailScreen: React.FC<PlaceDetailScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { placeId } = route.params as { placeId: string };

  const [loading, setLoading] = useState(true);
  const [place, setPlace] = useState<Place | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [showAllPhotos, setShowAllPhotos] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);

  useEffect(() => {
    loadPlaceDetail();
    loadReviews();
    getCurrentLocation();
  }, [placeId]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }
    } catch (error) {
      console.log('Location error:', error);
    }
  };

  const loadPlaceDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockPlace: Place = {
        id: placeId,
        name: 'Кафе "Встреча"',
        description: 'Уютное кафе в центре города с домашней атмосферой и вкусной кухней. Идеальное место для романтических свиданий и дружеских встреч. Мы предлагаем широкий выбор кофе, десертов и легких закусок.',
        shortDescription: 'Уютное кафе с домашней атмосферой',
        category: {
          id: 'cafe',
          name: 'Кафе',
          icon: 'local-cafe',
          color: '#8D6E63'
        },
        location: {
          latitude: 55.7558,
          longitude: 37.6176,
          address: 'ул. Тверская, 15',
          city: 'Москва',
          district: 'Тверской',
          formattedAddress: 'ул. Тверская, 15, Тверской район, Москва'
        },
        contact: {
          phone: '+7 (495) 123-45-67',
          email: '<EMAIL>',
          website: 'https://cafe-vstrecha.ru',
          socialMedia: {
            instagram: '@cafe_vstrecha',
            facebook: 'cafe.vstrecha',
            vk: 'cafe_vstrecha'
          }
        },
        workingHours: {
          monday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
          tuesday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
          wednesday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
          thursday: { isOpen: true, openTime: '08:00', closeTime: '22:00' },
          friday: { isOpen: true, openTime: '08:00', closeTime: '23:00' },
          saturday: { isOpen: true, openTime: '09:00', closeTime: '23:00' },
          sunday: { isOpen: true, openTime: '09:00', closeTime: '22:00' }
        },
        rating: {
          average: 4.6,
          count: 127,
          distribution: { 5: 65, 4: 35, 3: 15, 2: 8, 1: 4 },
          aspects: {
            atmosphere: 4.8,
            service: 4.5,
            cleanliness: 4.7,
            value: 4.3,
            location: 4.9
          }
        },
        priceRange: '$$',
        averageCheck: {
          min: 800,
          max: 1500,
          currency: 'RUB'
        },
        photos: [
          {
            id: '1',
            url: 'https://example.com/photo1.jpg',
            thumbnailUrl: 'https://example.com/thumb1.jpg',
            caption: 'Интерьер кафе',
            category: 'interior'
          },
          {
            id: '2',
            url: 'https://example.com/photo2.jpg',
            thumbnailUrl: 'https://example.com/thumb2.jpg',
            caption: 'Фирменный кофе',
            category: 'food'
          }
        ],
        amenities: [
          { id: '1', name: 'Wi-Fi', icon: 'wifi', category: 'comfort', isAvailable: true },
          { id: '2', name: 'Парковка', icon: 'local-parking', category: 'comfort', isAvailable: true },
          { id: '3', name: 'Терраса', icon: 'deck', category: 'comfort', isAvailable: true },
          { id: '4', name: 'Кондиционер', icon: 'ac-unit', category: 'comfort', isAvailable: true }
        ],
        tags: ['романтично', 'уютно', 'кофе', 'десерты', 'свидания'],
        features: {
          capacity: 40,
          reservationRequired: false,
          acceptsCards: true,
          acceptsCash: true,
          hasWifi: true,
          hasParking: true,
          isAccessible: true,
          allowsSmoking: false,
          isPetFriendly: true,
          hasDelivery: true,
          hasTakeaway: true
        },
        verification: {
          isVerified: true,
          verifiedAt: '2024-01-15T10:00:00Z',
          claimedBy: 'owner'
        },
        statistics: {
          views: 1247,
          favorites: 89,
          checkins: 156,
          shares: 23,
          meetingsHeld: 45,
          eventsHeld: 12
        },
        isFavorite: false,
        isCheckedIn: false,
        distance: 0.8,
        estimatedTime: '10 мин'
      };

      setPlace(mockPlace);
    } catch (err: any) {
      setError('Ошибка загрузки информации о месте');
      console.error('Place detail loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadReviews = async () => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockReviews: Review[] = [
        {
          id: '1',
          authorName: 'Анна Петрова',
          authorAvatar: 'https://example.com/avatar1.jpg',
          rating: 5,
          title: 'Отличное место!',
          comment: 'Очень уютное кафе с прекрасной атмосферой. Кофе вкусный, персонал дружелюбный. Обязательно вернемся!',
          aspects: {
            atmosphere: 5,
            service: 5,
            cleanliness: 5,
            value: 4,
            location: 5
          },
          photos: [],
          visitDate: '2024-01-10',
          createdAt: '2024-01-11T10:00:00Z',
          isVerified: true,
          helpfulCount: 12
        },
        {
          id: '2',
          authorName: 'Михаил Сидоров',
          rating: 4,
          comment: 'Хорошее кафе, но цены немного завышены. В целом рекомендую для свиданий.',
          aspects: {
            atmosphere: 5,
            service: 4,
            cleanliness: 4,
            value: 3,
            location: 4
          },
          photos: [],
          createdAt: '2024-01-08T15:30:00Z',
          isVerified: false,
          helpfulCount: 8
        }
      ];

      setReviews(mockReviews);
    } catch (error) {
      console.error('Reviews loading error:', error);
    }
  };

  const handleFavoriteToggle = async () => {
    if (!place) return;

    try {
      setActionLoading(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setPlace(prev => prev ? { ...prev, isFavorite: !prev.isFavorite } : null);

      Alert.alert(
        'Успех',
        place.isFavorite ? 'Место удалено из избранного' : 'Место добавлено в избранное'
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось обновить избранное');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCheckIn = async () => {
    if (!place) return;

    try {
      setActionLoading(true);

      // TODO: Implement actual API call with location verification
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPlace(prev => prev ? { ...prev, isCheckedIn: true } : null);

      Alert.alert('Успех', 'Вы отметились в этом месте!');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отметиться');
    } finally {
      setActionLoading(false);
    }
  };

  const handleShare = async () => {
    if (!place) return;

    try {
      await Share.share({
        message: `Посмотри это место: ${place.name}\n${place.shortDescription}\n\nОценка: ${place.rating.average}⭐ (${place.rating.count} отзывов)\nАдрес: ${place.location.formattedAddress}`,
        title: place.name,
      });
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const handleCall = () => {
    if (place?.contact.phone) {
      Linking.openURL(`tel:${place.contact.phone}`);
    }
  };

  const handleWebsite = () => {
    if (place?.contact.website) {
      Linking.openURL(place.contact.website);
    }
  };

  const handleDirections = () => {
    if (place && userLocation) {
      const url = `https://www.openstreetmap.org/directions?from=${userLocation.latitude},${userLocation.longitude}&to=${place.location.latitude},${place.location.longitude}`;
      Linking.openURL(url);
    }
  };

  const handleCreateMeeting = () => {
    if (place) {
      navigation.navigate('CreateMeeting', {
        selectedPlace: {
          id: place.id,
          name: place.name,
          address: place.location.formattedAddress,
          latitude: place.location.latitude,
          longitude: place.location.longitude
        }
      });
    }
  };

  const formatWorkingHours = () => {
    if (!place) return '';

    const today = new Date().toLocaleDateString('en-US', { weekday: 'lowercase' });
    const todayHours = place.workingHours[today];

    if (!todayHours?.isOpen) {
      return 'Сегодня закрыто';
    }

    return `Сегодня: ${todayHours.openTime} - ${todayHours.closeTime}`;
  };

  const isOpenNow = () => {
    if (!place) return false;

    const now = new Date();
    const today = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
    const todayHours = place.workingHours[today];

    if (!todayHours?.isOpen) return false;

    const currentTime = now.getHours() * 60 + now.getMinutes();
    const [openHour, openMin] = todayHours.openTime!.split(':').map(Number);
    const [closeHour, closeMin] = todayHours.closeTime!.split(':').map(Number);

    const openTime = openHour * 60 + openMin;
    const closeTime = closeHour * 60 + closeMin;

    return currentTime >= openTime && currentTime <= closeTime;
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes} мин. назад`;
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч. назад`;
    } else {
      const days = Math.floor(diffInMinutes / (24 * 60));
      return `${days} дн. назад`;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#E91E63" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка места...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !place) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error || 'Место не найдено'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadPlaceDetail}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#E91E63" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header Image */}
        <View style={styles.imageContainer}>
          <View style={styles.imagePlaceholder}>
            <Icon name={place.category.icon} size={64} color={place.category.color} />
          </View>

          {/* Header Overlay */}
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageOverlay}
          >
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-back" size={24} color="#ffffff" />
              </TouchableOpacity>

              <View style={styles.headerRightActions}>
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={handleShare}
                >
                  <Icon name="share" size={24} color="#ffffff" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={handleFavoriteToggle}
                  disabled={actionLoading}
                >
                  <Icon
                    name={place.isFavorite ? 'favorite' : 'favorite-border'}
                    size={24}
                    color={place.isFavorite ? '#E91E63' : '#ffffff'}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Status Badge */}
            <View style={styles.statusBadge}>
              <View style={[styles.statusDot, { backgroundColor: isOpenNow() ? '#4CAF50' : '#F44336' }]} />
              <Text style={styles.statusText}>
                {isOpenNow() ? 'Открыто' : 'Закрыто'}
              </Text>
            </View>
          </LinearGradient>
        </View>

        {/* Place Info */}
        <View style={styles.placeInfo}>
          <View style={styles.placeHeader}>
            <View style={styles.placeTitleContainer}>
              <Text style={styles.placeTitle}>{place.name}</Text>
              {place.verification.isVerified && (
                <Icon name="verified" size={20} color="#4CAF50" style={styles.verifiedIcon} />
              )}
            </View>

            <View style={styles.categoryBadge}>
              <Icon name={place.category.icon} size={16} color={place.category.color} />
              <Text style={[styles.categoryText, { color: place.category.color }]}>
                {place.category.name}
              </Text>
            </View>
          </View>

          <Text style={styles.placeDescription}>{place.description}</Text>

          {/* Rating and Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.ratingContainer}>
              <View style={styles.ratingMain}>
                <Icon name="star" size={20} color="#FFD700" />
                <Text style={styles.ratingText}>{place.rating.average}</Text>
                <Text style={styles.ratingCount}>({place.rating.count})</Text>
              </View>

              <View style={styles.ratingAspects}>
                {Object.entries(place.rating.aspects).map(([aspect, rating]) => (
                  <View key={aspect} style={styles.aspectItem}>
                    <Text style={styles.aspectName}>
                      {aspect === 'atmosphere' ? 'Атмосфера' :
                       aspect === 'service' ? 'Сервис' :
                       aspect === 'cleanliness' ? 'Чистота' :
                       aspect === 'value' ? 'Цена/качество' :
                       'Расположение'}
                    </Text>
                    <View style={styles.aspectRating}>
                      <View style={styles.aspectBar}>
                        <View
                          style={[styles.aspectBarFill, { width: `${(rating / 5) * 100}%` }]}
                        />
                      </View>
                      <Text style={styles.aspectValue}>{rating}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </View>

            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Icon name="visibility" size={16} color="#666666" />
                <Text style={styles.statText}>{place.statistics.views}</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="favorite" size={16} color="#666666" />
                <Text style={styles.statText}>{place.statistics.favorites}</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="check-in" size={16} color="#666666" />
                <Text style={styles.statText}>{place.statistics.checkins}</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="event" size={16} color="#666666" />
                <Text style={styles.statText}>{place.statistics.meetingsHeld}</Text>
              </View>
            </View>
          </View>

          {/* Price and Working Hours */}
          <View style={styles.infoRow}>
            <View style={styles.priceContainer}>
              <Text style={styles.priceRange}>{place.priceRange}</Text>
              {place.averageCheck && (
                <Text style={styles.averageCheck}>
                  {place.averageCheck.min}-{place.averageCheck.max}₽
                </Text>
              )}
            </View>

            <View style={styles.workingHoursContainer}>
              <Icon name="schedule" size={16} color="#666666" />
              <Text style={styles.workingHoursText}>{formatWorkingHours()}</Text>
            </View>
          </View>

          {/* Tags */}
          <View style={styles.tagsContainer}>
            {place.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Contact and Location */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Контакты и расположение</Text>

          <View style={styles.contactItem}>
            <Icon name="location-on" size={20} color="#E91E63" />
            <Text style={styles.contactText}>{place.location.formattedAddress}</Text>
          </View>

          {place.contact.phone && (
            <TouchableOpacity style={styles.contactItem} onPress={handleCall}>
              <Icon name="phone" size={20} color="#E91E63" />
              <Text style={styles.contactText}>{place.contact.phone}</Text>
              <Icon name="call" size={16} color="#666666" />
            </TouchableOpacity>
          )}

          {place.contact.website && (
            <TouchableOpacity style={styles.contactItem} onPress={handleWebsite}>
              <Icon name="language" size={20} color="#E91E63" />
              <Text style={styles.contactText}>Веб-сайт</Text>
              <Icon name="open-in-new" size={16} color="#666666" />
            </TouchableOpacity>
          )}

          {place.distance && (
            <View style={styles.contactItem}>
              <Icon name="directions-walk" size={20} color="#E91E63" />
              <Text style={styles.contactText}>
                {place.distance} км • {place.estimatedTime}
              </Text>
            </View>
          )}
        </View>

        {/* Map */}
        <View style={styles.section}>
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: place.location.latitude,
                longitude: place.location.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }}
              scrollEnabled={false}
              zoomEnabled={false}
            >
              <Marker
                coordinate={{
                  latitude: place.location.latitude,
                  longitude: place.location.longitude,
                }}
                title={place.name}
                description={place.location.address}
              />
            </MapView>

            <TouchableOpacity style={styles.directionsButton} onPress={handleDirections}>
              <Icon name="directions" size={20} color="#E91E63" />
              <Text style={styles.directionsButtonText}>Маршрут</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Amenities */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Удобства</Text>
          <View style={styles.amenitiesGrid}>
            {place.amenities.map((amenity) => (
              <View key={amenity.id} style={styles.amenityItem}>
                <Icon
                  name={amenity.icon}
                  size={20}
                  color={amenity.isAvailable ? '#4CAF50' : '#cccccc'}
                />
                <Text style={[
                  styles.amenityText,
                  { color: amenity.isAvailable ? '#333333' : '#cccccc' }
                ]}>
                  {amenity.name}
                </Text>
              </View>
            ))}
          </View>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Особенности</Text>
          <View style={styles.featuresGrid}>
            {place.features.capacity && (
              <View style={styles.featureItem}>
                <Icon name="people" size={16} color="#666666" />
                <Text style={styles.featureText}>До {place.features.capacity} человек</Text>
              </View>
            )}

            {place.features.acceptsCards && (
              <View style={styles.featureItem}>
                <Icon name="credit-card" size={16} color="#666666" />
                <Text style={styles.featureText}>Принимают карты</Text>
              </View>
            )}

            {place.features.hasWifi && (
              <View style={styles.featureItem}>
                <Icon name="wifi" size={16} color="#666666" />
                <Text style={styles.featureText}>Wi-Fi</Text>
              </View>
            )}

            {place.features.hasParking && (
              <View style={styles.featureItem}>
                <Icon name="local-parking" size={16} color="#666666" />
                <Text style={styles.featureText}>Парковка</Text>
              </View>
            )}

            {place.features.isPetFriendly && (
              <View style={styles.featureItem}>
                <Icon name="pets" size={16} color="#666666" />
                <Text style={styles.featureText}>Можно с питомцами</Text>
              </View>
            )}

            {place.features.hasDelivery && (
              <View style={styles.featureItem}>
                <Icon name="delivery-dining" size={16} color="#666666" />
                <Text style={styles.featureText}>Доставка</Text>
              </View>
            )}
          </View>
        </View>

        {/* Reviews */}
        <View style={styles.section}>
          <View style={styles.reviewsHeader}>
            <Text style={styles.sectionTitle}>Отзывы ({reviews.length})</Text>
            <TouchableOpacity onPress={() => setShowReviewModal(true)}>
              <Text style={styles.writeReviewText}>Написать отзыв</Text>
            </TouchableOpacity>
          </View>

          {reviews.map((review) => (
            <View key={review.id} style={styles.reviewItem}>
              <View style={styles.reviewHeader}>
                <View style={styles.reviewerAvatar}>
                  <Icon name="person" size={20} color="#E91E63" />
                </View>

                <View style={styles.reviewerInfo}>
                  <View style={styles.reviewerNameRow}>
                    <Text style={styles.reviewerName}>{review.authorName}</Text>
                    {review.isVerified && (
                      <Icon name="verified" size={14} color="#4CAF50" />
                    )}
                  </View>

                  <View style={styles.reviewRating}>
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Icon
                        key={star}
                        name="star"
                        size={14}
                        color={star <= review.rating ? '#FFD700' : '#e0e0e0'}
                      />
                    ))}
                  </View>
                </View>

                <Text style={styles.reviewDate}>{formatTimeAgo(review.createdAt)}</Text>
              </View>

              {review.title && (
                <Text style={styles.reviewTitle}>{review.title}</Text>
              )}

              <Text style={styles.reviewComment}>{review.comment}</Text>

              <View style={styles.reviewFooter}>
                <TouchableOpacity style={styles.helpfulButton}>
                  <Icon name="thumb-up" size={14} color="#666666" />
                  <Text style={styles.helpfulText}>Полезно ({review.helpfulCount})</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleCheckIn}
          disabled={actionLoading || place.isCheckedIn}
        >
          {actionLoading ? (
            <ActivityIndicator size="small" color="#E91E63" />
          ) : (
            <>
              <Icon
                name={place.isCheckedIn ? 'check' : 'check-in'}
                size={20}
                color="#E91E63"
              />
              <Text style={styles.actionButtonText}>
                {place.isCheckedIn ? 'Отмечен' : 'Отметиться'}
              </Text>
            </>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.primaryActionButton]}
          onPress={handleCreateMeeting}
        >
          <Icon name="event" size={20} color="#ffffff" />
          <Text style={[styles.actionButtonText, styles.primaryActionButtonText]}>
            Встреча здесь
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    position: 'relative',
  },
  imagePlaceholder: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    padding: 8,
  },
  headerRightActions: {
    flexDirection: 'row',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeInfo: {
    padding: 20,
  },
  placeHeader: {
    marginBottom: 12,
  },
  placeTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  placeTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    flex: 1,
    lineHeight: 32,
  },
  verifiedIcon: {
    marginLeft: 8,
  },
  categoryBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  placeDescription: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 24,
    marginBottom: 20,
  },
  statsContainer: {
    marginBottom: 20,
  },
  ratingContainer: {
    marginBottom: 16,
  },
  ratingMain: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingText: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginLeft: 6,
  },
  ratingCount: {
    fontSize: 16,
    color: '#666666',
    marginLeft: 4,
  },
  ratingAspects: {
    gap: 8,
  },
  aspectItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  aspectName: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  aspectRating: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 12,
  },
  aspectBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#e0e0e0',
    borderRadius: 2,
    marginRight: 8,
  },
  aspectBarFill: {
    height: '100%',
    backgroundColor: '#FFD700',
    borderRadius: 2,
  },
  aspectValue: {
    fontSize: 12,
    color: '#333333',
    fontWeight: '600',
    width: 24,
    textAlign: 'right',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    paddingVertical: 12,
  },
  statItem: {
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: '#666666',
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  priceContainer: {
    alignItems: 'flex-start',
  },
  priceRange: {
    fontSize: 18,
    fontWeight: '700',
    color: '#E91E63',
  },
  averageCheck: {
    fontSize: 14,
    color: '#666666',
    marginTop: 2,
  },
  workingHoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  workingHoursText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#E91E63',
    fontWeight: '500',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  contactText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  mapContainer: {
    height: 200,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  directionsButton: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  directionsButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 4,
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '45%',
    marginBottom: 8,
  },
  amenityText: {
    fontSize: 14,
    marginLeft: 8,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  featureText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 6,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  writeReviewText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '600',
  },
  reviewItem: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginRight: 6,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: 2,
  },
  reviewDate: {
    fontSize: 12,
    color: '#999999',
  },
  reviewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 6,
  },
  reviewComment: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  reviewFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  helpfulText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  bottomPadding: {
    height: 100,
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  primaryActionButton: {
    backgroundColor: '#E91E63',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  primaryActionButtonText: {
    color: '#ffffff',
  },
});

export default PlaceDetailScreen;