import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
  FormControlLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Paper,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Check as CheckIcon,
  Star as StarIcon,
  Diamond as DiamondIcon,
  Favorite as FavoriteIcon,
  Visibility as VisibilityIcon,
  Chat as ChatIcon,
  SupervisorAccount as SuperIcon,
  Security as SecurityIcon,
  Support as SupportIcon,
  Cancel as CancelIcon,
  Payment as PaymentIcon,
  History as HistoryIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { subscriptionService } from '../../services/subscriptionService';
import { SubscriptionPlan, UserSubscription } from '../../types/subscription.types';

interface SubscriptionPlansPageProps {}

const SubscriptionPlansPage: React.FC<SubscriptionPlansPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [upgradeDialog, setUpgradeDialog] = useState(false);
  const [cancelDialog, setCancelDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/subscription/plans');
      return;
    }

    loadSubscriptionData();
  }, [isAuthenticated, router]);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [plansData, subscriptionData] = await Promise.all([
        subscriptionService.getPlans(),
        subscriptionService.getCurrentSubscription()
      ]);

      setPlans(plansData);
      setCurrentSubscription(subscriptionData);
    } catch (err: any) {
      setError('Ошибка загрузки данных подписки');
      console.error('Subscription data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    if (currentSubscription?.planId === plan.id) {
      return; // Уже активный план
    }

    setSelectedPlan(plan);
    setUpgradeDialog(true);
  };

  const handleUpgrade = async () => {
    if (!selectedPlan) return;

    try {
      const paymentUrl = await subscriptionService.createPayment(selectedPlan.id);
      window.location.href = paymentUrl;
    } catch (error) {
      setError('Ошибка создания платежа');
    }
  };

  const handleCancelSubscription = async () => {
    try {
      await subscriptionService.cancelSubscription();
      await loadSubscriptionData();
      setCancelDialog(false);
    } catch (error) {
      setError('Ошибка отмены подписки');
    }
  };

  const getPlanIcon = (planType: string) => {
    switch (planType) {
      case 'premium':
        return <StarIcon sx={{ color: '#FFD700' }} />;
      case 'vip':
        return <DiamondIcon sx={{ color: '#E91E63' }} />;
      default:
        return <FavoriteIcon sx={{ color: '#2196F3' }} />;
    }
  };

  const getPlanColor = (planType: string) => {
    switch (planType) {
      case 'premium':
        return '#FFD700';
      case 'vip':
        return '#E91E63';
      default:
        return '#2196F3';
    }
  };

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.planId === planId && currentSubscription?.status === 'active';
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isActive = isCurrentPlan(plan.id);
    const planColor = getPlanColor(plan.type);

    return (
      <Grid item xs={12} md={4} key={plan.id}>
        <Card
          sx={{
            height: '100%',
            position: 'relative',
            border: isActive ? `2px solid ${planColor}` : '1px solid #e0e0e0',
            transform: plan.type === 'premium' ? 'scale(1.05)' : 'scale(1)',
            transition: 'transform 0.2s ease-in-out'
          }}
        >
          {plan.popular && (
            <Chip
              label="Популярный"
              color="primary"
              sx={{
                position: 'absolute',
                top: -10,
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 1
              }}
            />
          )}

          <CardHeader
            avatar={
              <Avatar sx={{ bgcolor: planColor }}>
                {getPlanIcon(plan.type)}
              </Avatar>
            }
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="h6">{plan.name}</Typography>
                {isActive && <Chip label="Активен" color="success" size="small" />}
              </Box>
            }
            subheader={plan.description}
          />

          <CardContent>
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <Typography variant="h3" component="div" sx={{ color: planColor }}>
                {plan.price === 0 ? 'Бесплатно' : `${plan.price}₽`}
              </Typography>
              {plan.price > 0 && (
                <Typography variant="body2" color="text.secondary">
                  за {plan.duration === 'monthly' ? 'месяц' : 'год'}
                </Typography>
              )}
              {plan.originalPrice && plan.originalPrice > plan.price && (
                <Typography
                  variant="body2"
                  sx={{ textDecoration: 'line-through', color: 'text.secondary' }}
                >
                  {plan.originalPrice}₽
                </Typography>
              )}
            </Box>

            <List dense>
              {plan.features.map((feature, index) => (
                <ListItem key={index} sx={{ px: 0 }}>
                  <ListItemIcon sx={{ minWidth: 32 }}>
                    <CheckIcon color="success" fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary={feature.name}
                    secondary={feature.description}
                    primaryTypographyProps={{ variant: 'body2' }}
                    secondaryTypographyProps={{ variant: 'caption' }}
                  />
                </ListItem>
              ))}
            </List>

            <Box sx={{ mt: 3 }}>
              {isActive ? (
                <Button
                  fullWidth
                  variant="outlined"
                  color="success"
                  startIcon={<CheckIcon />}
                  disabled
                >
                  Текущий план
                </Button>
              ) : (
                <Button
                  fullWidth
                  variant={plan.type === 'premium' ? 'contained' : 'outlined'}
                  color="primary"
                  onClick={() => handlePlanSelect(plan)}
                  sx={{
                    bgcolor: plan.type === 'premium' ? planColor : undefined,
                    '&:hover': {
                      bgcolor: plan.type === 'premium' ? planColor : undefined,
                      opacity: 0.8
                    }
                  }}
                >
                  {plan.price === 0 ? 'Выбрать' : 'Подписаться'}
                </Button>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    );
  };

  const renderCurrentSubscription = () => {
    if (!currentSubscription || currentSubscription.status !== 'active') {
      return null;
    }

    const plan = plans.find(p => p.id === currentSubscription.planId);
    if (!plan) return null;

    const daysLeft = Math.ceil(
      (new Date(currentSubscription.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    );

    return (
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Текущая подписка
        </Typography>

        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Avatar sx={{ bgcolor: getPlanColor(plan.type) }}>
                {getPlanIcon(plan.type)}
              </Avatar>
              <Box>
                <Typography variant="subtitle1">{plan.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  Действует до {new Date(currentSubscription.expiresAt).toLocaleDateString('ru-RU')}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Осталось дней: {daysLeft}
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.max(0, Math.min(100, (daysLeft / 30) * 100))}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
          </Grid>

          <Grid item xs={12} md={2}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="История платежей">
                <IconButton onClick={() => router.push('/subscription/history')}>
                  <HistoryIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Отменить подписку">
                <IconButton
                  color="error"
                  onClick={() => setCancelDialog(true)}
                >
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  return (
    <Layout>
      <Head>
        <title>Планы подписки - LikesLove</title>
        <meta name="description" content="Выберите подходящий план подписки на LikesLove и получите доступ к премиум функциям" />
        <meta name="keywords" content="подписка, премиум, vip, планы, цены, знакомства" />
        <meta property="og:title" content="Планы подписки - LikesLove" />
        <meta property="og:description" content="Премиум функции для успешных знакомств" />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="https://likeslove.ru/subscription/plans" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Планы подписки
          </Typography>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
            Выберите план, который подходит именно вам
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Current Subscription */}
        {renderCurrentSubscription()}

        {/* Plans */}
        <Grid container spacing={4} sx={{ mb: 6 }}>
          {plans.map(plan => renderPlanCard(plan))}
        </Grid>