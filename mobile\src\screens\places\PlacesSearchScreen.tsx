import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  FlatList,
  Dimensions,
  PermissionsAndroid,
  Platform
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import Geolocation from '@react-native-community/geolocation';

interface PlacesSearchScreenProps {}

interface Place {
  id: string;
  name: string;
  category: string;
  rating: number;
  distance: number;
  address: string;
  priceLevel: number;
  imageUrl: string;
  latitude: number;
  longitude: number;
  isOpen: boolean;
  openingHours?: string;
  phone?: string;
  website?: string;
}

interface Location {
  latitude: number;
  longitude: number;
}

const { width, height } = Dimensions.get('window');

const PlacesSearchScreen: React.FC<PlacesSearchScreenProps> = () => {
  const navigation = useNavigation();
  const mapRef = useRef<MapView>(null);

  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [places, setPlaces] = useState<Place[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<Place[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [userLocation, setUserLocation] = useState<Location | null>(null);
  const [showMap, setShowMap] = useState(false);
  const [selectedPlace, setSelectedPlace] = useState<Place | null>(null);
  const [locationPermission, setLocationPermission] = useState(false);

  const categories = [
    { id: 'all', name: 'Все', icon: 'apps' },
    { id: 'restaurant', name: 'Рестораны', icon: 'restaurant' },
    { id: 'cafe', name: 'Кафе', icon: 'local-cafe' },
    { id: 'bar', name: 'Бары', icon: 'local-bar' },
    { id: 'entertainment', name: 'Развлечения', icon: 'movie' },
    { id: 'park', name: 'Парки', icon: 'park' },
    { id: 'museum', name: 'Музеи', icon: 'museum' },
    { id: 'shopping', name: 'Шопинг', icon: 'shopping-bag' }
  ];

  useEffect(() => {
    requestLocationPermission();
  }, []);

  useEffect(() => {
    if (userLocation) {
      loadNearbyPlaces();
    }
  }, [userLocation]);

  useEffect(() => {
    filterPlaces();
  }, [places, selectedCategory, searchQuery]);

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Разрешение на геолокацию',
            message: 'Приложению нужен доступ к вашему местоположению для поиска ближайших мест',
            buttonNeutral: 'Спросить позже',
            buttonNegative: 'Отмена',
            buttonPositive: 'OK',
          }
        );

        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setLocationPermission(true);
          getCurrentLocation();
        } else {
          setLocationPermission(false);
          setLoading(false);
        }
      } else {
        // iOS permissions handled by react-native-geolocation-service
        getCurrentLocation();
      }
    } catch (error) {
      console.error('Permission error:', error);
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserLocation({ latitude, longitude });
        setLocationPermission(true);
      },
      (error) => {
        console.error('Location error:', error);
        Alert.alert(
          'Ошибка геолокации',
          'Не удалось получить ваше местоположение. Будут показаны места в центре города.',
          [{ text: 'OK' }]
        );
        // Default to Moscow center
        setUserLocation({ latitude: 55.7558, longitude: 37.6176 });
        setLoading(false);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };

  const loadNearbyPlaces = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call to places service
      await new Promise(resolve => setTimeout(resolve, 1500));

      const mockPlaces: Place[] = [
        {
          id: '1',
          name: 'Кафе "Пушкин"',
          category: 'cafe',
          rating: 4.5,
          distance: 0.3,
          address: 'Тверской бульвар, 26А',
          priceLevel: 3,
          imageUrl: 'https://example.com/cafe1.jpg',
          latitude: userLocation!.latitude + 0.001,
          longitude: userLocation!.longitude + 0.001,
          isOpen: true,
          openingHours: '08:00 - 23:00',
          phone: '+7 (495) 123-45-67'
        },
        {
          id: '2',
          name: 'Ресторан "Белуга"',
          category: 'restaurant',
          rating: 4.8,
          distance: 0.7,
          address: 'Петровка, 24',
          priceLevel: 4,
          imageUrl: 'https://example.com/restaurant1.jpg',
          latitude: userLocation!.latitude - 0.002,
          longitude: userLocation!.longitude + 0.003,
          isOpen: true,
          openingHours: '12:00 - 02:00'
        },
        {
          id: '3',
          name: 'Парк Горького',
          category: 'park',
          rating: 4.6,
          distance: 1.2,
          address: 'Крымский Вал, 9',
          priceLevel: 1,
          imageUrl: 'https://example.com/park1.jpg',
          latitude: userLocation!.latitude + 0.005,
          longitude: userLocation!.longitude - 0.002,
          isOpen: true,
          openingHours: '06:00 - 24:00'
        },
        {
          id: '4',
          name: 'Бар "Mendeleev"',
          category: 'bar',
          rating: 4.3,
          distance: 0.5,
          address: 'Новый Арбат, 21',
          priceLevel: 3,
          imageUrl: 'https://example.com/bar1.jpg',
          latitude: userLocation!.latitude - 0.001,
          longitude: userLocation!.longitude - 0.004,
          isOpen: false,
          openingHours: '18:00 - 03:00'
        },
        {
          id: '5',
          name: 'Третьяковская галерея',
          category: 'museum',
          rating: 4.9,
          distance: 2.1,
          address: 'Лаврушинский пер., 10',
          priceLevel: 2,
          imageUrl: 'https://example.com/museum1.jpg',
          latitude: userLocation!.latitude + 0.008,
          longitude: userLocation!.longitude + 0.006,
          isOpen: true,
          openingHours: '10:00 - 18:00'
        }
      ];

      setPlaces(mockPlaces);
    } catch (error) {
      console.error('Error loading places:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить места');
    } finally {
      setLoading(false);
    }
  };

  const filterPlaces = () => {
    let filtered = places;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(place => place.category === selectedCategory);
    }

    if (searchQuery.trim()) {
      filtered = filtered.filter(place =>
        place.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        place.address.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    setFilteredPlaces(filtered);
  };

  const handlePlaceSelect = (place: Place) => {
    setSelectedPlace(place);
    if (showMap && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: place.latitude,
        longitude: place.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
    }
  };

  const handlePlacePress = (place: Place) => {
    navigation.navigate('PlaceDetails', { place });
  };

  const handleCreateMeeting = (place: Place) => {
    navigation.navigate('CreateMeeting', { selectedPlace: place });
  };

  const getPriceSymbol = (level: number): string => {
    return '₽'.repeat(level);
  };

  const renderPlaceCard = ({ item }: { item: Place }) => (
    <TouchableOpacity
      style={styles.placeCard}
      onPress={() => handlePlacePress(item)}
      onLongPress={() => handlePlaceSelect(item)}
    >
      <View style={styles.placeImageContainer}>
        <Icon name="place" size={40} color="#E91E63" />
      </View>

      <View style={styles.placeInfo}>
        <View style={styles.placeHeader}>
          <Text style={styles.placeName} numberOfLines={1}>{item.name}</Text>
          <View style={styles.placeStatus}>
            <View style={[styles.statusDot, { backgroundColor: item.isOpen ? '#4CAF50' : '#ff4444' }]} />
            <Text style={[styles.statusText, { color: item.isOpen ? '#4CAF50' : '#ff4444' }]}>
              {item.isOpen ? 'Открыто' : 'Закрыто'}
            </Text>
          </View>
        </View>

        <Text style={styles.placeAddress} numberOfLines={1}>{item.address}</Text>

        <View style={styles.placeDetails}>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{item.rating}</Text>
          </View>

          <Text style={styles.priceLevel}>{getPriceSymbol(item.priceLevel)}</Text>

          <Text style={styles.distance}>{item.distance} км</Text>
        </View>

        {item.openingHours && (
          <Text style={styles.openingHours}>{item.openingHours}</Text>
        )}
      </View>

      <TouchableOpacity
        style={styles.meetingButton}
        onPress={() => handleCreateMeeting(item)}
      >
        <Icon name="event" size={20} color="#E91E63" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  const renderCategoryFilter = () => (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.categoriesContainer}
      contentContainerStyle={styles.categoriesContent}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.id}
          style={[
            styles.categoryButton,
            selectedCategory === category.id && styles.categoryButtonActive
          ]}
          onPress={() => setSelectedCategory(category.id)}
        >
          <Icon
            name={category.icon}
            size={20}
            color={selectedCategory === category.id ? '#ffffff' : '#666666'}
          />
          <Text
            style={[
              styles.categoryText,
              selectedCategory === category.id && styles.categoryTextActive
            ]}
          >
            {category.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Поиск мест поблизости...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!locationPermission) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.permissionContainer}>
          <Icon name="location-off" size={64} color="#cccccc" />
          <Text style={styles.permissionTitle}>Нужен доступ к геолокации</Text>
          <Text style={styles.permissionDescription}>
            Для поиска мест поблизости необходимо разрешить доступ к вашему местоположению
          </Text>
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={requestLocationPermission}
          >
            <Text style={styles.permissionButtonText}>Разрешить доступ</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Места для встреч</Text>
        <TouchableOpacity
          style={styles.mapToggleButton}
          onPress={() => setShowMap(!showMap)}
        >
          <Icon name={showMap ? "list" : "map"} size={24} color="#E91E63" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск мест..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories */}
      {renderCategoryFilter()}

      {/* Content */}
      {showMap ? (
        <View style={styles.mapContainer}>
          {userLocation && (
            <MapView
              ref={mapRef}
              provider={PROVIDER_GOOGLE}
              style={styles.map}
              initialRegion={{
                latitude: userLocation.latitude,
                longitude: userLocation.longitude,
                latitudeDelta: 0.02,
                longitudeDelta: 0.02,
              }}
              showsUserLocation={true}
              showsMyLocationButton={true}
            >
              {filteredPlaces.map((place) => (
                <Marker
                  key={place.id}
                  coordinate={{
                    latitude: place.latitude,
                    longitude: place.longitude,
                  }}
                  title={place.name}
                  description={place.address}
                  onPress={() => handlePlaceSelect(place)}
                />
              ))}
            </MapView>
          )}

          {selectedPlace && (
            <View style={styles.selectedPlaceCard}>
              <Text style={styles.selectedPlaceName}>{selectedPlace.name}</Text>
              <Text style={styles.selectedPlaceAddress}>{selectedPlace.address}</Text>
              <View style={styles.selectedPlaceActions}>
                <TouchableOpacity
                  style={styles.detailsButton}
                  onPress={() => handlePlacePress(selectedPlace)}
                >
                  <Text style={styles.detailsButtonText}>Подробнее</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.meetingButtonSmall}
                  onPress={() => handleCreateMeeting(selectedPlace)}
                >
                  <Icon name="event" size={16} color="#ffffff" />
                  <Text style={styles.meetingButtonText}>Встреча</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      ) : (
        <FlatList
          data={filteredPlaces}
          renderItem={renderPlaceCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Icon name="location-off" size={64} color="#cccccc" />
              <Text style={styles.emptyTitle}>Места не найдены</Text>
              <Text style={styles.emptyDescription}>
                Попробуйте изменить фильтры или поисковый запрос
              </Text>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  permissionButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  permissionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  mapToggleButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
    marginLeft: 12,
  },
  categoriesContainer: {
    paddingVertical: 8,
  },
  categoriesContent: {
    paddingHorizontal: 20,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
  },
  categoryButtonActive: {
    backgroundColor: '#E91E63',
  },
  categoryText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  categoryTextActive: {
    color: '#ffffff',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  selectedPlaceCard: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
  },
  selectedPlaceName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  selectedPlaceAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  selectedPlaceActions: {
    flexDirection: 'row',
    gap: 12,
  },
  detailsButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  detailsButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  meetingButtonSmall: {
    flex: 1,
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  meetingButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 4,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  placeCard: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  placeImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  placeInfo: {
    flex: 1,
  },
  placeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  placeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  placeStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  placeAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  placeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 4,
  },
  priceLevel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
    marginRight: 12,
  },
  distance: {
    fontSize: 14,
    color: '#666666',
  },
  openingHours: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
  },
  meetingButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    padding: 12,
    marginLeft: 12,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default PlacesSearchScreen;