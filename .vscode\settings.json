{
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "files.associations": {
    "*.prisma": "prisma",
    "*.yml": "yaml",
    "*.yaml": "yaml",
    ".github/workflows/*.yml": "github-actions-workflow"
  },
  "jest.autoRun": {
    "watch": false,
    "onSave": "test-file"
  },
  "jest.showCoverageOnLoad": true,
  "prettier.requireConfig": true,
  "prettier.configPath": ".prettierrc",
  "debug.javascript.autoAttachFilter": "smart",
  "typescript.preferences.importModuleSpecifier": "relative",
  "java.configuration.updateBuildConfiguration": "automatic",
  // GitHub Actions and YAML configuration
  "yaml.schemas": {
    "https://json.schemastore.org/github-workflow.json": ".github/workflows/*.yml",
    "./.github/schemas/github-actions-schema.json": ".github/workflows/*.yml"
  },
  "yaml.validate": true,
  "yaml.completion": true,
  "yaml.hover": true,
  "yaml.format.enable": true,
  "yaml.format.singleQuote": false,
  "yaml.format.bracketSpacing": true,
  "yaml.format.proseWrap": "preserve",
  "yaml.format.printWidth": 80,
  "yaml.schemaStore.enable": true,
  "yaml.schemaStore.url": "https://www.schemastore.org/api/json/catalog.json",
  // GitHub Actions extension settings
  "github-actions.workflows.pinned.workflows": [
    ".github/workflows/ci-cd.yml",
    ".github/workflows/production-deploy.yml",
    ".github/workflows/deploy-yc.yml"
  ],
  "github-actions.workflows.pinned.refresh.enabled": true,
  "github-actions.workflows.pinned.refresh.interval": 30,
  // Enhanced editor suggestions for YAML
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  "editor.suggest.insertMode": "replace",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.tabCompletion": "on",
  "editor.wordBasedSuggestions": "matchingDocuments",
  "editor.parameterHints.enabled": true,
  "editor.suggestOnTriggerCharacters": true,
  "editor.quickSuggestionsDelay": 10,
  "editor.suggest.localityBonus": true,
  "editor.suggest.shareSuggestSelections": true,
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.suggest.filterGraceful": true,
  // Disable telemetry for privacy
  "redhat.telemetry.enabled": false,
  "cSpell.words": [
    "государственная"
  ],
  "github.copilot.chat.codesearch.enabled": true,
  "github.copilot.chat.agent.thinkingTool": true,
  "github.copilot.chat.completionContext.typescript.mode": "on",
  "CodeGPT.apiKey": "CodeGPT Plus Beta"
}