import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert,
  Snackbar,
  Avatar,
  IconButton,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  Tabs,
  Tab
} from '@mui/material';
import {
  Edit as EditIcon,
  PhotoCamera as PhotoCameraIcon,
  Delete as DeleteIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Privacy as PrivacyIcon,
  Person as PersonIcon,
  LocationOn as LocationIcon,
  Favorite as FavoriteIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { profileService } from '../../services/profileService';
import { UserProfile, ProfileSettings, NotificationSettings, PrivacySettings } from '../../types/profile.types';

interface ProfileSettingsForm {
  name: string;
  bio: string;
  age: number;
  location: string;
  interests: string[];
  lookingFor: string;
  ageRange: [number, number];
  maxDistance: number;
  showOnline: boolean;
  showDistance: boolean;
  showAge: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  matchNotifications: boolean;
  messageNotifications: boolean;
  marketingEmails: boolean;
}

const validationSchema = yup.object({
  name: yup.string().required('Имя обязательно').min(2, 'Минимум 2 символа'),
  bio: yup.string().max(500, 'Максимум 500 символов'),
  age: yup.number().required('Возраст обязателен').min(18, 'Минимум 18 лет').max(100, 'Максимум 100 лет'),
  location: yup.string().required('Местоположение обязательно'),
  lookingFor: yup.string().required('Цель знакомства обязательна'),
  maxDistance: yup.number().min(1, 'Минимум 1 км').max(100, 'Максимум 100 км')
});

const ProfileSettingsPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated, updateUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [deleteAccountDialog, setDeleteAccountDialog] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm<ProfileSettingsForm>({
    resolver: yupResolver(validationSchema),
    mode: 'onChange'
  });

  const tabs = [
    { label: 'Профиль', icon: <PersonIcon /> },
    { label: 'Приватность', icon: <PrivacyIcon /> },
    { label: 'Уведомления', icon: <NotificationsIcon /> },
    { label: 'Безопасность', icon: <SecurityIcon /> }
  ];

  const interests = [
    'Спорт', 'Музыка', 'Кино', 'Путешествия', 'Кулинария', 'Чтение',
    'Искусство', 'Танцы', 'Фотография', 'Природа', 'Технологии', 'Мода'
  ];

  const lookingForOptions = [
    { value: 'relationship', label: 'Серьезные отношения' },
    { value: 'dating', label: 'Свидания' },
    { value: 'friendship', label: 'Дружба' },
    { value: 'networking', label: 'Знакомства' }
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/profile/settings');
      return;
    }

    loadProfile();
  }, [isAuthenticated, router]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const profileData = await profileService.getProfile();
      setProfile(profileData);

      // Заполняем форму данными профиля
      reset({
        name: profileData.name,
        bio: profileData.bio || '',
        age: profileData.age,
        location: profileData.location,
        interests: profileData.interests || [],
        lookingFor: profileData.lookingFor,
        ageRange: profileData.preferences?.ageRange || [18, 35],
        maxDistance: profileData.preferences?.maxDistance || 25,
        showOnline: profileData.privacy?.showOnline ?? true,
        showDistance: profileData.privacy?.showDistance ?? true,
        showAge: profileData.privacy?.showAge ?? true,
        emailNotifications: profileData.notifications?.email ?? true,
        pushNotifications: profileData.notifications?.push ?? true,
        matchNotifications: profileData.notifications?.matches ?? true,
        messageNotifications: profileData.notifications?.messages ?? true,
        marketingEmails: profileData.notifications?.marketing ?? false
      });
    } catch (err: any) {
      setError('Ошибка загрузки профиля');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileSettingsForm) => {
    try {
      setSaving(true);
      setError(null);

      const updateData = {
        name: data.name,
        bio: data.bio,
        age: data.age,
        location: data.location,
        interests: data.interests,
        lookingFor: data.lookingFor,
        preferences: {
          ageRange: data.ageRange,
          maxDistance: data.maxDistance
        },
        privacy: {
          showOnline: data.showOnline,
          showDistance: data.showDistance,
          showAge: data.showAge
        },
        notifications: {
          email: data.emailNotifications,
          push: data.pushNotifications,
          matches: data.matchNotifications,
          messages: data.messageNotifications,
          marketing: data.marketingEmails
        }
      };

      const updatedProfile = await profileService.updateProfile(updateData);
      setProfile(updatedProfile);
      updateUser(updatedProfile);
      setSuccessMessage('Настройки успешно сохранены');
    } catch (err: any) {
      setError(err.message || 'Ошибка сохранения настроек');
    } finally {
      setSaving(false);
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const formData = new FormData();
      formData.append('photo', file);

      const updatedProfile = await profileService.uploadPhoto(formData);
      setProfile(updatedProfile);
      setSuccessMessage('Фото успешно обновлено');
    } catch (error) {
      setError('Ошибка загрузки фото');
    }
  };

  const handleDeleteAccount = async () => {
    try {
      await profileService.deleteAccount();
      router.push('/');
    } catch (error) {
      setError('Ошибка удаления аккаунта');
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const renderProfileTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent sx={{ textAlign: 'center' }}>
            <Avatar
              src={profile?.photos?.[0]?.url}
              sx={{ width: 120, height: 120, mx: 'auto', mb: 2 }}
            >
              {profile?.name?.charAt(0)}
            </Avatar>
            <input
              accept="image/*"
              style={{ display: 'none' }}
              id="photo-upload"
              type="file"
              onChange={handlePhotoUpload}
            />
            <label htmlFor="photo-upload">
              <IconButton color="primary" component="span">
                <PhotoCameraIcon />
              </IconButton>
            </label>
            <Typography variant="body2" color="text.secondary">
              Изменить фото
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={8}>
        <Card>
          <CardHeader title="Основная информация" />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Имя"
                      fullWidth
                      error={!!errors.name}
                      helperText={errors.name?.message}
                      required
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <Controller
                  name="age"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Возраст"
                      type="number"
                      fullWidth
                      error={!!errors.age}
                      helperText={errors.age?.message}
                      required
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Местоположение"
                      fullWidth
                      error={!!errors.location}
                      helperText={errors.location?.message}
                      required
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="bio"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="О себе"
                      fullWidth
                      multiline
                      rows={4}
                      error={!!errors.bio}
                      helperText={errors.bio?.message || 'Расскажите о себе, своих интересах и увлечениях'}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="lookingFor"
                  control={control}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.lookingFor}>
                      <InputLabel>Цель знакомства</InputLabel>
                      <Select {...field} label="Цель знакомства">
                        {lookingForOptions.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );