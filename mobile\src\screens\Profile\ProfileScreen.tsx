import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  RefreshControl
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

const { width } = Dimensions.get('window');

interface ProfileScreenProps {}

interface UserProfile {
  id: string;
  name: string;
  age: number;
  bio: string;
  location: string;
  photos: Array<{
    id: string;
    url: string;
    isMain: boolean;
  }>;
  interests: string[];
  lookingFor: string;
  isVerified: boolean;
  isPremium: boolean;
  stats: {
    likes: number;
    matches: number;
    views: number;
  };
  preferences: {
    ageRange: [number, number];
    maxDistance: number;
  };
}

const ProfileScreen: React.FC<ProfileScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      loadProfile();
    }, [])
  );

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockProfile: UserProfile = {
        id: '1',
        name: 'Анна',
        age: 25,
        bio: 'Люблю путешествия, фотографию и хорошую компанию. Ищу серьезные отношения с интересным человеком.',
        location: 'Москва',
        photos: [
          { id: '1', url: 'https://example.com/photo1.jpg', isMain: true },
          { id: '2', url: 'https://example.com/photo2.jpg', isMain: false },
          { id: '3', url: 'https://example.com/photo3.jpg', isMain: false }
        ],
        interests: ['Путешествия', 'Фотография', 'Кулинария', 'Спорт', 'Музыка'],
        lookingFor: 'Серьезные отношения',
        isVerified: true,
        isPremium: true,
        stats: {
          likes: 156,
          matches: 23,
          views: 1240
        },
        preferences: {
          ageRange: [22, 35],
          maxDistance: 25
        }
      };

      setProfile(mockProfile);
    } catch (err: any) {
      setError('Ошибка загрузки профиля');
      console.error('Profile loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProfile();
    setRefreshing(false);
  };

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handlePhotoPress = (photoIndex: number) => {
    navigation.navigate('PhotoGallery', {
      photos: profile?.photos,
      initialIndex: photoIndex
    });
  };

  const handleUpgradeToPremium = () => {
    navigation.navigate('Subscription');
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.settingsButton} onPress={handleSettings}>
        <Icon name="settings" size={24} color="#333333" />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>Мой профиль</Text>
      <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
        <Icon name="edit" size={24} color="#E91E63" />
      </TouchableOpacity>
    </View>
  );

  const renderPhotos = () => (
    <View style={styles.photosSection}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.photosContainer}
      >
        {profile?.photos.map((photo, index) => (
          <TouchableOpacity
            key={photo.id}
            style={[styles.photoContainer, photo.isMain && styles.mainPhotoContainer]}
            onPress={() => handlePhotoPress(index)}
          >
            <View style={styles.photoPlaceholder}>
              <Icon name="photo" size={40} color="#E91E63" />
            </View>
            {photo.isMain && (
              <View style={styles.mainPhotoBadge}>
                <Icon name="star" size={16} color="#ffffff" />
                <Text style={styles.mainPhotoText}>Главное</Text>
              </View>
            )}
          </TouchableOpacity>
        ))}

        <TouchableOpacity
          style={styles.addPhotoContainer}
          onPress={() => navigation.navigate('AddPhoto')}
        >
          <Icon name="add" size={32} color="#666666" />
          <Text style={styles.addPhotoText}>Добавить фото</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );

  const renderMainInfo = () => (
    <View style={styles.mainInfoSection}>
      <View style={styles.nameRow}>
        <Text style={styles.name}>{profile?.name}, {profile?.age}</Text>
        <View style={styles.badges}>
          {profile?.isVerified && (
            <View style={styles.verifiedBadge}>
              <Icon name="verified" size={16} color="#4CAF50" />
            </View>
          )}
          {profile?.isPremium && (
            <View style={styles.premiumBadge}>
              <Icon name="star" size={16} color="#FFD700" />
            </View>
          )}
        </View>
      </View>

      <View style={styles.locationRow}>
        <Icon name="location-on" size={16} color="#666666" />
        <Text style={styles.location}>{profile?.location}</Text>
      </View>

      <Text style={styles.bio}>{profile?.bio}</Text>

      <View style={styles.lookingForContainer}>
        <Text style={styles.lookingForLabel}>Ищу:</Text>
        <Text style={styles.lookingForValue}>{profile?.lookingFor}</Text>
      </View>
    </View>
  );

  const renderStats = () => (
    <View style={styles.statsSection}>
      <Text style={styles.sectionTitle}>Статистика</Text>
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{profile?.stats.likes}</Text>
          <Text style={styles.statLabel}>Лайков</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{profile?.stats.matches}</Text>
          <Text style={styles.statLabel}>Совпадений</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{profile?.stats.views}</Text>
          <Text style={styles.statLabel}>Просмотров</Text>
        </View>
      </View>
    </View>
  );

  const renderInterests = () => (
    <View style={styles.interestsSection}>
      <Text style={styles.sectionTitle}>Интересы</Text>
      <View style={styles.interestsContainer}>
        {profile?.interests.map((interest, index) => (
          <View key={index} style={styles.interestChip}>
            <Text style={styles.interestText}>{interest}</Text>
          </View>
        ))}
      </View>
    </View>
  );

  const renderPreferences = () => (
    <View style={styles.preferencesSection}>
      <Text style={styles.sectionTitle}>Предпочтения поиска</Text>
      <View style={styles.preferenceItem}>
        <Icon name="person" size={20} color="#666666" />
        <Text style={styles.preferenceText}>
          Возраст: {profile?.preferences.ageRange[0]} - {profile?.preferences.ageRange[1]} лет
        </Text>
      </View>
      <View style={styles.preferenceItem}>
        <Icon name="location-on" size={20} color="#666666" />
        <Text style={styles.preferenceText}>
          Расстояние: до {profile?.preferences.maxDistance} км
        </Text>
      </View>
    </View>
  );

  const renderPremiumPromo = () => {
    if (profile?.isPremium) return null;

    return (
      <TouchableOpacity style={styles.premiumPromo} onPress={handleUpgradeToPremium}>
        <LinearGradient
          colors={['#E91E63', '#FF6B9D']}
          style={styles.premiumGradient}
        >
          <Icon name="star" size={24} color="#ffffff" />
          <Text style={styles.premiumTitle}>Получить Premium</Text>
          <Text style={styles.premiumSubtitle}>Больше лайков и совпадений</Text>
          <Icon name="arrow-forward" size={20} color="#ffffff" />
        </LinearGradient>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка профиля...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color="#ff4444" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorMessage}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadProfile}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      {renderHeader()}

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {renderPhotos()}
        {renderMainInfo()}
        {renderStats()}
        {renderInterests()}
        {renderPreferences()}
        {renderPremiumPromo()}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  settingsButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  editButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  photosSection: {
    paddingVertical: 20,
  },
  photosContainer: {
    paddingHorizontal: 20,
  },
  photoContainer: {
    width: 100,
    height: 120,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
    marginRight: 12,
    position: 'relative',
    overflow: 'hidden',
  },
  mainPhotoContainer: {
    borderWidth: 2,
    borderColor: '#E91E63',
  },
  photoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainPhotoBadge: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#E91E63',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    gap: 4,
  },
  mainPhotoText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  addPhotoContainer: {
    width: 100,
    height: 120,
    borderRadius: 12,
    backgroundColor: '#f5f5f5',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoText: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    textAlign: 'center',
  },
  mainInfoSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  verifiedBadge: {
    backgroundColor: '#4CAF50' + '20',
    borderRadius: 12,
    padding: 4,
  },
  premiumBadge: {
    backgroundColor: '#FFD700' + '20',
    borderRadius: 12,
    padding: 4,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  location: {
    fontSize: 16,
    color: '#666666',
    marginLeft: 4,
  },
  bio: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
    marginBottom: 16,
  },
  lookingForContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  lookingForLabel: {
    fontSize: 16,
    color: '#666666',
    marginRight: 8,
  },
  lookingForValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  statsSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    padding: 20,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#E91E63',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666666',
  },
  statDivider: {
    width: 1,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 16,
  },
  interestsSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  interestChip: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  interestText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '500',
  },
  preferencesSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  preferenceText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  premiumPromo: {
    marginHorizontal: 20,
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
  },
  premiumGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
  },
  premiumTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 12,
  },
  premiumSubtitle: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.8,
    marginLeft: 12,
    flex: 1,
  },
});

export default ProfileScreen;