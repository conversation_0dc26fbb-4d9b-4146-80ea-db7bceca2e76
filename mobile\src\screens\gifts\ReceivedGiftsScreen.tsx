import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  RefreshControl,
  Alert
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ReceivedGiftsScreenProps {}

interface ReceivedGift {
  id: string;
  gift: {
    id: string;
    name: string;
    image: string;
    price: number;
  };
  sender: {
    id: string;
    name: string;
    avatar?: string;
    age: number;
  };
  message?: string;
  isAnonymous: boolean;
  receivedAt: string;
  isNew: boolean;
  count: number;
}

const ReceivedGiftsScreen: React.FC<ReceivedGiftsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [receivedGifts, setReceivedGifts] = useState<ReceivedGift[]>([]);
  const [filter, setFilter] = useState<'all' | 'new' | 'anonymous'>('all');

  useFocusEffect(
    useCallback(() => {
      loadReceivedGifts();
    }, [])
  );

  const loadReceivedGifts = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockReceivedGifts: ReceivedGift[] = [
        {
          id: '1',
          gift: {
            id: 'gift1',
            name: 'Букет роз',
            image: 'https://example.com/roses.jpg',
            price: 50
          },
          sender: {
            id: 'user1',
            name: 'Анна',
            avatar: 'https://example.com/avatar1.jpg',
            age: 25
          },
          message: 'Ты очень красивая! 🌹',
          isAnonymous: false,
          receivedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isNew: true,
          count: 1
        },
        {
          id: '2',
          gift: {
            id: 'gift2',
            name: 'Плюшевый мишка',
            image: 'https://example.com/teddy.jpg',
            price: 30
          },
          sender: {
            id: 'anonymous',
            name: 'Тайный поклонник',
            age: 0
          },
          isAnonymous: true,
          receivedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isNew: false,
          count: 2
        },
        {
          id: '3',
          gift: {
            id: 'gift3',
            name: 'Шоколадное сердце',
            image: 'https://example.com/chocolate.jpg',
            price: 25
          },
          sender: {
            id: 'user2',
            name: 'Михаил',
            avatar: 'https://example.com/avatar2.jpg',
            age: 30
          },
          message: 'Сладкого дня! 🍫',
          isAnonymous: false,
          receivedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          isNew: false,
          count: 1
        }
      ];
      
      setReceivedGifts(mockReceivedGifts);
    } catch (error) {
      console.error('Error loading received gifts:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить полученные подарки');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReceivedGifts();
    setRefreshing(false);
  };

  const handleGiftPress = (gift: ReceivedGift) => {
    if (!gift.isAnonymous) {
      navigation.navigate('Profile', { userId: gift.sender.id });
    } else {
      Alert.alert(
        'Анонимный подарок',
        'Этот подарок отправлен анонимно. Отправитель скрыл свою личность.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleThankSender = (gift: ReceivedGift) => {
    if (gift.isAnonymous) {
      Alert.alert(
        'Анонимный подарок',
        'Нельзя поблагодарить анонимного отправителя'
      );
      return;
    }

    Alert.alert(
      'Поблагодарить',
      `Отправить благодарность пользователю ${gift.sender.name}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отправить',
          onPress: () => {
            navigation.navigate('Chat', { userId: gift.sender.id });
          }
        }
      ]
    );
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} мин назад`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч назад`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} дн назад`;
    }
  };

  const filteredGifts = receivedGifts.filter(gift => {
    switch (filter) {
      case 'new':
        return gift.isNew;
      case 'anonymous':
        return gift.isAnonymous;
      default:
        return true;
    }
  });

  const renderFilterButton = (filterType: 'all' | 'new' | 'anonymous', label: string) => (
    <TouchableOpacity
      key={filterType}
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterType && styles.filterButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderGiftItem = (gift: ReceivedGift) => (
    <TouchableOpacity
      key={gift.id}
      style={styles.giftItem}
      onPress={() => handleGiftPress(gift)}
    >
      <View style={styles.giftImageContainer}>
        <Image source={{ uri: gift.gift.image }} style={styles.giftImage} />
        {gift.count > 1 && (
          <View style={styles.countBadge}>
            <Text style={styles.countText}>{gift.count}</Text>
          </View>
        )}
        {gift.isNew && <View style={styles.newIndicator} />}
      </View>
      
      <View style={styles.giftContent}>
        <View style={styles.giftHeader}>
          <Text style={styles.giftName}>{gift.gift.name}</Text>
          <Text style={styles.giftTime}>{formatTimeAgo(gift.receivedAt)}</Text>
        </View>
        
        <View style={styles.senderInfo}>
          {gift.isAnonymous ? (
            <View style={styles.anonymousSender}>
              <Icon name="help" size={16} color="#666666" />
              <Text style={styles.senderName}>Анонимный отправитель</Text>
            </View>
          ) : (
            <View style={styles.knownSender}>
              {gift.sender.avatar ? (
                <Image source={{ uri: gift.sender.avatar }} style={styles.senderAvatar} />
              ) : (
                <View style={styles.senderAvatarPlaceholder}>
                  <Icon name="person" size={16} color="#666666" />
                </View>
              )}
              <Text style={styles.senderName}>
                {gift.sender.name}, {gift.sender.age}
              </Text>
            </View>
          )}
        </View>
        
        {gift.message && (
          <Text style={styles.giftMessage}>"{gift.message}"</Text>
        )}
        
        <View style={styles.giftPrice}>
          <Icon name="diamond" size={14} color="#FFD700" />
          <Text style={styles.priceText}>{gift.gift.price} алмазов</Text>
        </View>
      </View>
      
      {!gift.isAnonymous && (
        <TouchableOpacity
          style={styles.thankButton}
          onPress={() => handleThankSender(gift)}
        >
          <Icon name="favorite" size={20} color="#E91E63" />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка подарков...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Полученные подарки</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersContent}
        >
          {renderFilterButton('all', 'Все')}
          {renderFilterButton('new', 'Новые')}
          {renderFilterButton('anonymous', 'Анонимные')}
        </ScrollView>
      </View>

      {/* Content */}
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredGifts.length > 0 ? (
          <View style={styles.giftsContainer}>
            {filteredGifts.map(renderGiftItem)}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="card-giftcard" size={64} color="#cccccc" />
            <Text style={styles.emptyTitle}>
              {filter === 'all' ? 'Нет подарков' : 
               filter === 'new' ? 'Нет новых подарков' : 
               'Нет анонимных подарков'}
            </Text>
            <Text style={styles.emptyDescription}>
              {filter === 'all' 
                ? 'Когда вам пришлют подарки, они появятся здесь'
                : 'Попробуйте выбрать другой фильтр'
              }
            </Text>
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  filtersContainer: {
    backgroundColor: '#f8f8f8',
    paddingVertical: 12,
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  filterButtonActive: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterButtonTextActive: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
  },
  giftsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  giftItem: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  giftImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  giftImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  countBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#E91E63',
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  countText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  newIndicator: {
    position: 'absolute',
    top: 4,
    left: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#4CAF50',
  },
  giftContent: {
    flex: 1,
  },
  giftHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  giftName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  giftTime: {
    fontSize: 12,
    color: '#999999',
  },
  senderInfo: {
    marginBottom: 8,
  },
  anonymousSender: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  knownSender: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  senderAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  senderAvatarPlaceholder: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  senderName: {
    fontSize: 14,
    color: '#666666',
  },
  giftMessage: {
    fontSize: 14,
    color: '#333333',
    fontStyle: 'italic',
    marginBottom: 8,
    lineHeight: 18,
  },
  giftPrice: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priceText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFD700',
  },
  thankButton: {
    padding: 8,
    alignSelf: 'flex-start',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomPadding: {
    height: 40,
  },
});

export default ReceivedGiftsScreen;
