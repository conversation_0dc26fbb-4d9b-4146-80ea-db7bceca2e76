import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  Platform,
  PermissionsAndroid
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker, Circle } from 'react-native-maps';
import * as Location from 'expo-location';
import { YaMap, Marker as YaMarker, Circle as YaCircle } from 'react-native-yamap';

const { width, height } = Dimensions.get('window');

interface MeetingMapScreenProps {}

interface MeetingLocation {
  id: string;
  title: string;
  description: string;
  latitude: number;
  longitude: number;
  address: string;
  category: string;
  rating: number;
  isSelected: boolean;
  distance?: number;
  estimatedTime?: string;
}

interface UserLocation {
  latitude: number;
  longitude: number;
  accuracy: number;
}

const MeetingMapScreen: React.FC<MeetingMapScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { meetingId, mode = 'view' } = route.params as { meetingId?: string; mode?: 'view' | 'select' };

  const mapRef = useRef<MapView>(null);
  const [loading, setLoading] = useState(true);
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<MeetingLocation | null>(null);
  const [nearbyLocations, setNearbyLocations] = useState<MeetingLocation[]>([]);
  const [mapType, setMapType] = useState<'standard' | 'satellite' | 'hybrid'>('standard');
  const [showUserLocation, setShowUserLocation] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeMap();
  }, []);

  const initializeMap = async () => {
    try {
      setLoading(true);
      setError(null);

      // Request location permissions
      const hasPermission = await requestLocationPermission();
      if (!hasPermission) {
        setError('Для работы карты необходимо разрешение на геолокацию');
        setLoading(false);
        return;
      }

      // Get user location
      await getCurrentLocation();

      // Load meeting data or nearby locations
      if (meetingId) {
        await loadMeetingLocation();
      } else {
        await loadNearbyLocations();
      }
    } catch (err: any) {
      setError('Ошибка загрузки карты');
      console.error('Map initialization error:', err);
    } finally {
      setLoading(false);
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  };

  const getCurrentLocation = async (): Promise<void> => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        timeInterval: 10000,
        distanceInterval: 10
      });

      const { latitude, longitude, accuracy } = location.coords;
      setUserLocation({ latitude, longitude, accuracy: accuracy || 1000 });
    } catch (error) {
      console.error('Geolocation error:', error);
      // Set default location (Moscow)
      setUserLocation({
        latitude: 55.7558,
        longitude: 37.6176,
        accuracy: 1000
      });
    }
  };

  const loadMeetingLocation = async () => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockMeetingLocation: MeetingLocation = {
        id: meetingId!,
        title: 'Кафе "Встреча"',
        description: 'Уютное место для романтического свидания',
        latitude: 55.7558,
        longitude: 37.6176,
        address: 'ул. Тверская, 15, Москва',
        category: 'cafe',
        rating: 4.5,
        isSelected: true,
        distance: 1.2,
        estimatedTime: '15 мин'
      };

      setSelectedLocation(mockMeetingLocation);

      // Center map on meeting location
      if (mapRef.current) {
        mapRef.current.animateToRegion({
          latitude: mockMeetingLocation.latitude,
          longitude: mockMeetingLocation.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01
        }, 1000);
      }
    } catch (error) {
      setError('Ошибка загрузки места встречи');
    }
  };

  const loadNearbyLocations = async () => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockLocations: MeetingLocation[] = [
        {
          id: '1',
          title: 'Кафе "Встреча"',
          description: 'Уютное место для романтического свидания',
          latitude: 55.7558,
          longitude: 37.6176,
          address: 'ул. Тверская, 15',
          category: 'cafe',
          rating: 4.5,
          isSelected: false,
          distance: 0.5,
          estimatedTime: '7 мин'
        },
        {
          id: '2',
          title: 'Ресторан "Романтика"',
          description: 'Изысканная кухня и атмосфера',
          latitude: 55.7608,
          longitude: 37.6194,
          address: 'ул. Пушкинская, 8',
          category: 'restaurant',
          rating: 4.8,
          isSelected: false,
          distance: 0.8,
          estimatedTime: '12 мин'
        },
        {
          id: '3',
          title: 'Парк "Сокольники"',
          description: 'Прогулка на свежем воздухе',
          latitude: 55.7894,
          longitude: 37.6706,
          address: 'Сокольнический Вал, 1',
          category: 'park',
          rating: 4.3,
          isSelected: false,
          distance: 2.1,
          estimatedTime: '25 мин'
        }
      ];

      setNearbyLocations(mockLocations);
    } catch (error) {
      setError('Ошибка загрузки мест');
    }
  };

  const handleLocationSelect = (location: MeetingLocation) => {
    if (mode === 'select') {
      setSelectedLocation(location);
      setNearbyLocations(prev =>
        prev.map(loc => ({ ...loc, isSelected: loc.id === location.id }))
      );
    }

    // Center map on selected location
    if (mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: location.latitude,
        longitude: location.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01
      }, 1000);
    }
  };

  const handleConfirmLocation = () => {
    if (selectedLocation && mode === 'select') {
      navigation.goBack();
      // TODO: Pass selected location back to previous screen
    }
  };

  const handleGetDirections = () => {
    if (selectedLocation) {
      const url = `https://maps.google.com/?daddr=${selectedLocation.latitude},${selectedLocation.longitude}`;
      // TODO: Open in maps app
      Alert.alert('Навигация', 'Открыть в картах Google?');
    }
  };

  const handleCenterOnUser = () => {
    if (userLocation && mapRef.current) {
      mapRef.current.animateToRegion({
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01
      }, 1000);
    }
  };

  const getMarkerColor = (category: string): string => {
    switch (category) {
      case 'cafe':
        return '#FF9800';
      case 'restaurant':
        return '#E91E63';
      case 'bar':
        return '#9C27B0';
      case 'park':
        return '#4CAF50';
      case 'cinema':
        return '#2196F3';
      default:
        return '#757575';
    }
  };

  const getCategoryIcon = (category: string): string => {
    switch (category) {
      case 'cafe':
        return 'local-cafe';
      case 'restaurant':
        return 'restaurant';
      case 'bar':
        return 'local-bar';
      case 'park':
        return 'park';
      case 'cinema':
        return 'movie';
      default:
        return 'place';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка карты...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={initializeMap}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>

        <Text style={styles.headerTitle}>
          {mode === 'select' ? 'Выберите место' : 'Карта встречи'}
        </Text>

        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setMapType(prev =>
              prev === 'standard' ? 'satellite' :
              prev === 'satellite' ? 'hybrid' : 'standard'
            )}
          >
            <Icon name="layers" size={24} color="#333333" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Map */}
      <View style={styles.mapContainer}>
        <YaMap
          ref={mapRef}
          style={styles.map}
          mapType={mapType === 'satellite' ? 'satellite' : 'vector'}
          showUserLocation={showUserLocation}
          userLocationAccuracyFillColor="rgba(233, 30, 99, 0.2)"
          userLocationAccuracyStrokeColor="rgba(233, 30, 99, 0.5)"
          userLocationAccuracyStrokeWidth={2}
          initialRegion={{
            lat: userLocation?.latitude || 55.7558,
            lon: userLocation?.longitude || 37.6176,
            zoom: 15,
          }}
        >
          {/* Selected location marker */}
          {selectedLocation && (
            <YaMarker
              point={{
                lat: selectedLocation.latitude,
                lon: selectedLocation.longitude,
              }}
              onPress={() => handleLocationSelect(selectedLocation)}
              children={
                <View style={[styles.markerContainer, { backgroundColor: getMarkerColor(selectedLocation.category) }]}>
                  <Icon
                    name={getCategoryIcon(selectedLocation.category)}
                    size={20}
                    color="#ffffff"
                  />
                </View>
              }
            />
          )}

          {/* Nearby locations markers */}
          {nearbyLocations.map((location) => (
            <YaMarker
              key={location.id}
              point={{
                lat: location.latitude,
                lon: location.longitude,
              }}
              onPress={() => handleLocationSelect(location)}
              children={
                <View style={[styles.markerContainer, { backgroundColor: getMarkerColor(location.category) }]}>
                  <Icon
                    name={getCategoryIcon(location.category)}
                    size={20}
                    color="#ffffff"
                  />
                </View>
              }
            />
          ))}
        </YaMap>

        {/* Map Controls */}
        <View style={styles.mapControls}>
          <TouchableOpacity
            style={styles.mapControlButton}
            onPress={handleCenterOnUser}
          >
            <Icon name="my-location" size={24} color="#E91E63" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Location Info */}
      {selectedLocation && (
        <View style={styles.locationInfo}>
          <View style={styles.locationHeader}>
            <View style={styles.locationIcon}>
              <Icon
                name={getCategoryIcon(selectedLocation.category)}
                size={24}
                color={getMarkerColor(selectedLocation.category)}
              />
            </View>

            <View style={styles.locationDetails}>
              <Text style={styles.locationTitle}>{selectedLocation.title}</Text>
              <Text style={styles.locationDescription}>{selectedLocation.description}</Text>
              <Text style={styles.locationAddress}>{selectedLocation.address}</Text>

              <View style={styles.locationMeta}>
                <View style={styles.locationRating}>
                  <Icon name="star" size={16} color="#FFD700" />
                  <Text style={styles.locationRatingText}>{selectedLocation.rating}</Text>
                </View>

                {selectedLocation.distance && (
                  <View style={styles.locationDistance}>
                    <Icon name="directions-walk" size={16} color="#666666" />
                    <Text style={styles.locationDistanceText}>
                      {selectedLocation.distance} км • {selectedLocation.estimatedTime}
                    </Text>
                  </View>
                )}
              </View>
            </View>
          </View>

          <View style={styles.locationActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={handleGetDirections}
            >
              <Icon name="directions" size={20} color="#E91E63" />
              <Text style={styles.actionButtonText}>Маршрут</Text>
            </TouchableOpacity>

            {mode === 'select' && (
              <TouchableOpacity
                style={[styles.actionButton, styles.primaryActionButton]}
                onPress={handleConfirmLocation}
              >
                <Icon name="check" size={20} color="#ffffff" />
                <Text style={[styles.actionButtonText, styles.primaryActionButtonText]}>
                  Выбрать
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Nearby Locations List */}
      {mode === 'select' && nearbyLocations.length > 0 && !selectedLocation && (
        <View style={styles.nearbyLocations}>
          <Text style={styles.nearbyTitle}>Рекомендуемые места</Text>

          {nearbyLocations.slice(0, 3).map((location) => (
            <TouchableOpacity
              key={location.id}
              style={styles.nearbyLocationItem}
              onPress={() => handleLocationSelect(location)}
            >
              <View style={styles.nearbyLocationIcon}>
                <Icon
                  name={getCategoryIcon(location.category)}
                  size={20}
                  color={getMarkerColor(location.category)}
                />
              </View>

              <View style={styles.nearbyLocationDetails}>
                <Text style={styles.nearbyLocationTitle}>{location.title}</Text>
                <Text style={styles.nearbyLocationAddress}>{location.address}</Text>

                <View style={styles.nearbyLocationMeta}>
                  <View style={styles.nearbyLocationRating}>
                    <Icon name="star" size={12} color="#FFD700" />
                    <Text style={styles.nearbyLocationRatingText}>{location.rating}</Text>
                  </View>

                  <Text style={styles.nearbyLocationDistance}>
                    {location.distance} км
                  </Text>
                </View>
              </View>

              <Icon name="chevron-right" size={20} color="#666666" />
            </TouchableOpacity>
          ))}
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapControls: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  mapControlButton: {
    backgroundColor: '#ffffff',
    borderRadius: 25,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  locationInfo: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingVertical: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  locationHeader: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  locationIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationDetails: {
    flex: 1,
  },
  locationTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  locationDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 8,
  },
  locationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  locationRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationRatingText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 4,
  },
  locationDistance: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationDistanceText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
  },
  locationActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 6,
  },
  primaryActionButton: {
    backgroundColor: '#E91E63',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  primaryActionButtonText: {
    color: '#ffffff',
  },
  nearbyLocations: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingHorizontal: 20,
    paddingTop: 16,
    maxHeight: height * 0.4,
  },
  nearbyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  nearbyLocationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  nearbyLocationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  nearbyLocationDetails: {
    flex: 1,
  },
  nearbyLocationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  nearbyLocationAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  nearbyLocationMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  nearbyLocationRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nearbyLocationRatingText: {
    fontSize: 12,
    color: '#333333',
    marginLeft: 2,
  },
  nearbyLocationDistance: {
    fontSize: 12,
    color: '#666666',
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
});

export default MeetingMapScreen;