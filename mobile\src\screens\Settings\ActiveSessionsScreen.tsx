import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Modal,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Device from 'expo-device';
import * as Location from 'expo-location';

const { width } = Dimensions.get('window');

interface ActiveSessionsScreenProps {}

interface ActiveSession {
  id: string;
  deviceInfo: {
    name: string;
    type: 'mobile' | 'desktop' | 'tablet' | 'web';
    os: string;
    browser?: string;
    model?: string;
    brand?: string;
  };
  location: {
    city: string;
    country: string;
    region?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    ipAddress: string;
  };
  activity: {
    lastActive: string;
    loginTime: string;
    isCurrentSession: boolean;
    status: 'active' | 'idle' | 'expired';
  };
  security: {
    isTrusted: boolean;
    loginMethod: 'password' | 'biometric' | 'social' | '2fa';
    riskLevel: 'low' | 'medium' | 'high';
    suspiciousActivity?: string[];
  };
  app: {
    version: string;
    platform: string;
    userAgent?: string;
  };
}

interface SecurityAlert {
  id: string;
  type: 'new_login' | 'suspicious_activity' | 'location_change' | 'device_change';
  title: string;
  description: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high';
  isRead: boolean;
  sessionId?: string;
}

const ActiveSessionsScreen: React.FC<ActiveSessionsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sessions, setSessions] = useState<ActiveSession[]>([]);
  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [selectedSession, setSelectedSession] = useState<ActiveSession | null>(null);
  const [showSessionModal, setShowSessionModal] = useState(false);
  const [terminatingSession, setTerminatingSession] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadActiveSessions();
      loadSecurityAlerts();
    }, [])
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await Promise.all([loadActiveSessions(), loadSecurityAlerts()]);
    setRefreshing(false);
  }, []);

  const loadActiveSessions = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current device info
      const deviceInfo = {
        name: Device.deviceName || 'Unknown Device',
        type: Device.deviceType === Device.DeviceType.PHONE ? 'mobile' as const :
              Device.deviceType === Device.DeviceType.TABLET ? 'tablet' as const : 'mobile' as const,
        os: Device.osName || 'Unknown OS',
        model: Device.modelName,
        brand: Device.brand
      };

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockSessions: ActiveSession[] = [
        {
          id: 'current',
          deviceInfo: {
            ...deviceInfo,
            name: deviceInfo.name || 'Текущее устройство'
          },
          location: {
            city: 'Москва',
            country: 'Россия',
            region: 'Московская область',
            coordinates: {
              latitude: 55.7558,
              longitude: 37.6176
            },
            ipAddress: '*************'
          },
          activity: {
            lastActive: new Date().toISOString(),
            loginTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            isCurrentSession: true,
            status: 'active'
          },
          security: {
            isTrusted: true,
            loginMethod: 'biometric',
            riskLevel: 'low'
          },
          app: {
            version: '1.0.0',
            platform: 'React Native'
          }
        },
        {
          id: 'web-session-1',
          deviceInfo: {
            name: 'MacBook Pro',
            type: 'desktop',
            os: 'macOS',
            browser: 'Chrome 120.0'
          },
          location: {
            city: 'Москва',
            country: 'Россия',
            ipAddress: '*************'
          },
          activity: {
            lastActive: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            loginTime: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
            isCurrentSession: false,
            status: 'idle'
          },
          security: {
            isTrusted: true,
            loginMethod: 'password',
            riskLevel: 'low'
          },
          app: {
            version: '1.0.0',
            platform: 'Web',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)'
          }
        },
        {
          id: 'mobile-session-1',
          deviceInfo: {
            name: 'iPhone 14',
            type: 'mobile',
            os: 'iOS',
            model: 'iPhone 14',
            brand: 'Apple'
          },
          location: {
            city: 'Санкт-Петербург',
            country: 'Россия',
            ipAddress: '***********'
          },
          activity: {
            lastActive: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            loginTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            isCurrentSession: false,
            status: 'idle'
          },
          security: {
            isTrusted: false,
            loginMethod: 'password',
            riskLevel: 'medium',
            suspiciousActivity: ['Необычное местоположение']
          },
          app: {
            version: '0.9.8',
            platform: 'iOS'
          }
        }
      ];

      setSessions(mockSessions);
    } catch (err: any) {
      setError('Ошибка загрузки активных сессий');
      console.error('Active sessions loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadSecurityAlerts = async () => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockAlerts: SecurityAlert[] = [
        {
          id: '1',
          type: 'new_login',
          title: 'Новый вход в аккаунт',
          description: 'Вход выполнен с устройства iPhone 14 из Санкт-Петербурга',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          severity: 'medium',
          isRead: false,
          sessionId: 'mobile-session-1'
        },
        {
          id: '2',
          type: 'location_change',
          title: 'Изменение местоположения',
          description: 'Обнаружен вход из нового города: Санкт-Петербург',
          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          severity: 'low',
          isRead: true
        }
      ];

      setAlerts(mockAlerts);
    } catch (error) {
      console.error('Security alerts loading error:', error);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    if (sessionId === 'current') {
      Alert.alert('Ошибка', 'Нельзя завершить текущую сессию');
      return;
    }

    Alert.alert(
      'Завершить сессию',
      'Вы уверены, что хотите завершить эту сессию? Пользователь будет отключен от аккаунта.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Завершить',
          style: 'destructive',
          onPress: async () => {
            try {
              setTerminatingSession(sessionId);

              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 1000));

              setSessions(prev => prev.filter(session => session.id !== sessionId));
              Alert.alert('Успех', 'Сессия завершена');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось завершить сессию');
            } finally {
              setTerminatingSession(null);
            }
          }
        }
      ]
    );
  };

  const handleTerminateAllOtherSessions = () => {
    const otherSessions = sessions.filter(session => !session.activity.isCurrentSession);

    if (otherSessions.length === 0) {
      Alert.alert('Информация', 'Нет других активных сессий');
      return;
    }

    Alert.alert(
      'Завершить все сессии',
      `Вы уверены, что хотите завершить все остальные сессии (${otherSessions.length})?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Завершить все',
          style: 'destructive',
          onPress: async () => {
            try {
              setTerminatingSession('all');

              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 1500));

              setSessions(prev => prev.filter(session => session.activity.isCurrentSession));
              Alert.alert('Успех', `Завершено ${otherSessions.length} сессий`);
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось завершить сессии');
            } finally {
              setTerminatingSession(null);
            }
          }
        }
      ]
    );
  };

  const handleMarkAlertAsRead = async (alertId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 200));

      setAlerts(prev => prev.map(alert =>
        alert.id === alertId ? { ...alert, isRead: true } : alert
      ));
    } catch (error) {
      console.error('Mark alert as read error:', error);
    }
  };

  const handleSessionPress = (session: ActiveSession) => {
    setSelectedSession(session);
    setShowSessionModal(true);
  };

  const getDeviceIcon = (type: string): string => {
    switch (type) {
      case 'mobile': return 'smartphone';
      case 'tablet': return 'tablet';
      case 'desktop': return 'computer';
      case 'web': return 'language';
      default: return 'device-unknown';
    }
  };

  const getRiskLevelColor = (level: string): string => {
    switch (level) {
      case 'low': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'high': return '#F44336';
      default: return '#666666';
    }
  };

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'low': return '#4CAF50';
      case 'medium': return '#FF9800';
      case 'high': return '#F44336';
      default: return '#666666';
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Только что';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} мин. назад`;
    } else if (diffInMinutes < 24 * 60) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} ч. назад`;
    } else {
      const days = Math.floor(diffInMinutes / (24 * 60));
      return `${days} дн. назад`;
    }
  };

  const getLoginMethodLabel = (method: string): string => {
    switch (method) {
      case 'password': return 'Пароль';
      case 'biometric': return 'Биометрия';
      case 'social': return 'Соц. сети';
      case '2fa': return '2FA';
      default: return method;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка активных сессий...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadActiveSessions}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const unreadAlerts = alerts.filter(alert => !alert.isRead);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Активные сессии</Text>
        <TouchableOpacity
          style={styles.terminateAllButton}
          onPress={handleTerminateAllOtherSessions}
          disabled={terminatingSession === 'all'}
        >
          {terminatingSession === 'all' ? (
            <ActivityIndicator size="small" color="#F44336" />
          ) : (
            <Icon name="logout" size={24} color="#F44336" />
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#E91E63']}
          />
        }
      >
        {/* Security Alerts */}
        {unreadAlerts.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Уведомления безопасности</Text>
            {unreadAlerts.map((alert) => (
              <TouchableOpacity
                key={alert.id}
                style={styles.alertCard}
                onPress={() => handleMarkAlertAsRead(alert.id)}
              >
                <View style={styles.alertHeader}>
                  <Icon
                    name={
                      alert.type === 'new_login' ? 'login' :
                      alert.type === 'suspicious_activity' ? 'warning' :
                      alert.type === 'location_change' ? 'location-on' :
                      'devices'
                    }
                    size={20}
                    color={getSeverityColor(alert.severity)}
                  />
                  <Text style={styles.alertTitle}>{alert.title}</Text>
                  <View style={[styles.severityBadge, { backgroundColor: getSeverityColor(alert.severity) }]}>
                    <Text style={styles.severityBadgeText}>
                      {alert.severity === 'low' ? 'Низкий' :
                       alert.severity === 'medium' ? 'Средний' : 'Высокий'}
                    </Text>
                  </View>
                </View>
                <Text style={styles.alertDescription}>{alert.description}</Text>
                <Text style={styles.alertTime}>{formatTimeAgo(alert.timestamp)}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Current Session */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Текущая сессия</Text>
          {(() => {
            const currentSession = sessions.find(session => session.activity.isCurrentSession);
            return currentSession ? (
              <TouchableOpacity
                style={[styles.sessionCard, styles.currentSessionCard]}
                onPress={() => handleSessionPress(currentSession)}
              >
                <View style={styles.sessionHeader}>
                  <Icon name={getDeviceIcon(currentSession.deviceInfo.type)} size={24} color="#E91E63" />
                  <View style={styles.sessionInfo}>
                    <Text style={styles.sessionDeviceName}>{currentSession.deviceInfo.name}</Text>
                    <Text style={styles.sessionLocation}>
                      {currentSession.location.city}, {currentSession.location.country}
                    </Text>
                  </View>
                  <View style={styles.currentSessionBadge}>
                    <Text style={styles.currentSessionBadgeText}>Текущая</Text>
                  </View>
                </View>

                <View style={styles.sessionDetails}>
                  <Text style={styles.sessionDetailText}>
                    {currentSession.deviceInfo.os} • {currentSession.app.platform} {currentSession.app.version}
                  </Text>
                  <Text style={styles.sessionDetailText}>
                    Вход: {getLoginMethodLabel(currentSession.security.loginMethod)}
                  </Text>
                  <Text style={styles.sessionDetailText}>
                    Активность: {formatTimeAgo(currentSession.activity.lastActive)}
                  </Text>
                </View>
              </TouchableOpacity>
            ) : null;
          })()}
        </View>

        {/* Other Sessions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Другие сессии</Text>
          {sessions.filter(session => !session.activity.isCurrentSession).map((session) => (
            <TouchableOpacity
              key={session.id}
              style={[
                styles.sessionCard,
                !session.security.isTrusted && styles.untrustedSessionCard
              ]}
              onPress={() => handleSessionPress(session)}
            >
              <View style={styles.sessionHeader}>
                <Icon name={getDeviceIcon(session.deviceInfo.type)} size={24} color="#666666" />
                <View style={styles.sessionInfo}>
                  <Text style={styles.sessionDeviceName}>{session.deviceInfo.name}</Text>
                  <Text style={styles.sessionLocation}>
                    {session.location.city}, {session.location.country}
                  </Text>
                </View>

                <View style={styles.sessionActions}>
                  <View style={[styles.riskBadge, { backgroundColor: getRiskLevelColor(session.security.riskLevel) }]}>
                    <Text style={styles.riskBadgeText}>
                      {session.security.riskLevel === 'low' ? 'Низкий' :
                       session.security.riskLevel === 'medium' ? 'Средний' : 'Высокий'}
                    </Text>
                  </View>

                  <TouchableOpacity
                    style={styles.terminateButton}
                    onPress={() => handleTerminateSession(session.id)}
                    disabled={terminatingSession === session.id}
                  >
                    {terminatingSession === session.id ? (
                      <ActivityIndicator size="small" color="#F44336" />
                    ) : (
                      <Icon name="close" size={20} color="#F44336" />
                    )}
                  </TouchableOpacity>
                </View>
              </View>

              <View style={styles.sessionDetails}>
                <Text style={styles.sessionDetailText}>
                  {session.deviceInfo.os}
                  {session.deviceInfo.browser && ` • ${session.deviceInfo.browser}`}
                </Text>
                <Text style={styles.sessionDetailText}>
                  Последняя активность: {formatTimeAgo(session.activity.lastActive)}
                </Text>
                <Text style={styles.sessionDetailText}>
                  Вход: {formatTimeAgo(session.activity.loginTime)} • {getLoginMethodLabel(session.security.loginMethod)}
                </Text>

                {session.security.suspiciousActivity && session.security.suspiciousActivity.length > 0 && (
                  <View style={styles.suspiciousActivity}>
                    <Icon name="warning" size={16} color="#FF9800" />
                    <Text style={styles.suspiciousActivityText}>
                      {session.security.suspiciousActivity.join(', ')}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Session Detail Modal */}
      <Modal
        visible={showSessionModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowSessionModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Детали сессии</Text>
              <TouchableOpacity onPress={() => setShowSessionModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            {selectedSession && (
              <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Устройство</Text>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Название:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.deviceInfo.name}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Тип:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.deviceInfo.type}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>ОС:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.deviceInfo.os}</Text>
                  </View>
                  {selectedSession.deviceInfo.browser && (
                    <View style={styles.modalDetailRow}>
                      <Text style={styles.modalDetailLabel}>Браузер:</Text>
                      <Text style={styles.modalDetailValue}>{selectedSession.deviceInfo.browser}</Text>
                    </View>
                  )}
                </View>

                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Местоположение</Text>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Город:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.location.city}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Страна:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.location.country}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>IP адрес:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.location.ipAddress}</Text>
                  </View>
                </View>

                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Активность</Text>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Статус:</Text>
                    <Text style={[
                      styles.modalDetailValue,
                      { color: selectedSession.activity.status === 'active' ? '#4CAF50' : '#FF9800' }
                    ]}>
                      {selectedSession.activity.status === 'active' ? 'Активна' : 'Неактивна'}
                    </Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Последняя активность:</Text>
                    <Text style={styles.modalDetailValue}>
                      {formatTimeAgo(selectedSession.activity.lastActive)}
                    </Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Время входа:</Text>
                    <Text style={styles.modalDetailValue}>
                      {formatTimeAgo(selectedSession.activity.loginTime)}
                    </Text>
                  </View>
                </View>

                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Безопасность</Text>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Доверенное устройство:</Text>
                    <Text style={[
                      styles.modalDetailValue,
                      { color: selectedSession.security.isTrusted ? '#4CAF50' : '#F44336' }
                    ]}>
                      {selectedSession.security.isTrusted ? 'Да' : 'Нет'}
                    </Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Метод входа:</Text>
                    <Text style={styles.modalDetailValue}>
                      {getLoginMethodLabel(selectedSession.security.loginMethod)}
                    </Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Уровень риска:</Text>
                    <Text style={[
                      styles.modalDetailValue,
                      { color: getRiskLevelColor(selectedSession.security.riskLevel) }
                    ]}>
                      {selectedSession.security.riskLevel === 'low' ? 'Низкий' :
                       selectedSession.security.riskLevel === 'medium' ? 'Средний' : 'Высокий'}
                    </Text>
                  </View>
                </View>

                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Приложение</Text>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Платформа:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.app.platform}</Text>
                  </View>
                  <View style={styles.modalDetailRow}>
                    <Text style={styles.modalDetailLabel}>Версия:</Text>
                    <Text style={styles.modalDetailValue}>{selectedSession.app.version}</Text>
                  </View>
                </View>
              </ScrollView>
            )}

            {selectedSession && !selectedSession.activity.isCurrentSession && (
              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.modalTerminateButton}
                  onPress={() => {
                    setShowSessionModal(false);
                    handleTerminateSession(selectedSession.id);
                  }}
                >
                  <Icon name="logout" size={20} color="#ffffff" />
                  <Text style={styles.modalTerminateButtonText}>Завершить сессию</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  terminateAllButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  alertCard: {
    backgroundColor: '#fff3e0',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  alertHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    marginLeft: 8,
  },
  severityBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  severityBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  alertDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  alertTime: {
    fontSize: 12,
    color: '#999999',
  },
  sessionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  currentSessionCard: {
    borderColor: '#E91E63',
    backgroundColor: '#E91E63' + '05',
  },
  untrustedSessionCard: {
    borderColor: '#FF9800',
    backgroundColor: '#fff3e0',
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  sessionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  sessionDeviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  sessionLocation: {
    fontSize: 14,
    color: '#666666',
  },
  currentSessionBadge: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  currentSessionBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  sessionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  riskBadge: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  riskBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  terminateButton: {
    backgroundColor: '#F44336' + '10',
    borderRadius: 8,
    padding: 6,
  },
  sessionDetails: {
    gap: 4,
  },
  sessionDetailText: {
    fontSize: 12,
    color: '#666666',
  },
  suspiciousActivity: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff3e0',
    borderRadius: 8,
    padding: 8,
    marginTop: 8,
  },
  suspiciousActivityText: {
    fontSize: 12,
    color: '#FF9800',
    marginLeft: 6,
    flex: 1,
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  modalBody: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modalSection: {
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  modalDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalDetailLabel: {
    fontSize: 14,
    color: '#666666',
    flex: 1,
  },
  modalDetailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    flex: 1,
    textAlign: 'right',
  },
  modalActions: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  modalTerminateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F44336',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  modalTerminateButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default ActiveSessionsScreen;