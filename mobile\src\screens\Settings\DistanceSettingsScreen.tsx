import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Alert,
  Switch
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Slider from '@react-native-community/slider';

interface DistanceSettingsScreenProps {}

interface RouteParams {
  currentDistance: number;
  onSave: (distance: number) => void;
}

const DistanceSettingsScreen: React.FC<DistanceSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { currentDistance, onSave } = (route.params as RouteParams) || {
    currentDistance: 50,
    onSave: () => {}
  };

  const [distance, setDistance] = useState(currentDistance);
  const [globalSearch, setGlobalSearch] = useState(currentDistance >= 1000);
  const [saving, setSaving] = useState(false);

  const MIN_DISTANCE = 1;
  const MAX_DISTANCE = 500;

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const finalDistance = globalSearch ? 1000 : distance;
      onSave(finalDistance);
      navigation.goBack();
    } catch (error) {
      console.error('Error saving distance:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleDistanceChange = (value: number) => {
    setDistance(Math.round(value));
  };

  const handleGlobalSearchToggle = (value: boolean) => {
    setGlobalSearch(value);
    if (value) {
      setDistance(MAX_DISTANCE);
    }
  };

  const handlePresetSelect = (presetDistance: number) => {
    setDistance(presetDistance);
    setGlobalSearch(false);
  };

  const presets = [
    { label: '5 км', value: 5, description: 'Очень близко' },
    { label: '15 км', value: 15, description: 'В городе' },
    { label: '30 км', value: 30, description: 'В области' },
    { label: '50 км', value: 50, description: 'Рекомендуемо' },
    { label: '100 км', value: 100, description: 'Широкий поиск' },
    { label: '200 км', value: 200, description: 'Очень широко' }
  ];

  const getDistanceDescription = (dist: number): string => {
    if (dist <= 5) return 'Только ближайшие районы';
    if (dist <= 15) return 'В пределах города';
    if (dist <= 30) return 'Город и пригороды';
    if (dist <= 50) return 'Оптимальное расстояние';
    if (dist <= 100) return 'Широкая область поиска';
    return 'Очень широкая область';
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Максимальное расстояние</Text>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          disabled={saving}
        >
          <Text style={[
            styles.saveButtonText,
            saving && styles.saveButtonTextDisabled
          ]}>
            {saving ? 'Сохранение...' : 'Сохранить'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Current Distance Display */}
        <View style={styles.distanceDisplay}>
          <Text style={styles.distanceTitle}>Показывать анкеты в радиусе:</Text>
          <Text style={styles.distanceValue}>
            {globalSearch ? 'Весь мир' : `${distance} км`}
          </Text>
          <Text style={styles.distanceDescription}>
            {globalSearch ? 'Поиск по всему миру' : getDistanceDescription(distance)}
          </Text>
        </View>

        {/* Global Search Toggle */}
        <View style={styles.globalSearchSection}>
          <View style={styles.globalSearchHeader}>
            <View style={styles.globalSearchInfo}>
              <Text style={styles.globalSearchTitle}>Глобальный поиск</Text>
              <Text style={styles.globalSearchDescription}>
                Показывать анкеты со всего мира
              </Text>
            </View>
            <Switch
              value={globalSearch}
              onValueChange={handleGlobalSearchToggle}
              trackColor={{ false: '#f0f0f0', true: '#E91E63' + '40' }}
              thumbColor={globalSearch ? '#E91E63' : '#cccccc'}
            />
          </View>
        </View>

        {/* Distance Slider */}
        {!globalSearch && (
          <View style={styles.sliderSection}>
            <View style={styles.sliderHeader}>
              <Text style={styles.sliderLabel}>Расстояние</Text>
              <Text style={styles.sliderValue}>{distance} км</Text>
            </View>
            <Slider
              style={styles.slider}
              minimumValue={MIN_DISTANCE}
              maximumValue={MAX_DISTANCE}
              value={distance}
              onValueChange={handleDistanceChange}
              step={1}
              minimumTrackTintColor="#E91E63"
              maximumTrackTintColor="#e0e0e0"
              thumbStyle={styles.sliderThumb}
            />
            <View style={styles.sliderRange}>
              <Text style={styles.sliderRangeText}>{MIN_DISTANCE} км</Text>
              <Text style={styles.sliderRangeText}>{MAX_DISTANCE} км</Text>
            </View>
          </View>
        )}

        {/* Presets */}
        {!globalSearch && (
          <View style={styles.presetsSection}>
            <Text style={styles.presetsTitle}>Быстрый выбор</Text>
            <View style={styles.presetsContainer}>
              {presets.map((preset) => {
                const isSelected = distance === preset.value;
                
                return (
                  <TouchableOpacity
                    key={preset.value}
                    style={[
                      styles.presetCard,
                      isSelected && styles.presetCardSelected
                    ]}
                    onPress={() => handlePresetSelect(preset.value)}
                  >
                    <Text style={[
                      styles.presetLabel,
                      isSelected && styles.presetLabelSelected
                    ]}>
                      {preset.label}
                    </Text>
                    <Text style={[
                      styles.presetDescription,
                      isSelected && styles.presetDescriptionSelected
                    ]}>
                      {preset.description}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        )}

        {/* Info */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Icon name="info" size={20} color="#2196F3" />
            <Text style={styles.infoText}>
              {globalSearch 
                ? 'При глобальном поиске вы будете видеть анкеты пользователей со всего мира. Это может увеличить количество совпадений, но усложнить встречи.'
                : 'Расстояние рассчитывается от вашего текущего местоположения. Чем больше радиус, тем больше анкет вы увидите.'
              }
            </Text>
          </View>
        </View>

        {/* Location Permission Info */}
        <View style={styles.locationSection}>
          <View style={styles.locationCard}>
            <Icon name="location-on" size={20} color="#FF9800" />
            <Text style={styles.locationText}>
              Для точного расчета расстояния необходимо разрешение на доступ к геолокации
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  saveButtonTextDisabled: {
    color: '#cccccc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  distanceDisplay: {
    alignItems: 'center',
    paddingVertical: 40,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  distanceTitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 8,
    textAlign: 'center',
  },
  distanceValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#E91E63',
    marginBottom: 12,
  },
  distanceDescription: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },
  globalSearchSection: {
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  globalSearchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  globalSearchInfo: {
    flex: 1,
  },
  globalSearchTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  globalSearchDescription: {
    fontSize: 14,
    color: '#666666',
  },
  sliderSection: {
    paddingVertical: 40,
  },
  sliderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sliderLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  sliderValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#E91E63',
    width: 20,
    height: 20,
  },
  sliderRange: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sliderRangeText: {
    fontSize: 12,
    color: '#999999',
  },
  presetsSection: {
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  presetsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  presetsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  presetCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    alignItems: 'center',
  },
  presetCardSelected: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  presetLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  presetLabelSelected: {
    color: '#ffffff',
  },
  presetDescription: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  presetDescriptionSelected: {
    color: '#ffffff' + 'CC',
  },
  infoSection: {
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    padding: 16,
    borderRadius: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
    marginLeft: 12,
  },
  locationSection: {
    paddingVertical: 16,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF3E0',
    padding: 16,
    borderRadius: 12,
  },
  locationText: {
    flex: 1,
    fontSize: 14,
    color: '#F57C00',
    lineHeight: 20,
    marginLeft: 12,
  },
});

export default DistanceSettingsScreen;
