import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  FlatList,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

interface RestaurantsScreenProps {}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisine: string[];
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    formattedAddress: string;
  };
  rating: {
    average: number;
    count: number;
    aspects: {
      food: number;
      service: number;
      atmosphere: number;
      price: number;
      cleanliness: number;
    };
  };
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  photos: Array<{
    id: string;
    url: string;
    thumbnailUrl: string;
  }>;
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  features: {
    delivery: boolean;
    takeaway: boolean;
    reservation: boolean;
    parking: boolean;
    terrace: boolean;
    liveMusic: boolean;
    wifi: boolean;
    kidsMenu: boolean;
    petFriendly: boolean;
    wheelchairAccessible: boolean;
  };
  menu: {
    hasBreakfast: boolean;
    hasLunch: boolean;
    hasDinner: boolean;
    hasVegan: boolean;
    hasHalal: boolean;
    hasAlcohol: boolean;
    hasKidsMenu: boolean;
  };
  distance?: number;
  estimatedTime?: string;
  isOpen?: boolean;
  isFavorite: boolean;
  tags: string[];
  averageCheck: {
    min: number;
    max: number;
    currency: string;
  };
  michelin?: {
    stars: number;
    guide: boolean;
  };
}

type SortOption = 'distance' | 'rating' | 'price' | 'name' | 'popularity';
type FilterOption = 'all' | 'open' | 'delivery' | 'reservation' | 'terrace' | 'vegan' | 'halal';
type ViewMode = 'list' | 'map';

const RestaurantsScreen: React.FC<RestaurantsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [filteredRestaurants, setFilteredRestaurants] = useState<Restaurant[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('distance');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [error, setError] = useState<string | null>(null);

  const filterOptions = [
    { id: 'all', name: 'Все', icon: 'restaurant' },
    { id: 'open', name: 'Открыто', icon: 'schedule' },
    { id: 'delivery', name: 'Доставка', icon: 'delivery-dining' },
    { id: 'reservation', name: 'Бронь', icon: 'event-seat' },
    { id: 'terrace', name: 'Терраса', icon: 'deck' },
    { id: 'vegan', name: 'Веган', icon: 'eco' },
    { id: 'halal', name: 'Халяль', icon: 'verified' },
  ];

  useFocusEffect(
    useCallback(() => {
      loadRestaurants();
      getCurrentLocation();
    }, [])
  );

  useEffect(() => {
    filterAndSortRestaurants();
  }, [restaurants, searchQuery, sortBy, filterBy]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }
    } catch (error) {
      console.log('Location error:', error);
    }
  };

  const loadRestaurants = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockRestaurants: Restaurant[] = [
        {
          id: '1',
          name: 'Bella Vista',
          description: 'Аутентичная итальянская кухня с видом на город',
          cuisine: ['Итальянская', 'Европейская'],
          location: {
            latitude: 55.7558,
            longitude: 37.6176,
            address: 'ул. Тверская, 25',
            city: 'Москва',
            formattedAddress: 'ул. Тверская, 25, Москва'
          },
          rating: {
            average: 4.7,
            count: 234,
            aspects: {
              food: 4.8,
              service: 4.6,
              atmosphere: 4.7,
              price: 4.2,
              cleanliness: 4.9
            }
          },
          priceRange: '$$$',
          photos: [
            {
              id: '1',
              url: 'https://example.com/restaurant1.jpg',
              thumbnailUrl: 'https://example.com/restaurant1_thumb.jpg'
            }
          ],
          workingHours: {
            monday: { open: '12:00', close: '23:00', isOpen: true },
            tuesday: { open: '12:00', close: '23:00', isOpen: true },
            wednesday: { open: '12:00', close: '23:00', isOpen: true },
            thursday: { open: '12:00', close: '23:00', isOpen: true },
            friday: { open: '12:00', close: '24:00', isOpen: true },
            saturday: { open: '12:00', close: '24:00', isOpen: true },
            sunday: { open: '12:00', close: '22:00', isOpen: true }
          },
          features: {
            delivery: true,
            takeaway: true,
            reservation: true,
            parking: true,
            terrace: true,
            liveMusic: false,
            wifi: true,
            kidsMenu: true,
            petFriendly: false,
            wheelchairAccessible: true
          },
          menu: {
            hasBreakfast: false,
            hasLunch: true,
            hasDinner: true,
            hasVegan: true,
            hasHalal: false,
            hasAlcohol: true,
            hasKidsMenu: true
          },
          distance: 1.2,
          estimatedTime: '15 мин',
          isOpen: true,
          isFavorite: true,
          tags: ['итальянская', 'терраса', 'романтично', 'бронирование'],
          averageCheck: {
            min: 1500,
            max: 3500,
            currency: 'RUB'
          }
        },
        {
          id: '2',
          name: 'Sushi Master',
          description: 'Свежие суши и роллы от японского шеф-повара',
          cuisine: ['Японская', 'Азиатская'],
          location: {
            latitude: 55.7489,
            longitude: 37.6176,
            address: 'ул. Арбат, 32',
            city: 'Москва',
            formattedAddress: 'ул. Арбат, 32, Москва'
          },
          rating: {
            average: 4.5,
            count: 156,
            aspects: {
              food: 4.7,
              service: 4.4,
              atmosphere: 4.3,
              price: 4.1,
              cleanliness: 4.8
            }
          },
          priceRange: '$$',
          photos: [
            {
              id: '2',
              url: 'https://example.com/restaurant2.jpg',
              thumbnailUrl: 'https://example.com/restaurant2_thumb.jpg'
            }
          ],
          workingHours: {
            monday: { open: '11:00', close: '22:00', isOpen: true },
            tuesday: { open: '11:00', close: '22:00', isOpen: true },
            wednesday: { open: '11:00', close: '22:00', isOpen: true },
            thursday: { open: '11:00', close: '22:00', isOpen: true },
            friday: { open: '11:00', close: '23:00', isOpen: true },
            saturday: { open: '11:00', close: '23:00', isOpen: true },
            sunday: { open: '11:00', close: '21:00', isOpen: true }
          },
          features: {
            delivery: true,
            takeaway: true,
            reservation: false,
            parking: false,
            terrace: false,
            liveMusic: false,
            wifi: true,
            kidsMenu: false,
            petFriendly: false,
            wheelchairAccessible: true
          },
          menu: {
            hasBreakfast: false,
            hasLunch: true,
            hasDinner: true,
            hasVegan: false,
            hasHalal: true,
            hasAlcohol: false,
            hasKidsMenu: false
          },
          distance: 0.9,
          estimatedTime: '12 мин',
          isOpen: true,
          isFavorite: false,
          tags: ['суши', 'доставка', 'халяль'],
          averageCheck: {
            min: 800,
            max: 2000,
            currency: 'RUB'
          }
        }
      ];

      setRestaurants(mockRestaurants);
    } catch (err: any) {
      setError('Ошибка загрузки ресторанов');
      console.error('Restaurants loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadRestaurants();
    setRefreshing(false);
  }, []);

  const filterAndSortRestaurants = () => {
    let filtered = [...restaurants];

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(restaurant =>
        restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        restaurant.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        restaurant.location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        restaurant.cuisine.some(cuisine => cuisine.toLowerCase().includes(searchQuery.toLowerCase())) ||
        restaurant.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    switch (filterBy) {
      case 'open':
        filtered = filtered.filter(restaurant => restaurant.isOpen);
        break;
      case 'delivery':
        filtered = filtered.filter(restaurant => restaurant.features.delivery);
        break;
      case 'reservation':
        filtered = filtered.filter(restaurant => restaurant.features.reservation);
        break;
      case 'terrace':
        filtered = filtered.filter(restaurant => restaurant.features.terrace);
        break;
      case 'vegan':
        filtered = filtered.filter(restaurant => restaurant.menu.hasVegan);
        break;
      case 'halal':
        filtered = filtered.filter(restaurant => restaurant.menu.hasHalal);
        break;
    }

    // Sort restaurants
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        case 'rating':
          return b.rating.average - a.rating.average;
        case 'price':
          const priceOrder = { '$': 1, '$$': 2, '$$$': 3, '$$$$': 4 };
          return priceOrder[a.priceRange] - priceOrder[b.priceRange];
        case 'name':
          return a.name.localeCompare(b.name);
        case 'popularity':
          return b.rating.count - a.rating.count;
        default:
          return 0;
      }
    });

    setFilteredRestaurants(filtered);
  };

  const handleRestaurantPress = (restaurant: Restaurant) => {
    navigation.navigate('PlaceDetail', { placeId: restaurant.id, placeType: 'restaurant' });
  };

  const handleToggleFavorite = async (restaurantId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 300));

      setRestaurants(prev => prev.map(restaurant =>
        restaurant.id === restaurantId ? { ...restaurant, isFavorite: !restaurant.isFavorite } : restaurant
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось обновить избранное');
    }
  };

  const handleCreateMeeting = (restaurant: Restaurant) => {
    navigation.navigate('CreateMeeting', {
      selectedPlace: {
        id: restaurant.id,
        name: restaurant.name,
        address: restaurant.location.formattedAddress,
        latitude: restaurant.location.latitude,
        longitude: restaurant.location.longitude
      }
    });
  };

  const handleDirections = (restaurant: Restaurant) => {
    if (userLocation) {
      const url = `https://www.openstreetmap.org/directions?from=${userLocation.latitude},${userLocation.longitude}&to=${restaurant.location.latitude},${restaurant.location.longitude}`;
      // TODO: Open in browser or maps app
      Alert.alert('Навигация', 'Открыть маршрут в картах?');
    }
  };

  const handleReservation = (restaurant: Restaurant) => {
    if (restaurant.features.reservation) {
      Alert.alert('Бронирование', `Забронировать столик в ${restaurant.name}?`);
      // TODO: Implement reservation functionality
    }
  };

  const getSortLabel = (option: SortOption): string => {
    switch (option) {
      case 'distance': return 'По расстоянию';
      case 'rating': return 'По рейтингу';
      case 'price': return 'По цене';
      case 'name': return 'По названию';
      case 'popularity': return 'По популярности';
      default: return '';
    }
  };

  const getFeatureIcon = (feature: string): string => {
    switch (feature) {
      case 'delivery': return 'delivery-dining';
      case 'takeaway': return 'shopping-bag';
      case 'reservation': return 'event-seat';
      case 'parking': return 'local-parking';
      case 'terrace': return 'deck';
      case 'liveMusic': return 'music-note';
      case 'wifi': return 'wifi';
      case 'kidsMenu': return 'child-care';
      case 'petFriendly': return 'pets';
      case 'wheelchairAccessible': return 'accessible';
      default: return 'check';
    }
  };

  const getFeatureLabel = (feature: string): string => {
    switch (feature) {
      case 'delivery': return 'Доставка';
      case 'takeaway': return 'На вынос';
      case 'reservation': return 'Бронирование';
      case 'parking': return 'Парковка';
      case 'terrace': return 'Терраса';
      case 'liveMusic': return 'Живая музыка';
      case 'wifi': return 'Wi-Fi';
      case 'kidsMenu': return 'Детское меню';
      case 'petFriendly': return 'С питомцами';
      case 'wheelchairAccessible': return 'Доступно';
      default: return feature;
    }
  };

  const renderRestaurantCard = ({ item: restaurant }: { item: Restaurant }) => (
    <TouchableOpacity
      style={styles.restaurantCard}
      onPress={() => handleRestaurantPress(restaurant)}
    >
      <View style={styles.restaurantImageContainer}>
        <View style={styles.placeholderImage}>
          <Icon name="restaurant" size={32} color="#FF6B35" />
        </View>

        {restaurant.isOpen !== undefined && (
          <View style={[styles.statusBadge, { backgroundColor: restaurant.isOpen ? '#4CAF50' : '#F44336' }]}>
            <Text style={styles.statusBadgeText}>
              {restaurant.isOpen ? 'Открыто' : 'Закрыто'}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleToggleFavorite(restaurant.id)}
        >
          <Icon
            name={restaurant.isFavorite ? "favorite" : "favorite-border"}
            size={20}
            color={restaurant.isFavorite ? "#E91E63" : "#ffffff"}
          />
        </TouchableOpacity>

        {restaurant.michelin && restaurant.michelin.stars > 0 && (
          <View style={styles.michelinBadge}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.michelinText}>{restaurant.michelin.stars}</Text>
          </View>
        )}
      </View>

      <View style={styles.restaurantInfo}>
        <View style={styles.restaurantHeader}>
          <Text style={styles.restaurantName} numberOfLines={1}>{restaurant.name}</Text>
          <Text style={styles.priceRange}>{restaurant.priceRange}</Text>
        </View>

        <View style={styles.cuisineContainer}>
          {restaurant.cuisine.slice(0, 2).map((cuisine, index) => (
            <Text key={index} style={styles.cuisineTag}>{cuisine}</Text>
          ))}
        </View>

        <Text style={styles.restaurantDescription} numberOfLines={2}>
          {restaurant.description}
        </Text>

        <View style={styles.restaurantDetails}>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{restaurant.rating.average}</Text>
            <Text style={styles.ratingCount}>({restaurant.rating.count})</Text>
          </View>

          {restaurant.distance && (
            <View style={styles.distanceContainer}>
              <Icon name="location-on" size={16} color="#666666" />
              <Text style={styles.distanceText}>{restaurant.distance} км</Text>
            </View>
          )}

          {restaurant.estimatedTime && (
            <View style={styles.timeContainer}>
              <Icon name="access-time" size={16} color="#666666" />
              <Text style={styles.timeText}>{restaurant.estimatedTime}</Text>
            </View>
          )}
        </View>

        <View style={styles.featuresContainer}>
          {Object.entries(restaurant.features)
            .filter(([_, value]) => value)
            .slice(0, 4)
            .map(([feature, _]) => (
              <View key={feature} style={styles.featureChip}>
                <Icon name={getFeatureIcon(feature)} size={12} color="#FF6B35" />
                <Text style={styles.featureText}>{getFeatureLabel(feature)}</Text>
              </View>
            ))}
        </View>

        <View style={styles.restaurantFooter}>
          <Text style={styles.addressText} numberOfLines={1}>
            {restaurant.location.address}
          </Text>

          <View style={styles.restaurantActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDirections(restaurant)}
            >
              <Icon name="directions" size={16} color="#E91E63" />
            </TouchableOpacity>

            {restaurant.features.reservation && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleReservation(restaurant)}
              >
                <Icon name="event-seat" size={16} color="#E91E63" />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleCreateMeeting(restaurant)}
            >
              <Icon name="event" size={16} color="#E91E63" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.averageCheckContainer}>
          <Text style={styles.averageCheckText}>
            Средний чек: {restaurant.averageCheck.min}-{restaurant.averageCheck.max} ₽
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMapView = () => (
    <View style={styles.mapContainer}>
      <MapView
        style={styles.map}
        initialRegion={{
          latitude: userLocation?.latitude || 55.7558,
          longitude: userLocation?.longitude || 37.6176,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {filteredRestaurants.map((restaurant) => (
          <Marker
            key={restaurant.id}
            coordinate={{
              latitude: restaurant.location.latitude,
              longitude: restaurant.location.longitude,
            }}
            title={restaurant.name}
            description={restaurant.description}
            onPress={() => handleRestaurantPress(restaurant)}
          >
            <View style={styles.customMarker}>
              <Icon name="restaurant" size={20} color="#ffffff" />
            </View>
          </Marker>
        ))}
      </MapView>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка ресторанов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadRestaurants}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Рестораны</Text>
        <TouchableOpacity
          style={styles.viewModeButton}
          onPress={() => setViewMode(viewMode === 'list' ? 'map' : 'list')}
        >
          <Icon name={viewMode === 'list' ? 'map' : 'list'} size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск ресторанов..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        {filterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterChip,
              filterBy === filter.id && styles.filterChipSelected
            ]}
            onPress={() => setFilterBy(filter.id as FilterOption)}
          >
            <Icon
              name={filter.icon}
              size={16}
              color={filterBy === filter.id ? '#ffffff' : '#FF6B35'}
            />
            <Text style={[
              styles.filterChipText,
              filterBy === filter.id && styles.filterChipTextSelected
            ]}>
              {filter.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Sort and Stats */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            const sortOptions: SortOption[] = ['distance', 'rating', 'price', 'name', 'popularity'];
            const currentIndex = sortOptions.indexOf(sortBy);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            setSortBy(sortOptions[nextIndex]);
          }}
        >
          <Icon name="sort" size={16} color="#E91E63" />
          <Text style={styles.sortButtonText}>{getSortLabel(sortBy)}</Text>
        </TouchableOpacity>

        <Text style={styles.statsText}>
          {filteredRestaurants.length} из {restaurants.length} ресторанов
        </Text>
      </View>

      {/* Content */}
      {filteredRestaurants.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="restaurant" size={64} color="#cccccc" />
          <Text style={styles.emptyTitle}>
            {searchQuery || filterBy !== 'all' ? 'Ничего не найдено' : 'Нет ресторанов'}
          </Text>
          <Text style={styles.emptyDescription}>
            {searchQuery || filterBy !== 'all'
              ? 'Попробуйте изменить параметры поиска'
              : 'В этом районе пока нет ресторанов'
            }
          </Text>
        </View>
      ) : viewMode === 'list' ? (
        <FlatList
          data={filteredRestaurants}
          renderItem={renderRestaurantCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#E91E63']}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderMapView()
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  viewModeButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  filtersContainer: {
    paddingVertical: 8,
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  filterChipSelected: {
    backgroundColor: '#FF6B35',
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterChipTextSelected: {
    color: '#ffffff',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  sortButtonText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '500',
  },
  statsText: {
    fontSize: 14,
    color: '#666666',
  },
  listContainer: {
    padding: 20,
    gap: 16,
  },
  restaurantCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  restaurantImageContainer: {
    height: 120,
    position: 'relative',
  },
  placeholderImage: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 16,
    padding: 8,
  },
  michelinBadge: {
    position: 'absolute',
    bottom: 12,
    left: 12,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  michelinText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  restaurantInfo: {
    padding: 16,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  priceRange: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FF6B35',
  },
  cuisineContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 8,
  },
  cuisineTag: {
    fontSize: 12,
    color: '#FF6B35',
    backgroundColor: '#FF6B35' + '10',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
    fontWeight: '500',
  },
  restaurantDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  restaurantDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  ratingCount: {
    fontSize: 14,
    color: '#666666',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  distanceText: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 14,
    color: '#666666',
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  featureChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF6B35' + '10',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  featureText: {
    fontSize: 10,
    color: '#FF6B35',
    fontWeight: '500',
  },
  restaurantFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 12,
    color: '#999999',
    flex: 1,
  },
  restaurantActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
  },
  averageCheckContainer: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  averageCheckText: {
    fontSize: 12,
    color: '#FF6B35',
    fontWeight: '500',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF6B35',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default RestaurantsScreen;