import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  FlatList,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

interface RestaurantsScreenProps {}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisine: string[];
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    formattedAddress: string;
  };
  rating: {
    average: number;
    count: number;
    aspects: {
      food: number;
      service: number;
      atmosphere: number;
      price: number;
      cleanliness: number;
    };
  };
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  photos: Array<{
    id: string;
    url: string;
    thumbnailUrl: string;
  }>;
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  features: {
    delivery: boolean;
    takeaway: boolean;
    reservation: boolean;
    parking: boolean;
    terrace: boolean;
    liveMusic: boolean;
    wifi: boolean;
    kidsMenu: boolean;
    petFriendly: boolean;
    wheelchairAccessible: boolean;
  };
  menu: {
    hasBreakfast: boolean;
    hasLunch: boolean;
    hasDinner: boolean;
    hasVegan: boolean;
    hasHalal: boolean;
    hasAlcohol: boolean;
    hasKidsMenu: boolean;
  };
  distance?: number;
  estimatedTime?: string;
  isOpen?: boolean;
  isFavorite: boolean;
  tags: string[];
  averageCheck: {
    min: number;
    max: number;
    currency: string;
  };
  michelin?: {
    stars: number;
    guide: boolean;
  };
}

type SortOption = 'distance' | 'rating' | 'price' | 'name' | 'popularity';
type FilterOption = 'all' | 'open' | 'delivery' | 'reservation' | 'terrace' | 'vegan' | 'halal';
type ViewMode = 'list' | 'map';

const RestaurantsScreen: React.FC<RestaurantsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [filteredRestaurants, setFilteredRestaurants] = useState<Restaurant[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('distance');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [error, setError] = useState<string | null>(null);

  const filterOptions = [
    { id: 'all', name: 'Все', icon: 'restaurant' },
    { id: 'open', name: 'Открыто', icon: 'schedule' },
    { id: 'delivery', name: 'Доставка', icon: 'delivery-dining' },
    { id: 'reservation', name: 'Бронь', icon: 'event-seat' },
    { id: 'terrace', name: 'Терраса', icon: 'deck' },
    { id: 'vegan', name: 'Веган', icon: 'eco' },
    { id: 'halal', name: 'Халяль', icon: 'verified' },
  ];

  useFocusEffect(
    useCallback(() => {
      loadRestaurants();
      getCurrentLocation();
    }, [])
  );

  useEffect(() => {
    filterAndSortRestaurants();
  }, [restaurants, searchQuery, sortBy, filterBy]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }
    } catch (error) {
      console.log('Location error:', error);
    }
  };

  const loadRestaurants = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockRestaurants: Restaurant[] = [
        {
          id: '1',
          name: 'Bella Vista',
          description: 'Аутентичная итальянская кухня с видом на город',
          cuisine: ['Итальянская', 'Европейская'],
          location: {
            latitude: 55.7558,
            longitude: 37.6176,
            address: 'ул. Тверская, 25',
            city: 'Москва',
            formattedAddress: 'ул. Тверская, 25, Москва'
          },
          rating: {
            average: 4.7,
            count: 234,
            aspects: {
              food: 4.8,
              service: 4.6,
              atmosphere: 4.7,
              price: 4.2,
              cleanliness: 4.9
            }
          },
          priceRange: '$$$',
          photos: [
            {
              id: '1',
              url: 'https://example.com/restaurant1.jpg',
              thumbnailUrl: 'https://example.com/restaurant1_thumb.jpg'
            }
          ],
          workingHours: {
            monday: { open: '12:00', close: '23:00', isOpen: true },
            tuesday: { open: '12:00', close: '23:00', isOpen: true },
            wednesday: { open: '12:00', close: '23:00', isOpen: true },
            thursday: { open: '12:00', close: '23:00', isOpen: true },
            friday: { open: '12:00', close: '24:00', isOpen: true },
            saturday: { open: '12:00', close: '24:00', isOpen: true },
            sunday: { open: '12:00', close: '22:00', isOpen: true }
          },
          features: {
            delivery: true,
            takeaway: true,
            reservation: true,
            parking: true,
            terrace: true,
            liveMusic: false,
            wifi: true,
            kidsMenu: true,
            petFriendly: false,
            wheelchairAccessible: true
          },
          menu: {
            hasBreakfast: false,
            hasLunch: true,
            hasDinner: true,
            hasVegan: true,
            hasHalal: false,
            hasAlcohol: true,
            hasKidsMenu: true
          },
          distance: 1.2,
          estimatedTime: '15 мин',
          isOpen: true,
          isFavorite: true,
          tags: ['итальянская', 'терраса', 'романтично', 'бронирование'],
          averageCheck: {
            min: 1500,
            max: 3500,
            currency: 'RUB'
          }
        },
        {
          id: '2',
          name: 'Sushi Master',
          description: 'Свежие суши и роллы от японского шеф-повара',
          cuisine: ['Японская', 'Азиатская'],
          location: {
            latitude: 55.7489,
            longitude: 37.6176,
            address: 'ул. Арбат, 32',
            city: 'Москва',
            formattedAddress: 'ул. Арбат, 32, Москва'
          },
          rating: {
            average: 4.5,
            count: 156,
            aspects: {
              food: 4.7,
              service: 4.4,
              atmosphere: 4.3,
              price: 4.1,
              cleanliness: 4.8
            }
          },
          priceRange: '$$',
          photos: [
            {
              id: '2',
              url: 'https://example.com/restaurant2.jpg',
              thumbnailUrl: 'https://example.com/restaurant2_thumb.jpg'
            }
          ],
          workingHours: {
            monday: { open: '11:00', close: '22:00', isOpen: true },
            tuesday: { open: '11:00', close: '22:00', isOpen: true },
            wednesday: { open: '11:00', close: '22:00', isOpen: true },
            thursday: { open: '11:00', close: '22:00', isOpen: true },
            friday: { open: '11:00', close: '23:00', isOpen: true },
            saturday: { open: '11:00', close: '23:00', isOpen: true },
            sunday: { open: '11:00', close: '21:00', isOpen: true }
          },
          features: {
            delivery: true,
            takeaway: true,
            reservation: false,
            parking: false,
            terrace: false,
            liveMusic: false,
            wifi: true,
            kidsMenu: false,
            petFriendly: false,
            wheelchairAccessible: true
          },
          menu: {
            hasBreakfast: false,
            hasLunch: true,
            hasDinner: true,
            hasVegan: false,
            hasHalal: true,
            hasAlcohol: false,
            hasKidsMenu: false
          },
          distance: 0.9,
          estimatedTime: '12 мин',
          isOpen: true,
          isFavorite: false,
          tags: ['суши', 'доставка', 'халяль'],
          averageCheck: {
            min: 800,
            max: 2000,
            currency: 'RUB'
          }
        }
      ];

      setRestaurants(mockRestaurants);
    } catch (err: any) {
      setError('Ошибка загрузки ресторанов');
      console.error('Restaurants loading error:', err);
    } finally {
      setLoading(false);
    }
  };