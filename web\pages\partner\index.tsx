import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Alert,
  Skeleton,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Event as EventIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Help as HelpIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { partnerService } from '../../services/partnerService';
import { PartnerDashboard, PartnerStats } from '../../types/partner.types';

interface PartnerPortalPageProps {}

const PartnerPortalPage: React.FC<PartnerPortalPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState<PartnerDashboard | null>(null);
  const [stats, setStats] = useState<PartnerStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/partner');
      return;
    }

    if (user?.role !== 'partner') {
      router.push('/partner/apply');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, user, router]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [dashboardData, statsData] = await Promise.all([
        partnerService.getDashboard(),
        partnerService.getStats()
      ]);

      setDashboard(dashboardData);
      setStats(statsData);
    } catch (err: any) {
      setError('Ошибка загрузки данных партнерского портала');
      console.error('Partner dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateVenue = () => {
    router.push('/partner/venues/create');
  };

  const handleViewAnalytics = () => {
    router.push('/partner/analytics');
  };

  const handleManageEvents = () => {
    router.push('/partner/events');
  };

  const handleSettings = () => {
    router.push('/partner/settings');
  };

  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Партнерский портал - LikesLove</title>
          <meta name="description" content="Партнерский портал для управления заведениями и мероприятиями" />
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ mb: 4 }}>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={500} height={24} sx={{ mt: 1 }} />
          </Box>
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} sm={6} md={3} key={item}>
                <Skeleton variant="rectangular" height={120} />
              </Grid>
            ))}
          </Grid>
        </Container>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Head>
          <title>Ошибка - Партнерский портал</title>
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Button variant="contained" onClick={loadDashboardData}>
            Повторить попытку
          </Button>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Партнерский портал - LikesLove</title>
        <meta name="description" content="Партнерский портал для управления заведениями и мероприятиями на платформе LikesLove" />
        <meta name="keywords" content="партнер, заведения, рестораны, кафе, мероприятия, аналитика" />
        <meta property="og:title" content="Партнерский портал - LikesLove" />
        <meta property="og:description" content="Управляйте своими заведениями и мероприятиями на платформе знакомств" />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="https://likeslove.ru/partner" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Добро пожаловать, {user?.name}!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Управляйте своими заведениями и отслеживайте статистику
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Уведомления">
              <IconButton>
                <NotificationsIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Справка">
              <IconButton>
                <HelpIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Настройки">
              <IconButton onClick={handleSettings}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <BusinessIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.totalVenues || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Заведений
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats?.totalVenues || 0) * 10, 100)}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <PeopleIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.totalVisitors || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Посетителей
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats?.totalVisitors || 0) / 10, 100)}
                  color="success"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <EventIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.totalEvents || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Мероприятий
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats?.totalEvents || 0) * 5, 100)}
                  color="warning"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.revenue || 0}₽
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Доход
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={`+${stats?.revenueGrowth || 0}%`}
                  color="success"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Быстрые действия
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<AddIcon />}
                      onClick={handleCreateVenue}
                      sx={{ py: 2 }}
                    >
                      Добавить заведение
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<EventIcon />}
                      onClick={handleManageEvents}
                      sx={{ py: 2 }}
                    >
                      Мероприятия
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<AnalyticsIcon />}
                      onClick={handleViewAnalytics}
                      sx={{ py: 2 }}
                    >
                      Аналитика
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<VisibilityIcon />}
                      onClick={() => router.push('/partner/venues')}
                      sx={{ py: 2 }}
                    >
                      Мои заведения
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Статус аккаунта
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Chip
                    label={dashboard?.accountStatus || 'Активен'}
                    color="success"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    Верифицирован
                  </Typography>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Следующий платеж
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  {dashboard?.nextPaymentDate || '15 февраля 2024'}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Recent Activity */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Последние бронирования
                </Typography>
                {dashboard?.recentBookings?.length ? (
                  dashboard.recentBookings.map((booking, index) => (
                    <Box key={index} sx={{ py: 1, borderBottom: index < dashboard.recentBookings.length - 1 ? 1 : 0, borderColor: 'divider' }}>
                      <Typography variant="body2" fontWeight="medium">
                        {booking.venueName}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {booking.date} • {booking.guestCount} гостей
                      </Typography>
                    </Box>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Нет недавних бронирований
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Уведомления
                </Typography>
                {dashboard?.notifications?.length ? (
                  dashboard.notifications.map((notification, index) => (
                    <Box key={index} sx={{ py: 1, borderBottom: index < dashboard.notifications.length - 1 ? 1 : 0, borderColor: 'divider' }}>
                      <Typography variant="body2">
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {notification.date}
                      </Typography>
                    </Box>
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Нет новых уведомлений
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default PartnerPortalPage;