import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Image,
  Alert,
  Dimensions
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';

const { width } = Dimensions.get('window');
const PHOTO_SIZE = (width - 80) / 4;

interface WriteReviewScreenProps {}

interface RouteParams {
  placeId: string;
  placeName: string;
  existingReview?: {
    id: string;
    rating: number;
    comment: string;
    photos: string[];
  };
}

const WriteReviewScreen: React.FC<WriteReviewScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { placeId, placeName, existingReview } = (route.params as RouteParams) || {};

  const [loading, setLoading] = useState(false);
  const [rating, setRating] = useState(existingReview?.rating || 0);
  const [comment, setComment] = useState(existingReview?.comment || '');
  const [photos, setPhotos] = useState<string[]>(existingReview?.photos || []);
  const [uploading, setUploading] = useState(false);

  const maxPhotos = 5;
  const maxCommentLength = 500;

  useFocusEffect(
    useCallback(() => {
      requestPermissions();
    }, [])
  );

  const requestPermissions = async () => {
    try {
      await ImagePicker.requestMediaLibraryPermissionsAsync();
      await ImagePicker.requestCameraPermissionsAsync();
    } catch (error) {
      console.error('Error requesting permissions:', error);
    }
  };

  const handleRatingPress = (selectedRating: number) => {
    setRating(selectedRating);
  };

  const handleAddPhoto = () => {
    if (photos.length >= maxPhotos) {
      Alert.alert('Лимит фотографий', `Максимальное количество фотографий: ${maxPhotos}`);
      return;
    }

    Alert.alert(
      'Добавить фотографию',
      'Выберите источник фотографии',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Камера', onPress: () => openCamera() },
        { text: 'Галерея', onPress: () => openImagePicker() }
      ]
    );
  };

  const openCamera = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert('Ошибка', 'Не удалось открыть камеру');
    }
  };

  const openImagePicker = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening image picker:', error);
      Alert.alert('Ошибка', 'Не удалось открыть галерею');
    }
  };

  const processImage = async (uri: string) => {
    try {
      setUploading(true);

      // Обработка изображения
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          { resize: { width: 800, height: 800 } }
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      setPhotos(prev => [...prev, manipulatedImage.uri]);
    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Ошибка', 'Не удалось обработать изображение');
    } finally {
      setUploading(false);
    }
  };

  const handleRemovePhoto = (index: number) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmitReview = async () => {
    if (rating === 0) {
      Alert.alert('Ошибка', 'Пожалуйста, поставьте оценку');
      return;
    }

    if (comment.trim().length < 10) {
      Alert.alert('Ошибка', 'Комментарий должен содержать минимум 10 символов');
      return;
    }

    try {
      setLoading(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Отзыв отправлен!',
        existingReview ? 'Ваш отзыв успешно обновлен' : 'Спасибо за ваш отзыв!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error submitting review:', error);
      Alert.alert('Ошибка', 'Не удалось отправить отзыв');
    } finally {
      setLoading(false);
    }
  };

  const getRatingText = (rating: number): string => {
    switch (rating) {
      case 1: return 'Ужасно';
      case 2: return 'Плохо';
      case 3: return 'Нормально';
      case 4: return 'Хорошо';
      case 5: return 'Отлично';
      default: return 'Поставьте оценку';
    }
  };

  const renderStars = () => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map(star => (
          <TouchableOpacity
            key={star}
            style={styles.starButton}
            onPress={() => handleRatingPress(star)}
          >
            <Icon
              name={star <= rating ? 'star' : 'star-border'}
              size={32}
              color={star <= rating ? '#FFD700' : '#cccccc'}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderPhotos = () => {
    const slots = Array.from({ length: maxPhotos }, (_, index) => {
      const photo = photos[index];
      
      if (photo) {
        return (
          <View key={index} style={styles.photoContainer}>
            <Image source={{ uri: photo }} style={styles.photo} />
            <TouchableOpacity
              style={styles.removePhotoButton}
              onPress={() => handleRemovePhoto(index)}
            >
              <Icon name="close" size={16} color="#ffffff" />
            </TouchableOpacity>
          </View>
        );
      }
      
      if (index === photos.length && photos.length < maxPhotos) {
        return (
          <TouchableOpacity
            key={index}
            style={styles.addPhotoButton}
            onPress={handleAddPhoto}
            disabled={uploading}
          >
            {uploading ? (
              <ActivityIndicator size="small" color="#E91E63" />
            ) : (
              <Icon name="add-a-photo" size={24} color="#E91E63" />
            )}
          </TouchableOpacity>
        );
      }
      
      return null;
    });

    return <View style={styles.photosContainer}>{slots}</View>;
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>
            {existingReview ? 'Редактировать отзыв' : 'Написать отзыв'}
          </Text>
          <Text style={styles.headerSubtitle}>{placeName}</Text>
        </View>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Rating Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ваша оценка</Text>
          {renderStars()}
          <Text style={styles.ratingText}>{getRatingText(rating)}</Text>
        </View>

        {/* Comment Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ваш отзыв</Text>
          <TextInput
            style={styles.commentInput}
            placeholder="Расскажите о вашем опыте посещения этого места..."
            value={comment}
            onChangeText={setComment}
            multiline
            numberOfLines={6}
            maxLength={maxCommentLength}
            textAlignVertical="top"
          />
          <Text style={styles.characterCount}>
            {comment.length}/{maxCommentLength}
          </Text>
        </View>

        {/* Photos Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Фотографии ({photos.length}/{maxPhotos})
          </Text>
          <Text style={styles.sectionDescription}>
            Добавьте фотографии, чтобы помочь другим пользователям
          </Text>
          {renderPhotos()}
        </View>

        {/* Tips Section */}
        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>Советы для хорошего отзыва:</Text>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Будьте честными и объективными</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Опишите конкретные детали</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Добавьте фотографии</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Соблюдайте вежливость</Text>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Submit Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.submitButton,
            (rating === 0 || comment.trim().length < 10 || loading) && styles.submitButtonDisabled
          ]}
          onPress={handleSubmitReview}
          disabled={rating === 0 || comment.trim().length < 10 || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Text style={styles.submitButtonText}>
              {existingReview ? 'Обновить отзыв' : 'Опубликовать отзыв'}
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  starButton: {
    padding: 8,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
  },
  commentInput: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    minHeight: 120,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 8,
  },
  photosContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photoContainer: {
    position: 'relative',
  },
  photo: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  removePhotoButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoButton: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  tipsSection: {
    margin: 20,
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
    flex: 1,
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  submitButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  bottomPadding: {
    height: 40,
  },
});

export default WriteReviewScreen;
