import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Alert
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface InterestedInSettingsScreenProps {}

interface RouteParams {
  currentValue: 'men' | 'women' | 'both';
  onSave: (value: 'men' | 'women' | 'both') => void;
}

interface Option {
  value: 'men' | 'women' | 'both';
  title: string;
  description: string;
  icon: string;
}

const InterestedInSettingsScreen: React.FC<InterestedInSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { currentValue, onSave } = (route.params as RouteParams) || {
    currentValue: 'both' as const,
    onSave: () => {}
  };

  const [selectedValue, setSelectedValue] = useState(currentValue);
  const [saving, setSaving] = useState(false);

  const options: Option[] = [
    {
      value: 'men',
      title: 'Мужчины',
      description: 'Показывать только мужские анкеты',
      icon: 'male'
    },
    {
      value: 'women',
      title: 'Женщины',
      description: 'Показывать только женские анкеты',
      icon: 'female'
    },
    {
      value: 'both',
      title: 'Все',
      description: 'Показывать анкеты мужчин и женщин',
      icon: 'people'
    }
  ];

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      onSave(selectedValue);
      navigation.goBack();
    } catch (error) {
      console.error('Error saving interested in settings:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleOptionSelect = (value: 'men' | 'women' | 'both') => {
    setSelectedValue(value);
  };

  const renderOption = (option: Option) => {
    const isSelected = selectedValue === option.value;
    
    return (
      <TouchableOpacity
        key={option.value}
        style={[
          styles.optionCard,
          isSelected && styles.optionCardSelected
        ]}
        onPress={() => handleOptionSelect(option.value)}
      >
        <View style={[
          styles.optionIcon,
          isSelected && styles.optionIconSelected
        ]}>
          <Icon 
            name={option.icon} 
            size={32} 
            color={isSelected ? '#ffffff' : '#E91E63'} 
          />
        </View>
        
        <View style={styles.optionContent}>
          <Text style={[
            styles.optionTitle,
            isSelected && styles.optionTitleSelected
          ]}>
            {option.title}
          </Text>
          <Text style={[
            styles.optionDescription,
            isSelected && styles.optionDescriptionSelected
          ]}>
            {option.description}
          </Text>
        </View>
        
        <View style={styles.optionCheck}>
          {isSelected && (
            <Icon name="check-circle" size={24} color="#ffffff" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Кто вас интересует</Text>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          disabled={saving}
        >
          <Text style={[
            styles.saveButtonText,
            saving && styles.saveButtonTextDisabled
          ]}>
            {saving ? 'Сохранение...' : 'Сохранить'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Description */}
        <View style={styles.descriptionSection}>
          <Text style={styles.descriptionTitle}>Выберите, кого вы хотите видеть</Text>
          <Text style={styles.descriptionText}>
            Эта настройка определяет, анкеты каких пользователей будут показываться вам при поиске
          </Text>
        </View>

        {/* Options */}
        <View style={styles.optionsSection}>
          {options.map(renderOption)}
        </View>

        {/* Info */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Icon name="info" size={20} color="#2196F3" />
            <Text style={styles.infoText}>
              Вы можете изменить эту настройку в любое время. Изменения вступят в силу немедленно.
            </Text>
          </View>
        </View>

        {/* Privacy Note */}
        <View style={styles.privacySection}>
          <View style={styles.privacyCard}>
            <Icon name="security" size={20} color="#4CAF50" />
            <Text style={styles.privacyText}>
              Ваши предпочтения конфиденциальны и не отображаются в вашем профиле
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  saveButtonTextDisabled: {
    color: '#cccccc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  descriptionSection: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  descriptionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    textAlign: 'center',
    marginBottom: 12,
  },
  descriptionText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 20,
  },
  optionsSection: {
    paddingVertical: 24,
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionCardSelected: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  optionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  optionIconSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 6,
  },
  optionTitleSelected: {
    color: '#ffffff',
  },
  optionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  optionDescriptionSelected: {
    color: '#ffffff' + 'CC',
  },
  optionCheck: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoSection: {
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    padding: 16,
    borderRadius: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
    marginLeft: 12,
  },
  privacySection: {
    paddingVertical: 16,
  },
  privacyCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E8F5E8',
    padding: 16,
    borderRadius: 12,
  },
  privacyText: {
    flex: 1,
    fontSize: 14,
    color: '#2E7D32',
    lineHeight: 20,
    marginLeft: 12,
  },
});

export default InterestedInSettingsScreen;
