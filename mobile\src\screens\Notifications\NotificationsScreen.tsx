import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Switch
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface NotificationsScreenProps {}

interface NotificationItem {
  id: string;
  type: 'like' | 'match' | 'message' | 'meeting' | 'system' | 'promotion';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  actionData?: any;
}

const NotificationsScreen: React.FC<NotificationsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [pushEnabled, setPushEnabled] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadNotifications();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      loadNotifications();
    }, [])
  );

  const loadNotifications = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockNotifications: NotificationItem[] = [
        {
          id: '1',
          type: 'match',
          title: 'Новое совпадение!',
          message: 'У вас взаимная симпатия с Анной',
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          isRead: false,
          userId: 'user1',
          userName: 'Анна',
          userAvatar: 'https://example.com/avatar1.jpg'
        },
        {
          id: '2',
          type: 'like',
          title: 'Вы понравились!',
          message: 'Мария поставила вам лайк',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          isRead: false,
          userId: 'user2',
          userName: 'Мария',
          userAvatar: 'https://example.com/avatar2.jpg'
        },
        {
          id: '3',
          type: 'message',
          title: 'Новое сообщение',
          message: 'Елена написала вам сообщение',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          isRead: true,
          userId: 'user3',
          userName: 'Елена',
          userAvatar: 'https://example.com/avatar3.jpg'
        },
        {
          id: '4',
          type: 'meeting',
          title: 'Напоминание о встрече',
          message: 'Встреча с Анной сегодня в 19:00 в кафе "Пушкин"',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          isRead: true,
          actionData: { meetingId: 'meeting1' }
        },
        {
          id: '5',
          type: 'system',
          title: 'Обновление профиля',
          message: 'Добавьте больше фотографий для увеличения количества совпадений',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          isRead: true
        },
        {
          id: '6',
          type: 'promotion',
          title: 'Специальное предложение',
          message: 'Получите Premium со скидкой 50% до конца месяца',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          isRead: true
        }
      ];

      setNotifications(mockNotifications);
    } catch (err: any) {
      setError('Ошибка загрузки уведомлений');
      console.error('Notifications loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadNotifications();
    setRefreshing(false);
  };

  const handleNotificationPress = async (notification: NotificationItem) => {
    // Отмечаем как прочитанное
    if (!notification.isRead) {
      await markAsRead(notification.id);
    }

    // Навигация в зависимости от типа уведомления
    switch (notification.type) {
      case 'match':
      case 'like':
        if (notification.userId) {
          navigation.navigate('UserProfile', { userId: notification.userId });
        }
        break;
      case 'message':
        if (notification.userId) {
          navigation.navigate('Chat', {
            userId: notification.userId,
            userName: notification.userName
          });
        }
        break;
      case 'meeting':
        if (notification.actionData?.meetingId) {
          navigation.navigate('MeetingDetails', {
            meetingId: notification.actionData.meetingId
          });
        }
        break;
      case 'system':
        navigation.navigate('Profile');
        break;
      case 'promotion':
        navigation.navigate('Subscription');
        break;
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      // TODO: Implement actual API call
      setNotifications(prev => prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      ));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      // TODO: Implement actual API call
      setNotifications(prev => prev.map(notification =>
        ({ ...notification, isRead: true })
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отметить все уведомления как прочитанные');
    }
  };

  const clearAllNotifications = () => {
    Alert.alert(
      'Очистить уведомления',
      'Вы уверены, что хотите удалить все уведомления?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual API call
              setNotifications([]);
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось очистить уведомления');
            }
          }
        }
      ]
    );
  };

  const togglePushNotifications = async (enabled: boolean) => {
    try {
      // TODO: Implement actual API call to update settings
      setPushEnabled(enabled);
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить настройки уведомлений');
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'match':
        return 'favorite';
      case 'like':
        return 'thumb-up';
      case 'message':
        return 'chat';
      case 'meeting':
        return 'event';
      case 'system':
        return 'info';
      case 'promotion':
        return 'local-offer';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'match':
        return '#E91E63';
      case 'like':
        return '#FF6B9D';
      case 'message':
        return '#2196F3';
      case 'meeting':
        return '#FF9800';
      case 'system':
        return '#9C27B0';
      case 'promotion':
        return '#4CAF50';
      default:
        return '#666666';
    }
  };

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} мин назад`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} ч назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн назад`;
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const renderNotificationItem = ({ item }: { item: NotificationItem }) => (
    <TouchableOpacity
      style={[styles.notificationItem, !item.isRead && styles.unreadNotification]}
      onPress={() => handleNotificationPress(item)}
    >
      <View style={[styles.iconContainer, { backgroundColor: getNotificationColor(item.type) + '20' }]}>
        <Icon
          name={getNotificationIcon(item.type)}
          size={24}
          color={getNotificationColor(item.type)}
        />
      </View>

      <View style={styles.notificationContent}>
        <View style={styles.notificationHeader}>
          <Text style={[styles.notificationTitle, !item.isRead && styles.unreadTitle]}>
            {item.title}
          </Text>
          <Text style={styles.timestamp}>
            {formatTimestamp(item.timestamp)}
          </Text>
        </View>

        <Text style={styles.notificationMessage} numberOfLines={2}>
          {item.message}
        </Text>
      </View>

      {!item.isRead && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="notifications-none" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Нет уведомлений</Text>
      <Text style={styles.emptyDescription}>
        Здесь будут отображаться ваши уведомления о лайках, совпадениях и сообщениях
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка уведомлений...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          Уведомления {unreadCount > 0 && `(${unreadCount})`}
        </Text>
        <TouchableOpacity
          style={styles.menuButton}
          onPress={() => {
            Alert.alert(
              'Действия',
              'Выберите действие',
              [
                { text: 'Отмена', style: 'cancel' },
                { text: 'Отметить все как прочитанные', onPress: markAllAsRead },
                { text: 'Очистить все', style: 'destructive', onPress: clearAllNotifications }
              ]
            );
          }}
        >
          <Icon name="more-vert" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Push Notifications Toggle */}
      <View style={styles.settingsContainer}>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>Push-уведомления</Text>
          <Switch
            value={pushEnabled}
            onValueChange={togglePushNotifications}
            trackColor={{ false: '#e0e0e0', true: '#E91E63' + '40' }}
            thumbColor={pushEnabled ? '#E91E63' : '#f4f3f4'}
          />
        </View>
      </View>

      {/* Notifications List */}
      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  menuButton: {
    padding: 8,
  },
  settingsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  settingLabel: {
    fontSize: 16,
    color: '#333333',
  },
  listContainer: {
    paddingBottom: 20,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    position: 'relative',
  },
  unreadNotification: {
    backgroundColor: '#E91E63' + '05',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  notificationContent: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    flex: 1,
    marginRight: 8,
  },
  unreadTitle: {
    fontWeight: '600',
  },
  timestamp: {
    fontSize: 12,
    color: '#666666',
  },
  notificationMessage: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  unreadDot: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E91E63',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default NotificationsScreen;