import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  Avatar,
  LinearProgress,
  Alert,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Badge
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Report as ReportIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { adminService } from '../../services/adminService';
import { AdminDashboard, AdminStats, RecentActivity } from '../../types/admin.types';

interface AdminDashboardPageProps {}

const AdminDashboardPage: React.FC<AdminDashboardPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [dashboard, setDashboard] = useState<AdminDashboard | null>(null);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/admin/dashboard');
      return;
    }

    if (user?.role !== 'admin' && user?.role !== 'moderator') {
      router.push('/');
      return;
    }

    loadDashboardData();
  }, [isAuthenticated, user, router]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [dashboardData, statsData, activityData] = await Promise.all([
        adminService.getDashboard(),
        adminService.getStats(),
        adminService.getRecentActivity()
      ]);

      setDashboard(dashboardData);
      setStats(statsData);
      setRecentActivity(activityData);
    } catch (err: any) {
      setError('Ошибка загрузки данных админ-панели');
      console.error('Admin dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'users':
        router.push('/admin/users');
        break;
      case 'reports':
        router.push('/admin/reports');
        break;
      case 'moderation':
        router.push('/admin/moderation');
        break;
      case 'analytics':
        router.push('/admin/analytics');
        break;
      case 'settings':
        router.push('/admin/settings');
        break;
      default:
        break;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registered':
        return <PeopleIcon fontSize="small" color="success" />;
      case 'report_submitted':
        return <ReportIcon fontSize="small" color="warning" />;
      case 'user_blocked':
        return <BlockIcon fontSize="small" color="error" />;
      case 'content_approved':
        return <CheckCircleIcon fontSize="small" color="success" />;
      case 'security_alert':
        return <SecurityIcon fontSize="small" color="error" />;
      default:
        return <DashboardIcon fontSize="small" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'user_registered':
      case 'content_approved':
        return 'success';
      case 'report_submitted':
        return 'warning';
      case 'user_blocked':
      case 'security_alert':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Админ-панель - LikesLove</title>
          <meta name="description" content="Административная панель управления платформой" />
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ mb: 4 }}>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={500} height={24} sx={{ mt: 1 }} />
          </Box>
          <Grid container spacing={3}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <Grid item xs={12} sm={6} md={4} lg={2} key={item}>
                <Skeleton variant="rectangular" height={120} />
              </Grid>
            ))}
          </Grid>
        </Container>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Head>
          <title>Ошибка - Админ-панель</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
          <Button variant="contained" onClick={loadDashboardData}>
            Повторить попытку
          </Button>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Админ-панель - LikesLove</title>
        <meta name="description" content="Административная панель управления платформой знакомств LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
        <link rel="canonical" href="https://likeslove.ru/admin/dashboard" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Админ-панель
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Добро пожаловать, {user?.name}! Управление платформой LikesLove
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Уведомления">
              <IconButton>
                <Badge badgeContent={dashboard?.pendingNotifications || 0} color="error">
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            </Tooltip>
            <Tooltip title="Настройки">
              <IconButton onClick={() => handleQuickAction('settings')}>
                <SettingsIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Critical Alerts */}
        {dashboard?.criticalAlerts && dashboard.criticalAlerts.length > 0 && (
          <Alert severity="error" sx={{ mb: 3 }} icon={<WarningIcon />}>
            <Typography variant="subtitle2" gutterBottom>
              Критические уведомления ({dashboard.criticalAlerts.length})
            </Typography>
            {dashboard.criticalAlerts.slice(0, 3).map((alert, index) => (
              <Typography key={index} variant="body2">
                • {alert.message}
              </Typography>
            ))}
          </Alert>
        )}

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={4} lg={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <PeopleIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.totalUsers?.toLocaleString() || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Пользователей
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={`+${stats?.newUsersToday || 0} сегодня`}
                  color="success"
                  size="small"
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={2}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <ReportIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" component="div">
                      {stats?.pendingReports || 0}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Жалобы
                    </Typography>
                  </Box>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={Math.min((stats?.pendingReports || 0) * 10, 100)}
                  color="warning"
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </CardContent>
            </Card>
          </Grid>