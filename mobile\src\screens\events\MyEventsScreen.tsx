import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const { width } = Dimensions.get('window');

interface MyEventsScreenProps {}

interface MyEvent {
  id: string;
  title: string;
  description: string;
  category: string;
  date: string;
  time: string;
  location: {
    name: string;
    address: string;
  };
  imageUrl: string;
  price: number;
  currency: string;
  attendeesCount: number;
  maxAttendees: number;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  isOnline: boolean;
  role: 'organizer' | 'attendee';
}

const MyEventsScreen: React.FC<MyEventsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [events, setEvents] = useState<MyEvent[]>([]);
  const [selectedTab, setSelectedTab] = useState<'upcoming' | 'past' | 'organized'>('upcoming');
  const [error, setError] = useState<string | null>(null);

  const tabs = [
    { id: 'upcoming', name: 'Предстоящие', icon: 'schedule' },
    { id: 'past', name: 'Прошедшие', icon: 'history' },
    { id: 'organized', name: 'Мои события', icon: 'event-note' }
  ];

  useEffect(() => {
    loadMyEvents();
  }, [selectedTab]);

  useFocusEffect(
    React.useCallback(() => {
      loadMyEvents();
    }, [selectedTab])
  );

  const loadMyEvents = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockEvents: MyEvent[] = [
        {
          id: '1',
          title: 'Speed Dating в центре города',
          description: 'Встреча для знакомства с интересными людьми',
          category: 'dating',
          date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          time: '19:00',
          location: {
            name: 'Кафе "Встреча"',
            address: 'ул. Тверская, 15'
          },
          imageUrl: 'https://example.com/event1.jpg',
          price: 1500,
          currency: 'RUB',
          attendeesCount: 12,
          maxAttendees: 20,
          status: 'upcoming',
          isOnline: false,
          role: 'attendee'
        },
        {
          id: '2',
          title: 'Кулинарный мастер-класс',
          description: 'Готовим вместе и знакомимся',
          category: 'food',
          date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          time: '18:30',
          location: {
            name: 'Кулинарная студия "Вкус"',
            address: 'ул. Арбат, 25'
          },
          imageUrl: 'https://example.com/event2.jpg',
          price: 2500,
          currency: 'RUB',
          attendeesCount: 8,
          maxAttendees: 16,
          status: 'upcoming',
          isOnline: false,
          role: 'organizer'
        },
        {
          id: '3',
          title: 'Вечер настольных игр',
          description: 'Играем и общаемся в дружеской атмосфере',
          category: 'party',
          date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          time: '20:00',
          location: {
            name: 'Антикафе "Игры"',
            address: 'ул. Покровка, 10'
          },
          imageUrl: 'https://example.com/event3.jpg',
          price: 0,
          currency: 'RUB',
          attendeesCount: 15,
          maxAttendees: 20,
          status: 'completed',
          isOnline: false,
          role: 'attendee'
        }
      ];

      // Filter events based on selected tab
      let filteredEvents = mockEvents;
      if (selectedTab === 'upcoming') {
        filteredEvents = mockEvents.filter(event =>
          event.status === 'upcoming' || event.status === 'ongoing'
        );
      } else if (selectedTab === 'past') {
        filteredEvents = mockEvents.filter(event =>
          event.status === 'completed' || event.status === 'cancelled'
        );
      } else if (selectedTab === 'organized') {
        filteredEvents = mockEvents.filter(event => event.role === 'organizer');
      }

      setEvents(filteredEvents);
    } catch (err: any) {
      setError('Ошибка загрузки событий');
      console.error('My events loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMyEvents();
    setRefreshing(false);
  };

  const handleEventPress = (event: MyEvent) => {
    navigation.navigate('EventDetail', { eventId: event.id });
  };

  const handleCancelEvent = (eventId: string) => {
    Alert.alert(
      'Отменить участие',
      'Вы уверены, что хотите отменить участие в этом событии?',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Да',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual API call
              setEvents(prev => prev.filter(event => event.id !== eventId));
              Alert.alert('Успех', 'Участие отменено');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось отменить участие');
            }
          }
        }
      ]
    );
  };

  const handleEditEvent = (eventId: string) => {
    navigation.navigate('EditEvent', { eventId });
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Сегодня';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Завтра';
    } else {
      return date.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'long'
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '#4CAF50';
      case 'ongoing':
        return '#FF9800';
      case 'completed':
        return '#9E9E9E';
      case 'cancelled':
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'Предстоящее';
      case 'ongoing':
        return 'Идет сейчас';
      case 'completed':
        return 'Завершено';
      case 'cancelled':
        return 'Отменено';
      default:
        return status;
    }
  };

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {tabs.map((tab) => (
        <TouchableOpacity
          key={tab.id}
          style={[
            styles.tab,
            selectedTab === tab.id && styles.tabActive
          ]}
          onPress={() => setSelectedTab(tab.id as any)}
        >
          <Icon
            name={tab.icon}
            size={20}
            color={selectedTab === tab.id ? '#E91E63' : '#666666'}
          />
          <Text
            style={[
              styles.tabText,
              selectedTab === tab.id && styles.tabTextActive
            ]}
          >
            {tab.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEventCard = ({ item }: { item: MyEvent }) => (
    <TouchableOpacity
      style={styles.eventCard}
      onPress={() => handleEventPress(item)}
    >
      <View style={styles.eventImageContainer}>
        <View style={styles.eventImagePlaceholder}>
          <Icon name="event" size={32} color="#E91E63" />
        </View>

        <View style={styles.eventBadges}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusBadgeText}>{getStatusLabel(item.status)}</Text>
          </View>

          {item.role === 'organizer' && (
            <View style={styles.roleBadge}>
              <Icon name="star" size={12} color="#ffffff" />
              <Text style={styles.roleBadgeText}>Организатор</Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.eventContent}>
        <Text style={styles.eventTitle} numberOfLines={2}>
          {item.title}
        </Text>

        <Text style={styles.eventDescription} numberOfLines={2}>
          {item.description}
        </Text>

        <View style={styles.eventDetails}>
          <View style={styles.eventDateTime}>
            <Icon name="schedule" size={14} color="#666666" />
            <Text style={styles.eventDateTimeText}>
              {formatDate(item.date)} в {item.time}
            </Text>
          </View>

          <View style={styles.eventLocation}>
            <Icon name={item.isOnline ? 'videocam' : 'location-on'} size={14} color="#666666" />
            <Text style={styles.eventLocationText} numberOfLines={1}>
              {item.isOnline ? 'Онлайн' : item.location.name}
            </Text>
          </View>
        </View>

        <View style={styles.eventFooter}>
          <View style={styles.eventPrice}>
            {item.price > 0 ? (
              <Text style={styles.priceText}>{item.price}₽</Text>
            ) : (
              <Text style={styles.priceFreeText}>Бесплатно</Text>
            )}
          </View>

          <View style={styles.eventActions}>
            {item.role === 'organizer' && item.status === 'upcoming' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleEditEvent(item.id)}
              >
                <Icon name="edit" size={16} color="#E91E63" />
              </TouchableOpacity>
            )}

            {item.role === 'attendee' && item.status === 'upcoming' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleCancelEvent(item.id)}
              >
                <Icon name="close" size={16} color="#F44336" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="event-note" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>
        {selectedTab === 'upcoming' && 'Нет предстоящих событий'}
        {selectedTab === 'past' && 'Нет прошедших событий'}
        {selectedTab === 'organized' && 'Вы еще не создали ни одного события'}
      </Text>
      <Text style={styles.emptyDescription}>
        {selectedTab === 'upcoming' && 'Найдите интересные события в разделе "События"'}
        {selectedTab === 'past' && 'Здесь будут отображаться события, в которых вы участвовали'}
        {selectedTab === 'organized' && 'Создайте свое первое событие и найдите единомышленников'}
      </Text>
      {selectedTab === 'organized' && (
        <TouchableOpacity
          style={styles.createEventButton}
          onPress={() => navigation.navigate('CreateEvent')}
        >
          <Text style={styles.createEventButtonText}>Создать событие</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка событий...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Мои события</Text>
        <TouchableOpacity
          style={styles.createButton}
          onPress={() => navigation.navigate('CreateEvent')}
        >
          <Icon name="add" size={24} color="#E91E63" />
        </TouchableOpacity>
      </View>

      {/* Tab Bar */}
      {renderTabBar()}

      {/* Events List */}
      <FlatList
        data={events}
        renderItem={renderEventCard}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  createButton: {
    padding: 8,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 6,
  },
  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: '#E91E63',
  },
  tabText: {
    fontSize: 14,
    color: '#666666',
  },
  tabTextActive: {
    color: '#E91E63',
    fontWeight: '600',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  eventCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden',
  },
  eventImageContainer: {
    height: 100,
    position: 'relative',
  },
  eventImagePlaceholder: {
    flex: 1,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
  },
  eventBadges: {
    position: 'absolute',
    top: 8,
    left: 8,
    right: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  roleBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  eventContent: {
    padding: 16,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    lineHeight: 22,
  },
  eventDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  eventDetails: {
    marginBottom: 12,
  },
  eventDateTime: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  eventDateTimeText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  eventLocationText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 6,
    flex: 1,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventPrice: {
    flex: 1,
  },
  priceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  priceFreeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  eventActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    padding: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 30,
  },
  createEventButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  createEventButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default MyEventsScreen;