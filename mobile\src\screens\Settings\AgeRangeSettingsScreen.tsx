import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Alert,
  Dimensions
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Slider from '@react-native-community/slider';

const { width } = Dimensions.get('window');

interface AgeRangeSettingsScreenProps {}

interface RouteParams {
  currentRange: {
    min: number;
    max: number;
  };
  onSave: (range: { min: number; max: number }) => void;
}

const AgeRangeSettingsScreen: React.FC<AgeRangeSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { currentRange, onSave } = (route.params as RouteParams) || {
    currentRange: { min: 18, max: 35 },
    onSave: () => {}
  };

  const [minAge, setMinAge] = useState(currentRange.min);
  const [maxAge, setMaxAge] = useState(currentRange.max);
  const [saving, setSaving] = useState(false);

  const MIN_AGE = 18;
  const MAX_AGE = 100;

  const handleSave = async () => {
    if (minAge >= maxAge) {
      Alert.alert('Ошибка', 'Минимальный возраст должен быть меньше максимального');
      return;
    }

    if (maxAge - minAge < 1) {
      Alert.alert('Ошибка', 'Разница между минимальным и максимальным возрастом должна быть не менее 1 года');
      return;
    }

    try {
      setSaving(true);
      
      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      onSave({ min: minAge, max: maxAge });
      navigation.goBack();
    } catch (error) {
      console.error('Error saving age range:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleMinAgeChange = (value: number) => {
    const newMinAge = Math.round(value);
    setMinAge(newMinAge);
    
    // Автоматически корректируем максимальный возраст если нужно
    if (newMinAge >= maxAge) {
      setMaxAge(Math.min(newMinAge + 1, MAX_AGE));
    }
  };

  const handleMaxAgeChange = (value: number) => {
    const newMaxAge = Math.round(value);
    setMaxAge(newMaxAge);
    
    // Автоматически корректируем минимальный возраст если нужно
    if (newMaxAge <= minAge) {
      setMinAge(Math.max(newMaxAge - 1, MIN_AGE));
    }
  };

  const handlePresetSelect = (min: number, max: number) => {
    setMinAge(min);
    setMaxAge(max);
  };

  const presets = [
    { label: '18-25', min: 18, max: 25 },
    { label: '25-35', min: 25, max: 35 },
    { label: '30-40', min: 30, max: 40 },
    { label: '35-45', min: 35, max: 45 },
    { label: '40-50', min: 40, max: 50 },
    { label: '45+', min: 45, max: MAX_AGE }
  ];

  const renderPreset = (preset: { label: string; min: number; max: number }) => {
    const isSelected = minAge === preset.min && maxAge === preset.max;
    
    return (
      <TouchableOpacity
        key={preset.label}
        style={[
          styles.presetButton,
          isSelected && styles.presetButtonSelected
        ]}
        onPress={() => handlePresetSelect(preset.min, preset.max)}
      >
        <Text style={[
          styles.presetButtonText,
          isSelected && styles.presetButtonTextSelected
        ]}>
          {preset.label}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Возрастной диапазон</Text>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          disabled={saving}
        >
          <Text style={[
            styles.saveButtonText,
            saving && styles.saveButtonTextDisabled
          ]}>
            {saving ? 'Сохранение...' : 'Сохранить'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {/* Current Range Display */}
        <View style={styles.rangeDisplay}>
          <Text style={styles.rangeTitle}>Показывать анкеты людей в возрасте:</Text>
          <Text style={styles.rangeValue}>{minAge} - {maxAge} лет</Text>
          <Text style={styles.rangeDescription}>
            Вы будете видеть анкеты людей в выбранном возрастном диапазоне
          </Text>
        </View>

        {/* Sliders */}
        <View style={styles.slidersContainer}>
          {/* Min Age Slider */}
          <View style={styles.sliderSection}>
            <View style={styles.sliderHeader}>
              <Text style={styles.sliderLabel}>Минимальный возраст</Text>
              <Text style={styles.sliderValue}>{minAge} лет</Text>
            </View>
            <Slider
              style={styles.slider}
              minimumValue={MIN_AGE}
              maximumValue={MAX_AGE - 1}
              value={minAge}
              onValueChange={handleMinAgeChange}
              step={1}
              minimumTrackTintColor="#E91E63"
              maximumTrackTintColor="#e0e0e0"
              thumbStyle={styles.sliderThumb}
            />
            <View style={styles.sliderRange}>
              <Text style={styles.sliderRangeText}>{MIN_AGE}</Text>
              <Text style={styles.sliderRangeText}>{MAX_AGE}</Text>
            </View>
          </View>

          {/* Max Age Slider */}
          <View style={styles.sliderSection}>
            <View style={styles.sliderHeader}>
              <Text style={styles.sliderLabel}>Максимальный возраст</Text>
              <Text style={styles.sliderValue}>{maxAge} лет</Text>
            </View>
            <Slider
              style={styles.slider}
              minimumValue={MIN_AGE + 1}
              maximumValue={MAX_AGE}
              value={maxAge}
              onValueChange={handleMaxAgeChange}
              step={1}
              minimumTrackTintColor="#E91E63"
              maximumTrackTintColor="#e0e0e0"
              thumbStyle={styles.sliderThumb}
            />
            <View style={styles.sliderRange}>
              <Text style={styles.sliderRangeText}>{MIN_AGE}</Text>
              <Text style={styles.sliderRangeText}>{MAX_AGE}</Text>
            </View>
          </View>
        </View>

        {/* Presets */}
        <View style={styles.presetsSection}>
          <Text style={styles.presetsTitle}>Быстрый выбор</Text>
          <View style={styles.presetsContainer}>
            {presets.map(renderPreset)}
          </View>
        </View>

        {/* Info */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Icon name="info" size={20} color="#2196F3" />
            <Text style={styles.infoText}>
              Возрастной диапазон влияет на то, какие анкеты вы будете видеть при поиске. 
              Вы также можете изменить эти настройки в любое время.
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  saveButtonTextDisabled: {
    color: '#cccccc',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  rangeDisplay: {
    alignItems: 'center',
    paddingVertical: 40,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  rangeTitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 8,
    textAlign: 'center',
  },
  rangeValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#E91E63',
    marginBottom: 12,
  },
  rangeDescription: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
    lineHeight: 20,
    paddingHorizontal: 20,
  },
  slidersContainer: {
    paddingVertical: 40,
  },
  sliderSection: {
    marginBottom: 40,
  },
  sliderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sliderLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  sliderValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderThumb: {
    backgroundColor: '#E91E63',
    width: 20,
    height: 20,
  },
  sliderRange: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  sliderRangeText: {
    fontSize: 12,
    color: '#999999',
  },
  presetsSection: {
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  presetsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  presetsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  presetButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  presetButtonSelected: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  presetButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  presetButtonTextSelected: {
    color: '#ffffff',
  },
  infoSection: {
    paddingVertical: 24,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    padding: 16,
    borderRadius: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
    marginLeft: 12,
  },
});

export default AgeRangeSettingsScreen;
