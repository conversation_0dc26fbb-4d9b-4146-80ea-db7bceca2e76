import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Autocomplete,
  Avatar,
  Badge,
  IconButton,
  Tooltip,
  Alert,
  Skeleton,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  LocationOn as LocationIcon,
  Favorite as FavoriteIcon,
  Message as MessageIcon,
  Star as StarIcon,
  Upgrade as UpgradeIcon,
  Lock as LockIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../src/providers/AuthProvider';
import { useSubscription } from '../../src/providers/SubscriptionProvider';
import { Layout } from '../../components/Layout/Layout';
import { FeatureLock } from '../../components/Subscription/FeatureLock';
import { userService } from '../../src/services/userService';
import { shoppingCenterService } from '../../src/services/shoppingCenterService';
import { User, UserSearchFilters } from '../../src/types/user.types';
import { ShoppingCenter } from '../../src/types/shoppingCenter.types';

interface ShoppingCenterSearchPageProps {
  shoppingCenters: ShoppingCenter[];
}

const ShoppingCenterSearchPage: React.FC<ShoppingCenterSearchPageProps> = ({
  shoppingCenters
}) => {
  const { t } = useTranslation('common');
  const { user, isLoading: authLoading } = useAuth();
  const { subscription, isLoading: subLoading, isPremium } = useSubscription();
  const router = useRouter();

  const [selectedCenter, setSelectedCenter] = useState<ShoppingCenter | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState<UserSearchFilters>({
    ageRange: [18, 65],
    distance: 50,
    interests: [],
    isOnline: false,
  });

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login');
    }
  }, [user, authLoading, router]);

  const handleCenterSelect = async (center: ShoppingCenter | null) => {
    if (!center) {
      setSelectedCenter(null);
      setUsers([]);
      return;
    }

    setSelectedCenter(center);
    setIsLoading(true);

    try {
      const searchResults = await userService.searchByShoppingCenter(center.id, filters);
      setUsers(searchResults);
    } catch (error) {
      console.error('Error searching users:', error);
      setUsers([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLikeUser = async (userId: string) => {
    try {
      await userService.likeUser(userId);
      // Обновляем состояние пользователя
      setUsers(prev => prev.map(u => 
        u.id === userId ? { ...u, isLiked: true } : u
      ));
    } catch (error) {
      console.error('Error liking user:', error);
    }
  };

  const handleMessageUser = (userId: string) => {
    router.push(`/messages?userId=${userId}`);
  };

  if (authLoading || subLoading) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Skeleton variant="text" height={60} />
          <Skeleton variant="rectangular" height={400} sx={{ mt: 2 }} />
        </Container>
      </Layout>
    );
  }

  if (!user) {
    return null;
  }

  // Проверка доступа к премиум функции
  if (!isPremium) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <FeatureLock
            title="Поиск по торговым центрам"
            description="Находите людей, которые посещают те же торговые центры, что и вы"
            features={[
              'Поиск по любимым ТЦ других пользователей',
              'Фильтрация по возрасту и интересам',
              'Просмотр активности в ТЦ',
              'Приоритет в результатах поиска'
            ]}
            requiredPlan="premium"
            onUpgrade={() => router.push('/subscription/shopping-centers')}
          />
        </Container>
      </Layout>
    );
  }

  return (
    <>
      <Head>
        <title>Поиск по торговым центрам - Likes Love</title>
        <meta 
          name="description" 
          content="Находите людей, которые посещают те же торговые центры. Премиум функция для эффективных знакомств." 
        />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Layout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          {/* Header */}
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h4">
                Поиск по ТЦ
              </Typography>
              <Chip
                label="Premium"
                color="primary"
                size="small"
                icon={<StarIcon />}
              />
            </Box>
            <Typography variant="body1" color="text.secondary">
              Находите людей, которые посещают те же торговые центры, что и вы
            </Typography>
          </Box>

          {/* Shopping Center Selector */}
          <Box sx={{ mb: 4 }}>
            <Autocomplete
              options={shoppingCenters}
              getOptionLabel={(option) => option.name}
              value={selectedCenter}
              onChange={(_, newValue) => handleCenterSelect(newValue)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Выберите торговый центр"
                  placeholder="Начните вводить название ТЦ..."
                  InputProps={{
                    ...params.InputProps,
                    startAdornment: <SearchIcon color="action" sx={{ mr: 1 }} />,
                  }}
                />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Avatar
                      src={option.image}
                      alt={option.name}
                      sx={{ width: 40, height: 40, mr: 2 }}
                    />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle2">
                        {option.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {option.address}
                      </Typography>
                    </Box>
                    <Badge badgeContent={option.usersCount} color="primary" />
                  </Box>
                </Box>
              )}
              fullWidth
              size="large"
            />
          </Box>

          {/* Filters */}
          {selectedCenter && (
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Фильтры поиска
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Возраст от"
                    type="number"
                    value={filters.ageRange[0]}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      ageRange: [parseInt(e.target.value), prev.ageRange[1]]
                    }))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    label="Возраст до"
                    type="number"
                    value={filters.ageRange[1]}
                    onChange={(e) => setFilters(prev => ({
                      ...prev,
                      ageRange: [prev.ageRange[0], parseInt(e.target.value)]
                    }))}
                    fullWidth
                    size="small"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel>Статус</InputLabel>
                    <Select
                      value={filters.isOnline ? 'online' : 'all'}
                      onChange={(e) => setFilters(prev => ({
                        ...prev,
                        isOnline: e.target.value === 'online'
                      }))}
                    >
                      <MenuItem value="all">Все</MenuItem>
                      <MenuItem value="online">Только онлайн</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Button
                    variant="outlined"
                    startIcon={<FilterIcon />}
                    onClick={() => selectedCenter && handleCenterSelect(selectedCenter)}
                    fullWidth
                    disabled={isLoading}
                  >
                    Применить
                  </Button>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Results */}
          {selectedCenter && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Пользователи в {selectedCenter.name}
              </Typography>
              
              {isLoading ? (
                <Grid container spacing={3}>
                  {[...Array(6)].map((_, index) => (
                    <Grid item xs={12} sm={6} md={4} key={index}>
                      <Card>
                        <Skeleton variant="rectangular" height={200} />
                        <CardContent>
                          <Skeleton variant="text" height={32} />
                          <Skeleton variant="text" height={24} />
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : users.length === 0 ? (
                <Alert severity="info">
                  В этом торговом центре пока нет пользователей с указанными параметрами
                </Alert>
              ) : (
                <Grid container spacing={3}>
                  <AnimatePresence>
                    {users.map((user) => (
                      <Grid item xs={12} sm={6} md={4} key={user.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card 
                            sx={{ 
                              height: '100%',
                              '&:hover': {
                                transform: 'translateY(-4px)',
                                boxShadow: 4,
                              },
                              transition: 'all 0.2s ease-in-out',
                            }}
                          >
                            <CardMedia
                              component="img"
                              height="200"
                              image={user.photos[0]?.url || '/images/user-placeholder.jpg'}
                              alt={user.name}
                              sx={{ cursor: 'pointer' }}
                              onClick={() => router.push(`/profile/${user.id}`)}
                            />
                            
                            <CardContent>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                                <Typography variant="h6">
                                  {user.name}, {user.age}
                                </Typography>
                                {user.isOnline && (
                                  <Chip label="Онлайн" color="success" size="small" />
                                )}
                              </Box>
                              
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <LocationIcon fontSize="small" color="action" />
                                <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                                  {user.distance}км от вас
                                </Typography>
                              </Box>

                              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                Последний визит: {user.lastVisit ? 
                                  new Date(user.lastVisit).toLocaleDateString('ru-RU') : 
                                  'Недавно'
                                }
                              </Typography>

                              <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title={user.isLiked ? "Уже понравился" : "Нравится"}>
                                  <IconButton
                                    color={user.isLiked ? "error" : "default"}
                                    onClick={() => !user.isLiked && handleLikeUser(user.id)}
                                    disabled={user.isLiked}
                                  >
                                    <FavoriteIcon />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Написать сообщение">
                                  <IconButton
                                    color="primary"
                                    onClick={() => handleMessageUser(user.id)}
                                  >
                                    <MessageIcon />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </CardContent>
                          </Card>
                        </motion.div>
                      </Grid>
                    ))}
                  </AnimatePresence>
                </Grid>
              )}
            </Box>
          )}

          {!selectedCenter && (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" gutterBottom>
                Выберите торговый центр
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Начните поиск, выбрав торговый центр из списка выше
              </Typography>
            </Box>
          )}
        </Container>
      </Layout>
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  try {
    const shoppingCenters = await shoppingCenterService.getAll();

    return {
      props: {
        shoppingCenters,
        ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
      },
    };
  } catch (error) {
    return {
      props: {
        shoppingCenters: [],
        ...(await serverSideTranslations(locale ?? 'ru', ['common'])),
      },
    };
  }
};

export default ShoppingCenterSearchPage;
