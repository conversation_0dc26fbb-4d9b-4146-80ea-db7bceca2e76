import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Appearance,
  useColorScheme
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ThemeScreenProps {}

interface ThemeOption {
  id: 'light' | 'dark' | 'system';
  name: string;
  description: string;
  icon: string;
  preview: {
    backgroundColor: string;
    cardColor: string;
    textColor: string;
    accentColor: string;
  };
}

interface ThemeSettings {
  selectedTheme: 'light' | 'dark' | 'system';
  autoSwitchTime?: {
    enabled: boolean;
    lightTime: string;
    darkTime: string;
  };
  customColors?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    largeText: boolean;
  };
}

const ThemeScreen: React.FC<ThemeScreenProps> = () => {
  const navigation = useNavigation();
  const systemColorScheme = useColorScheme();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<ThemeSettings | null>(null);
  const [error, setError] = useState<string | null>(null);

  const themeOptions: ThemeOption[] = [
    {
      id: 'light',
      name: 'Светлая тема',
      description: 'Классическая светлая тема для дневного использования',
      icon: 'light-mode',
      preview: {
        backgroundColor: '#ffffff',
        cardColor: '#f8f8f8',
        textColor: '#333333',
        accentColor: '#E91E63'
      }
    },
    {
      id: 'dark',
      name: 'Темная тема',
      description: 'Темная тема для комфортного использования в темное время',
      icon: 'dark-mode',
      preview: {
        backgroundColor: '#121212',
        cardColor: '#1e1e1e',
        textColor: '#ffffff',
        accentColor: '#E91E63'
      }
    },
    {
      id: 'system',
      name: 'Системная тема',
      description: 'Автоматическое переключение в зависимости от настроек системы',
      icon: 'settings-brightness',
      preview: {
        backgroundColor: systemColorScheme === 'dark' ? '#121212' : '#ffffff',
        cardColor: systemColorScheme === 'dark' ? '#1e1e1e' : '#f8f8f8',
        textColor: systemColorScheme === 'dark' ? '#ffffff' : '#333333',
        accentColor: '#E91E63'
      }
    }
  ];

  useEffect(() => {
    loadThemeSettings();
  }, []);

  const loadThemeSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('themeSettings');
      let themeSettings: ThemeSettings;

      if (savedSettings) {
        themeSettings = JSON.parse(savedSettings);
      } else {
        // Default settings
        themeSettings = {
          selectedTheme: 'system',
          autoSwitchTime: {
            enabled: false,
            lightTime: '07:00',
            darkTime: '21:00'
          },
          accessibility: {
            highContrast: false,
            reducedMotion: false,
            largeText: false
          }
        };
      }

      // TODO: Load from API/user preferences
      await new Promise(resolve => setTimeout(resolve, 500));

      setSettings(themeSettings);
    } catch (err: any) {
      setError('Ошибка загрузки настроек темы');
      console.error('Theme settings loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveThemeSettings = async (updatedSettings: Partial<ThemeSettings>) => {
    try {
      setSaving(true);

      const newSettings = { ...settings, ...updatedSettings };

      // Save to AsyncStorage
      await AsyncStorage.setItem('themeSettings', JSON.stringify(newSettings));

      // TODO: Apply theme change to app
      // This would typically involve updating theme context
      await new Promise(resolve => setTimeout(resolve, 500));

      setSettings(newSettings as ThemeSettings);

      Alert.alert(
        'Тема изменена',
        'Новая тема применена успешно',
        [{ text: 'OK' }]
      );

    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить тему');
    } finally {
      setSaving(false);
    }
  };

  const handleThemeSelect = async (themeId: 'light' | 'dark' | 'system') => {
    if (!settings || themeId === settings.selectedTheme) return;

    await saveThemeSettings({ selectedTheme: themeId });
  };

  const handleAccessibilityToggle = async (key: keyof ThemeSettings['accessibility'], value: boolean) => {
    if (!settings) return;

    await saveThemeSettings({
      accessibility: {
        ...settings.accessibility,
        [key]: value
      }
    });
  };

  const handleAutoSwitchToggle = async (enabled: boolean) => {
    if (!settings) return;

    await saveThemeSettings({
      autoSwitchTime: {
        ...settings.autoSwitchTime!,
        enabled
      }
    });
  };

  const getCurrentThemePreview = (): ThemeOption['preview'] => {
    if (!settings) return themeOptions[0].preview;

    const selectedOption = themeOptions.find(option => option.id === settings.selectedTheme);
    return selectedOption?.preview || themeOptions[0].preview;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек темы...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !settings) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error || 'Настройки не найдены'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadThemeSettings}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Тема оформления</Text>
        <View style={styles.headerRight}>
          {saving && <ActivityIndicator size="small" color="#E91E63" />}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Theme Preview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Текущая тема</Text>
          <View style={styles.currentThemePreview}>
            <View style={[styles.previewContainer, { backgroundColor: getCurrentThemePreview().backgroundColor }]}>
              <View style={[styles.previewCard, { backgroundColor: getCurrentThemePreview().cardColor }]}>
                <View style={styles.previewHeader}>
                  <View style={[styles.previewAvatar, { backgroundColor: getCurrentThemePreview().accentColor }]} />
                  <View style={styles.previewText}>
                    <View style={[styles.previewLine, { backgroundColor: getCurrentThemePreview().textColor }]} />
                    <View style={[styles.previewLineSmall, { backgroundColor: getCurrentThemePreview().textColor + '60' }]} />
                  </View>
                </View>
                <View style={[styles.previewButton, { backgroundColor: getCurrentThemePreview().accentColor }]} />
              </View>
            </View>
            <Text style={styles.currentThemeName}>
              {themeOptions.find(option => option.id === settings.selectedTheme)?.name}
            </Text>
          </View>
        </View>

        {/* Theme Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Выбор темы</Text>
          {themeOptions.map((theme) => (
            <TouchableOpacity
              key={theme.id}
              style={[
                styles.themeOption,
                settings.selectedTheme === theme.id && styles.themeOptionSelected
              ]}
              onPress={() => handleThemeSelect(theme.id)}
              disabled={saving}
            >
              <View style={styles.themeOptionLeft}>
                <Icon name={theme.icon} size={24} color="#E91E63" />
                <View style={styles.themeOptionInfo}>
                  <Text style={styles.themeOptionName}>{theme.name}</Text>
                  <Text style={styles.themeOptionDescription}>{theme.description}</Text>
                </View>
              </View>

              <View style={styles.themeOptionRight}>
                <View style={[styles.themePreview, { backgroundColor: theme.preview.backgroundColor }]}>
                  <View style={[styles.themePreviewCard, { backgroundColor: theme.preview.cardColor }]}>
                    <View style={[styles.themePreviewDot, { backgroundColor: theme.preview.accentColor }]} />
                  </View>
                </View>

                {settings.selectedTheme === theme.id && (
                  <Icon name="check-circle" size={24} color="#4CAF50" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Auto Switch */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Автоматическое переключение</Text>
          <Text style={styles.sectionDescription}>
            Автоматически переключать между светлой и темной темой в зависимости от времени суток
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Включить автопереключение</Text>
              <Text style={styles.settingDescription}>
                Светлая тема с {settings.autoSwitchTime?.lightTime}, темная с {settings.autoSwitchTime?.darkTime}
              </Text>
            </View>
            <TouchableOpacity
              style={[
                styles.switch,
                settings.autoSwitchTime?.enabled && styles.switchActive
              ]}
              onPress={() => handleAutoSwitchToggle(!settings.autoSwitchTime?.enabled)}
              disabled={saving}
            >
              <View style={[
                styles.switchThumb,
                settings.autoSwitchTime?.enabled && styles.switchThumbActive
              ]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Accessibility */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Доступность</Text>
          <Text style={styles.sectionDescription}>
            Настройки для улучшения доступности интерфейса
          </Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Высокий контраст</Text>
              <Text style={styles.settingDescription}>Увеличить контрастность для лучшей читаемости</Text>
            </View>
            <TouchableOpacity
              style={[
                styles.switch,
                settings.accessibility.highContrast && styles.switchActive
              ]}
              onPress={() => handleAccessibilityToggle('highContrast', !settings.accessibility.highContrast)}
              disabled={saving}
            >
              <View style={[
                styles.switchThumb,
                settings.accessibility.highContrast && styles.switchThumbActive
              ]} />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Уменьшить анимации</Text>
              <Text style={styles.settingDescription}>Снизить количество анимаций в интерфейсе</Text>
            </View>
            <TouchableOpacity
              style={[
                styles.switch,
                settings.accessibility.reducedMotion && styles.switchActive
              ]}
              onPress={() => handleAccessibilityToggle('reducedMotion', !settings.accessibility.reducedMotion)}
              disabled={saving}
            >
              <View style={[
                styles.switchThumb,
                settings.accessibility.reducedMotion && styles.switchThumbActive
              ]} />
            </TouchableOpacity>
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Крупный текст</Text>
              <Text style={styles.settingDescription}>Увеличить размер шрифта для лучшей читаемости</Text>
            </View>
            <TouchableOpacity
              style={[
                styles.switch,
                settings.accessibility.largeText && styles.switchActive
              ]}
              onPress={() => handleAccessibilityToggle('largeText', !settings.accessibility.largeText)}
              disabled={saving}
            >
              <View style={[
                styles.switchThumb,
                settings.accessibility.largeText && styles.switchThumbActive
              ]} />
            </TouchableOpacity>
          </View>
        </View>

        {/* System Info */}
        <View style={styles.section}>
          <View style={styles.systemInfoCard}>
            <Icon name="info" size={24} color="#E91E63" />
            <View style={styles.systemInfoContent}>
              <Text style={styles.systemInfoTitle}>Системная тема</Text>
              <Text style={styles.systemInfoDescription}>
                Текущая системная тема: {systemColorScheme === 'dark' ? 'Темная' : 'Светлая'}
              </Text>
              <Text style={styles.systemInfoDescription}>
                При выборе "Системная тема" приложение будет автоматически следовать настройкам вашего устройства.
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
    alignItems: 'flex-end',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    lineHeight: 20,
  },
  currentThemePreview: {
    alignItems: 'center',
  },
  previewContainer: {
    width: 200,
    height: 120,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  previewCard: {
    flex: 1,
    borderRadius: 12,
    padding: 12,
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  previewAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  previewText: {
    flex: 1,
  },
  previewLine: {
    height: 8,
    borderRadius: 4,
    marginBottom: 4,
    width: '70%',
  },
  previewLineSmall: {
    height: 6,
    borderRadius: 3,
    width: '50%',
  },
  previewButton: {
    height: 20,
    borderRadius: 10,
    width: '60%',
  },
  currentThemeName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  themeOptionSelected: {
    backgroundColor: '#f8fff8',
    borderRadius: 8,
    marginHorizontal: -4,
  },
  themeOptionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  themeOptionInfo: {
    marginLeft: 16,
    flex: 1,
  },
  themeOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  themeOptionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  themeOptionRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  themePreview: {
    width: 40,
    height: 40,
    borderRadius: 8,
    padding: 6,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  themePreviewCard: {
    flex: 1,
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  themePreviewDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  switchActive: {
    backgroundColor: '#E91E63',
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  switchThumbActive: {
    transform: [{ translateX: 20 }],
  },
  systemInfoCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  systemInfoContent: {
    flex: 1,
    marginLeft: 12,
  },
  systemInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  systemInfoDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  bottomPadding: {
    height: 40,
  },
});

export default ThemeScreen;