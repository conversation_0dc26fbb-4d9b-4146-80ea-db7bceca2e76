import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  FlatList,
  RefreshControl,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

interface CoffeeShopsScreenProps {}

interface CoffeeShop {
  id: string;
  name: string;
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    formattedAddress: string;
  };
  rating: {
    average: number;
    count: number;
    aspects: {
      coffee: number;
      atmosphere: number;
      service: number;
      price: number;
      wifi: number;
    };
  };
  priceRange: '$' | '$$' | '$$$' | '$$$$';
  photos: Array<{
    id: string;
    url: string;
    thumbnailUrl: string;
  }>;
  workingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  features: {
    wifi: boolean;
    powerOutlets: boolean;
    quietZone: boolean;
    petFriendly: boolean;
    takeaway: boolean;
    delivery: boolean;
    parking: boolean;
    terrace: boolean;
    liveMusic: boolean;
    boardGames: boolean;
  };
  menu: {
    hasBreakfast: boolean;
    hasLunch: boolean;
    hasDesserts: boolean;
    hasVegan: boolean;
    hasAlcohol: boolean;
  };
  distance?: number;
  estimatedTime?: string;
  isOpen?: boolean;
  isFavorite: boolean;
  tags: string[];
  averageCheck: {
    min: number;
    max: number;
    currency: string;
  };
}

type SortOption = 'distance' | 'rating' | 'price' | 'name' | 'popularity';
type FilterOption = 'all' | 'open' | 'wifi' | 'quiet' | 'takeaway' | 'delivery';
type ViewMode = 'list' | 'map';

const CoffeeShopsScreen: React.FC<CoffeeShopsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [coffeeShops, setCoffeeShops] = useState<CoffeeShop[]>([]);
  const [filteredShops, setFilteredShops] = useState<CoffeeShop[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('distance');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [userLocation, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [error, setError] = useState<string | null>(null);

  const filterOptions = [
    { id: 'all', name: 'Все', icon: 'apps' },
    { id: 'open', name: 'Открыто', icon: 'schedule' },
    { id: 'wifi', name: 'Wi-Fi', icon: 'wifi' },
    { id: 'quiet', name: 'Тихо', icon: 'volume-off' },
    { id: 'takeaway', name: 'На вынос', icon: 'shopping-bag' },
    { id: 'delivery', name: 'Доставка', icon: 'delivery-dining' },
  ];

  useFocusEffect(
    useCallback(() => {
      loadCoffeeShops();
      getCurrentLocation();
    }, [])
  );

  useEffect(() => {
    filterAndSortShops();
  }, [coffeeShops, searchQuery, sortBy, filterBy]);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
      }
    } catch (error) {
      console.log('Location error:', error);
    }
  };

  const loadCoffeeShops = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockCoffeeShops: CoffeeShop[] = [
        {
          id: '1',
          name: 'Coffee Bean',
          description: 'Уютная кофейня с авторским кофе и домашней выпечкой',
          location: {
            latitude: 55.7558,
            longitude: 37.6176,
            address: 'ул. Тверская, 12',
            city: 'Москва',
            formattedAddress: 'ул. Тверская, 12, Москва'
          },
          rating: {
            average: 4.6,
            count: 127,
            aspects: {
              coffee: 4.8,
              atmosphere: 4.5,
              service: 4.4,
              price: 4.2,
              wifi: 4.7
            }
          },
          priceRange: '$$',
          photos: [
            {
              id: '1',
              url: 'https://example.com/photo1.jpg',
              thumbnailUrl: 'https://example.com/thumb1.jpg'
            }
          ],
          workingHours: {
            monday: { open: '07:00', close: '22:00', isOpen: true },
            tuesday: { open: '07:00', close: '22:00', isOpen: true },
            wednesday: { open: '07:00', close: '22:00', isOpen: true },
            thursday: { open: '07:00', close: '22:00', isOpen: true },
            friday: { open: '07:00', close: '23:00', isOpen: true },
            saturday: { open: '08:00', close: '23:00', isOpen: true },
            sunday: { open: '08:00', close: '21:00', isOpen: true }
          },
          features: {
            wifi: true,
            powerOutlets: true,
            quietZone: true,
            petFriendly: false,
            takeaway: true,
            delivery: false,
            parking: false,
            terrace: false,
            liveMusic: false,
            boardGames: true
          },
          menu: {
            hasBreakfast: true,
            hasLunch: true,
            hasDesserts: true,
            hasVegan: true,
            hasAlcohol: false
          },
          distance: 0.8,
          estimatedTime: '10 мин',
          isOpen: true,
          isFavorite: true,
          tags: ['кофе', 'работа', 'wi-fi', 'тихо'],
          averageCheck: {
            min: 200,
            max: 500,
            currency: 'RUB'
          }
        },
        {
          id: '2',
          name: 'Brew & Co',
          description: 'Специализированная кофейня с зернами прямой поставки',
          location: {
            latitude: 55.7489,
            longitude: 37.6176,
            address: 'ул. Арбат, 18',
            city: 'Москва',
            formattedAddress: 'ул. Арбат, 18, Москва'
          },
          rating: {
            average: 4.8,
            count: 89,
            aspects: {
              coffee: 4.9,
              atmosphere: 4.6,
              service: 4.8,
              price: 3.9,
              wifi: 4.5
            }
          },
          priceRange: '$$$',
          photos: [
            {
              id: '2',
              url: 'https://example.com/photo2.jpg',
              thumbnailUrl: 'https://example.com/thumb2.jpg'
            }
          ],
          workingHours: {
            monday: { open: '08:00', close: '20:00', isOpen: true },
            tuesday: { open: '08:00', close: '20:00', isOpen: true },
            wednesday: { open: '08:00', close: '20:00', isOpen: true },
            thursday: { open: '08:00', close: '20:00', isOpen: true },
            friday: { open: '08:00', close: '21:00', isOpen: true },
            saturday: { open: '09:00', close: '21:00', isOpen: true },
            sunday: { open: '09:00', close: '19:00', isOpen: true }
          },
          features: {
            wifi: true,
            powerOutlets: false,
            quietZone: false,
            petFriendly: true,
            takeaway: true,
            delivery: true,
            parking: true,
            terrace: true,
            liveMusic: false,
            boardGames: false
          },
          menu: {
            hasBreakfast: true,
            hasLunch: false,
            hasDesserts: true,
            hasVegan: true,
            hasAlcohol: false
          },
          distance: 1.2,
          estimatedTime: '15 мин',
          isOpen: true,
          isFavorite: false,
          tags: ['спешиалти', 'терраса', 'доставка'],
          averageCheck: {
            min: 300,
            max: 800,
            currency: 'RUB'
          }
        }
      ];

      setCoffeeShops(mockCoffeeShops);
    } catch (err: any) {
      setError('Ошибка загрузки кофеен');
      console.error('Coffee shops loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadCoffeeShops();
    setRefreshing(false);
  }, []);

  const filterAndSortShops = () => {
    let filtered = [...coffeeShops];

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(shop =>
        shop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shop.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shop.location.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shop.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    switch (filterBy) {
      case 'open':
        filtered = filtered.filter(shop => shop.isOpen);
        break;
      case 'wifi':
        filtered = filtered.filter(shop => shop.features.wifi);
        break;
      case 'quiet':
        filtered = filtered.filter(shop => shop.features.quietZone);
        break;
      case 'takeaway':
        filtered = filtered.filter(shop => shop.features.takeaway);
        break;
      case 'delivery':
        filtered = filtered.filter(shop => shop.features.delivery);
        break;
    }

    // Sort shops
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        case 'rating':
          return b.rating.average - a.rating.average;
        case 'price':
          const priceOrder = { '$': 1, '$$': 2, '$$$': 3, '$$$$': 4 };
          return priceOrder[a.priceRange] - priceOrder[b.priceRange];
        case 'name':
          return a.name.localeCompare(b.name);
        case 'popularity':
          return b.rating.count - a.rating.count;
        default:
          return 0;
      }
    });

    setFilteredShops(filtered);
  };

  const handleShopPress = (shop: CoffeeShop) => {
    navigation.navigate('PlaceDetail', { placeId: shop.id, placeType: 'coffee' });
  };

  const handleToggleFavorite = async (shopId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 300));

      setCoffeeShops(prev => prev.map(shop =>
        shop.id === shopId ? { ...shop, isFavorite: !shop.isFavorite } : shop
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось обновить избранное');
    }
  };

  const handleCreateMeeting = (shop: CoffeeShop) => {
    navigation.navigate('CreateMeeting', {
      selectedPlace: {
        id: shop.id,
        name: shop.name,
        address: shop.location.formattedAddress,
        latitude: shop.location.latitude,
        longitude: shop.location.longitude
      }
    });
  };

  const handleDirections = (shop: CoffeeShop) => {
    if (userLocation) {
      const url = `https://www.openstreetmap.org/directions?from=${userLocation.latitude},${userLocation.longitude}&to=${shop.location.latitude},${shop.location.longitude}`;
      // TODO: Open in browser or maps app
      Alert.alert('Навигация', 'Открыть маршрут в картах?');
    }
  };

  const getSortLabel = (option: SortOption): string => {
    switch (option) {
      case 'distance': return 'По расстоянию';
      case 'rating': return 'По рейтингу';
      case 'price': return 'По цене';
      case 'name': return 'По названию';
      case 'popularity': return 'По популярности';
      default: return '';
    }
  };

  const getFeatureIcon = (feature: string): string => {
    switch (feature) {
      case 'wifi': return 'wifi';
      case 'powerOutlets': return 'power';
      case 'quietZone': return 'volume-off';
      case 'petFriendly': return 'pets';
      case 'takeaway': return 'shopping-bag';
      case 'delivery': return 'delivery-dining';
      case 'parking': return 'local-parking';
      case 'terrace': return 'deck';
      case 'liveMusic': return 'music-note';
      case 'boardGames': return 'casino';
      default: return 'check';
    }
  };

  const getFeatureLabel = (feature: string): string => {
    switch (feature) {
      case 'wifi': return 'Wi-Fi';
      case 'powerOutlets': return 'Розетки';
      case 'quietZone': return 'Тихая зона';
      case 'petFriendly': return 'С питомцами';
      case 'takeaway': return 'На вынос';
      case 'delivery': return 'Доставка';
      case 'parking': return 'Парковка';
      case 'terrace': return 'Терраса';
      case 'liveMusic': return 'Живая музыка';
      case 'boardGames': return 'Настольные игры';
      default: return feature;
    }
  };

  const renderShopCard = ({ item: shop }: { item: CoffeeShop }) => (
    <TouchableOpacity
      style={styles.shopCard}
      onPress={() => handleShopPress(shop)}
    >
      <View style={styles.shopImageContainer}>
        <View style={styles.placeholderImage}>
          <Icon name="local-cafe" size={32} color="#8D6E63" />
        </View>

        {shop.isOpen !== undefined && (
          <View style={[styles.statusBadge, { backgroundColor: shop.isOpen ? '#4CAF50' : '#F44336' }]}>
            <Text style={styles.statusBadgeText}>
              {shop.isOpen ? 'Открыто' : 'Закрыто'}
            </Text>
          </View>
        )}

        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={() => handleToggleFavorite(shop.id)}
        >
          <Icon
            name={shop.isFavorite ? "favorite" : "favorite-border"}
            size={20}
            color={shop.isFavorite ? "#E91E63" : "#ffffff"}
          />
        </TouchableOpacity>
      </View>

      <View style={styles.shopInfo}>
        <View style={styles.shopHeader}>
          <Text style={styles.shopName} numberOfLines={1}>{shop.name}</Text>
          <Text style={styles.priceRange}>{shop.priceRange}</Text>
        </View>

        <Text style={styles.shopDescription} numberOfLines={2}>
          {shop.description}
        </Text>

        <View style={styles.shopDetails}>
          <View style={styles.ratingContainer}>
            <Icon name="star" size={16} color="#FFD700" />
            <Text style={styles.ratingText}>{shop.rating.average}</Text>
            <Text style={styles.ratingCount}>({shop.rating.count})</Text>
          </View>

          {shop.distance && (
            <View style={styles.distanceContainer}>
              <Icon name="location-on" size={16} color="#666666" />
              <Text style={styles.distanceText}>{shop.distance} км</Text>
            </View>
          )}

          {shop.estimatedTime && (
            <View style={styles.timeContainer}>
              <Icon name="access-time" size={16} color="#666666" />
              <Text style={styles.timeText}>{shop.estimatedTime}</Text>
            </View>
          )}
        </View>

        <View style={styles.featuresContainer}>
          {Object.entries(shop.features)
            .filter(([_, value]) => value)
            .slice(0, 4)
            .map(([feature, _]) => (
              <View key={feature} style={styles.featureChip}>
                <Icon name={getFeatureIcon(feature)} size={12} color="#8D6E63" />
                <Text style={styles.featureText}>{getFeatureLabel(feature)}</Text>
              </View>
            ))}
        </View>

        <View style={styles.shopFooter}>
          <Text style={styles.addressText} numberOfLines={1}>
            {shop.location.address}
          </Text>

          <View style={styles.shopActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDirections(shop)}
            >
              <Icon name="directions" size={16} color="#E91E63" />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleCreateMeeting(shop)}
            >
              <Icon name="event" size={16} color="#E91E63" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.averageCheckContainer}>
          <Text style={styles.averageCheckText}>
            Средний чек: {shop.averageCheck.min}-{shop.averageCheck.max} ₽
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMapView = () => (
    <View style={styles.mapContainer}>
      <MapView
        style={styles.map}
        initialRegion={{
          latitude: userLocation?.latitude || 55.7558,
          longitude: userLocation?.longitude || 37.6176,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {filteredShops.map((shop) => (
          <Marker
            key={shop.id}
            coordinate={{
              latitude: shop.location.latitude,
              longitude: shop.location.longitude,
            }}
            title={shop.name}
            description={shop.description}
            onPress={() => handleShopPress(shop)}
          >
            <View style={styles.customMarker}>
              <Icon name="local-cafe" size={20} color="#ffffff" />
            </View>
          </Marker>
        ))}
      </MapView>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка кофеен...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadCoffeeShops}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Кофейни</Text>
        <TouchableOpacity
          style={styles.viewModeButton}
          onPress={() => setViewMode(viewMode === 'list' ? 'map' : 'list')}
        >
          <Icon name={viewMode === 'list' ? 'map' : 'list'} size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск кофеен..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Filters */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        {filterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterChip,
              filterBy === filter.id && styles.filterChipSelected
            ]}
            onPress={() => setFilterBy(filter.id as FilterOption)}
          >
            <Icon
              name={filter.icon}
              size={16}
              color={filterBy === filter.id ? '#ffffff' : '#8D6E63'}
            />
            <Text style={[
              styles.filterChipText,
              filterBy === filter.id && styles.filterChipTextSelected
            ]}>
              {filter.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Sort and Stats */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={styles.sortButton}
          onPress={() => {
            const sortOptions: SortOption[] = ['distance', 'rating', 'price', 'name', 'popularity'];
            const currentIndex = sortOptions.indexOf(sortBy);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            setSortBy(sortOptions[nextIndex]);
          }}
        >
          <Icon name="sort" size={16} color="#E91E63" />
          <Text style={styles.sortButtonText}>{getSortLabel(sortBy)}</Text>
        </TouchableOpacity>

        <Text style={styles.statsText}>
          {filteredShops.length} из {coffeeShops.length} кофеен
        </Text>
      </View>

      {/* Content */}
      {filteredShops.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Icon name="local-cafe" size={64} color="#cccccc" />
          <Text style={styles.emptyTitle}>
            {searchQuery || filterBy !== 'all' ? 'Ничего не найдено' : 'Нет кофеен'}
          </Text>
          <Text style={styles.emptyDescription}>
            {searchQuery || filterBy !== 'all'
              ? 'Попробуйте изменить параметры поиска'
              : 'В этом районе пока нет кофеен'
            }
          </Text>
        </View>
      ) : viewMode === 'list' ? (
        <FlatList
          data={filteredShops}
          renderItem={renderShopCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={['#E91E63']}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        renderMapView()
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  viewModeButton: {
    padding: 8,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  filtersContainer: {
    paddingVertical: 8,
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 8,
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  filterChipSelected: {
    backgroundColor: '#8D6E63',
  },
  filterChipText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterChipTextSelected: {
    color: '#ffffff',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  sortButtonText: {
    fontSize: 14,
    color: '#E91E63',
    fontWeight: '500',
  },
  statsText: {
    fontSize: 14,
    color: '#666666',
  },
  listContainer: {
    padding: 20,
    gap: 16,
  },
  shopCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    overflow: 'hidden',
  },
  shopImageContainer: {
    height: 120,
    position: 'relative',
  },
  placeholderImage: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusBadge: {
    position: 'absolute',
    top: 12,
    left: 12,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 16,
    padding: 8,
  },
  shopInfo: {
    padding: 16,
  },
  shopHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  shopName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
  },
  priceRange: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8D6E63',
  },
  shopDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 12,
  },
  shopDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  ratingCount: {
    fontSize: 14,
    color: '#666666',
  },
  distanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  distanceText: {
    fontSize: 14,
    color: '#666666',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 14,
    color: '#666666',
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 12,
  },
  featureChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#8D6E63' + '10',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  featureText: {
    fontSize: 10,
    color: '#8D6E63',
    fontWeight: '500',
  },
  shopFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  addressText: {
    fontSize: 12,
    color: '#999999',
    flex: 1,
  },
  shopActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
  },
  averageCheckContainer: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  averageCheckText: {
    fontSize: 12,
    color: '#8D6E63',
    fontWeight: '500',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  customMarker: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#8D6E63',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default CoffeeShopsScreen;