import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  Linking,
  Dimensions,
  Share
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker, PROVIDER_GOOGLE } from 'react-native-maps';

interface PlaceDetailsScreenProps {}

interface Place {
  id: string;
  name: string;
  category: string;
  rating: number;
  distance: number;
  address: string;
  priceLevel: number;
  imageUrl: string;
  latitude: number;
  longitude: number;
  isOpen: boolean;
  openingHours?: string;
  phone?: string;
  website?: string;
  description?: string;
  photos?: string[];
  reviews?: Review[];
}

interface Review {
  id: string;
  userName: string;
  userAvatar: string;
  rating: number;
  comment: string;
  date: string;
}

interface RouteParams {
  place: Place;
}

const { width } = Dimensions.get('window');

const PlaceDetailsScreen: React.FC<PlaceDetailsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { place } = route.params as RouteParams;

  const [loading, setLoading] = useState(true);
  const [placeDetails, setPlaceDetails] = useState<Place>(place);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(0);

  useEffect(() => {
    loadPlaceDetails();
  }, []);

  const loadPlaceDetails = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call to get detailed place info
      await new Promise(resolve => setTimeout(resolve, 1000));

      const detailedPlace: Place = {
        ...place,
        description: 'Уютное место в центре города с отличной кухней и приятной атмосферой. Идеально подходит для романтических встреч и деловых обедов.',
        photos: [
          'https://example.com/photo1.jpg',
          'https://example.com/photo2.jpg',
          'https://example.com/photo3.jpg'
        ],
        reviews: [
          {
            id: '1',
            userName: 'Анна К.',
            userAvatar: 'https://example.com/avatar1.jpg',
            rating: 5,
            comment: 'Отличное место! Очень вкусная еда и приятная атмосфера.',
            date: '2024-01-10'
          },
          {
            id: '2',
            userName: 'Михаил С.',
            userAvatar: 'https://example.com/avatar2.jpg',
            rating: 4,
            comment: 'Хорошее обслуживание, но цены немного завышены.',
            date: '2024-01-08'
          }
        ]
      };

      setPlaceDetails(detailedPlace);
    } catch (error) {
      console.error('Error loading place details:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить детали места');
    } finally {
      setLoading(false);
    }
  };

  const handleCall = () => {
    if (placeDetails.phone) {
      Linking.openURL(`tel:${placeDetails.phone}`);
    } else {
      Alert.alert('Информация недоступна', 'Номер телефона не указан');
    }
  };

  const handleWebsite = () => {
    if (placeDetails.website) {
      Linking.openURL(placeDetails.website);
    } else {
      Alert.alert('Информация недоступна', 'Веб-сайт не указан');
    }
  };

  const handleDirections = () => {
    const url = `https://maps.google.com/?q=${placeDetails.latitude},${placeDetails.longitude}`;
    Linking.openURL(url);
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `Посмотри это место: ${placeDetails.name}\n${placeDetails.address}`,
        title: placeDetails.name,
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleCreateMeeting = () => {
    navigation.navigate('CreateMeeting', { selectedPlace: placeDetails });
  };

  const handleToggleFavorite = async () => {
    try {
      // TODO: Implement favorite toggle API call
      setIsFavorite(!isFavorite);
      Alert.alert(
        isFavorite ? 'Удалено из избранного' : 'Добавлено в избранное',
        isFavorite ? 'Место удалено из вашего списка избранного' : 'Место добавлено в ваш список избранного'
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось обновить избранное');
    }
  };

  const getPriceSymbol = (level: number): string => {
    return '₽'.repeat(level);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          name={i <= rating ? 'star' : 'star-border'}
          size={16}
          color="#FFD700"
        />
      );
    }
    return stars;
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка деталей...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>{placeDetails.name}</Text>
        <TouchableOpacity
          style={styles.shareButton}
          onPress={handleShare}
        >
          <Icon name="share" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Photos */}
        {placeDetails.photos && placeDetails.photos.length > 0 && (
          <View style={styles.photosContainer}>
            <ScrollView
              horizontal
              pagingEnabled
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={(event) => {
                const index = Math.round(event.nativeEvent.contentOffset.x / width);
                setSelectedPhotoIndex(index);
              }}
            >
              {placeDetails.photos.map((photo, index) => (
                <View key={index} style={styles.photoContainer}>
                  <Image source={{ uri: photo }} style={styles.photo} />
                </View>
              ))}
            </ScrollView>
            <View style={styles.photoIndicators}>
              {placeDetails.photos.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.photoIndicator,
                    selectedPhotoIndex === index && styles.photoIndicatorActive
                  ]}
                />
              ))}
            </View>
          </View>
        )}

        {/* Main Info */}
        <View style={styles.mainInfo}>
          <View style={styles.titleRow}>
            <Text style={styles.placeName}>{placeDetails.name}</Text>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={handleToggleFavorite}
            >
              <Icon
                name={isFavorite ? 'favorite' : 'favorite-border'}
                size={24}
                color={isFavorite ? '#E91E63' : '#666666'}
              />
            </TouchableOpacity>
          </View>

          <View style={styles.statusRow}>
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: placeDetails.isOpen ? '#4CAF50' : '#ff4444' }]} />
              <Text style={[styles.statusText, { color: placeDetails.isOpen ? '#4CAF50' : '#ff4444' }]}>
                {placeDetails.isOpen ? 'Открыто' : 'Закрыто'}
              </Text>
              {placeDetails.openingHours && (
                <Text style={styles.openingHours}> • {placeDetails.openingHours}</Text>
              )}
            </View>
          </View>

          <View style={styles.detailsRow}>
            <View style={styles.ratingContainer}>
              <View style={styles.starsContainer}>
                {renderStars(Math.floor(placeDetails.rating))}
              </View>
              <Text style={styles.ratingText}>{placeDetails.rating}</Text>
            </View>

            <Text style={styles.priceLevel}>{getPriceSymbol(placeDetails.priceLevel)}</Text>
            <Text style={styles.distance}>{placeDetails.distance} км</Text>
          </View>

          <Text style={styles.address}>{placeDetails.address}</Text>

          {placeDetails.description && (
            <Text style={styles.description}>{placeDetails.description}</Text>
          )}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity style={styles.actionButton} onPress={handleCall}>
            <Icon name="phone" size={20} color="#E91E63" />
            <Text style={styles.actionButtonText}>Позвонить</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleDirections}>
            <Icon name="directions" size={20} color="#E91E63" />
            <Text style={styles.actionButtonText}>Маршрут</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleWebsite}>
            <Icon name="language" size={20} color="#E91E63" />
            <Text style={styles.actionButtonText}>Сайт</Text>
          </TouchableOpacity>
        </View>

        {/* Map */}
        <View style={styles.mapSection}>
          <Text style={styles.sectionTitle}>Расположение</Text>
          <View style={styles.mapContainer}>
            <MapView
              provider={PROVIDER_GOOGLE}
              style={styles.map}
              initialRegion={{
                latitude: placeDetails.latitude,
                longitude: placeDetails.longitude,
                latitudeDelta: 0.01,
                longitudeDelta: 0.01,
              }}
              scrollEnabled={false}
              zoomEnabled={false}
              rotateEnabled={false}
              pitchEnabled={false}
            >
              <Marker
                coordinate={{
                  latitude: placeDetails.latitude,
                  longitude: placeDetails.longitude,
                }}
                title={placeDetails.name}
                description={placeDetails.address}
              />
            </MapView>
            <TouchableOpacity
              style={styles.mapOverlay}
              onPress={handleDirections}
            >
              <View style={styles.mapOverlayContent}>
                <Icon name="open-in-new" size={24} color="#E91E63" />
                <Text style={styles.mapOverlayText}>Открыть в картах</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Reviews */}
        {placeDetails.reviews && placeDetails.reviews.length > 0 && (
          <View style={styles.reviewsSection}>
            <Text style={styles.sectionTitle}>Отзывы</Text>
            {placeDetails.reviews.map((review) => (
              <View key={review.id} style={styles.reviewCard}>
                <View style={styles.reviewHeader}>
                  <Image
                    source={{ uri: review.userAvatar }}
                    style={styles.reviewAvatar}
                    defaultSource={require('../../assets/default-avatar.png')}
                  />
                  <View style={styles.reviewUserInfo}>
                    <Text style={styles.reviewUserName}>{review.userName}</Text>
                    <View style={styles.reviewRating}>
                      {renderStars(review.rating)}
                      <Text style={styles.reviewDate}> • {new Date(review.date).toLocaleDateString('ru-RU')}</Text>
                    </View>
                  </View>
                </View>
                <Text style={styles.reviewComment}>{review.comment}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Create Meeting Button */}
        <View style={styles.meetingSection}>
          <TouchableOpacity
            style={styles.createMeetingButton}
            onPress={handleCreateMeeting}
          >
            <Icon name="event" size={24} color="#ffffff" />
            <Text style={styles.createMeetingButtonText}>Создать встречу в этом месте</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 16,
  },
  shareButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  photosContainer: {
    height: 250,
    position: 'relative',
  },
  photoContainer: {
    width: width,
    height: 250,
  },
  photo: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f5f5f5',
  },
  photoIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  photoIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  photoIndicatorActive: {
    backgroundColor: '#ffffff',
  },
  mainInfo: {
    padding: 20,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  placeName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333333',
    flex: 1,
  },
  favoriteButton: {
    padding: 8,
  },
  statusRow: {
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  openingHours: {
    fontSize: 16,
    color: '#666666',
  },
  detailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 8,
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  priceLevel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
    marginRight: 16,
  },
  distance: {
    fontSize: 16,
    color: '#666666',
  },
  address: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 24,
  },
  actionsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
  mapSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  mapContainer: {
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  mapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapOverlayContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  mapOverlayText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
  reviewsSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  reviewCard: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  reviewAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#e0e0e0',
    marginRight: 12,
  },
  reviewUserInfo: {
    flex: 1,
  },
  reviewUserName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  reviewRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reviewDate: {
    fontSize: 14,
    color: '#666666',
  },
  reviewComment: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
  },
  meetingSection: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  createMeetingButton: {
    backgroundColor: '#E91E63',
    borderRadius: 16,
    paddingVertical: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
  },
  createMeetingButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default PlaceDetailsScreen;