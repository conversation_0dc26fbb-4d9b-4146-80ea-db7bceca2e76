import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  IconButton,
  Divider,
  Switch,
  FormControlLabel,
  Autocomplete,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CloudUpload as CloudUploadIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Schedule as ScheduleIcon,
  AttachMoney as AttachMoneyIcon
} from '@mui/icons-material';
import { useAuth } from '../../../contexts/AuthContext';
import Layout from '../../../components/Layout/Layout';
import { partnerService } from '../../../services/partnerService';

interface CreateVenuePageProps {}

interface VenueFormData {
  name: string;
  description: string;
  category: string;
  address: string;
  city: string;
  phone: string;
  email: string;
  website: string;
  workingHours: {
    [key: string]: {
      isOpen: boolean;
      openTime: string;
      closeTime: string;
    };
  };
  amenities: string[];
  priceRange: string;
  capacity: number;
  images: File[];
  socialMedia: {
    instagram: string;
    facebook: string;
    vk: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  policies: {
    cancellation: string;
    ageRestriction: number;
    dresscode: string;
    smoking: boolean;
    alcohol: boolean;
  };
}

const CreateVenuePage: React.FC<CreateVenuePageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<VenueFormData>({
    name: '',
    description: '',
    category: '',
    address: '',
    city: '',
    phone: '',
    email: '',
    website: '',
    workingHours: {
      monday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
      tuesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
      wednesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
      thursday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
      friday: { isOpen: true, openTime: '09:00', closeTime: '23:00' },
      saturday: { isOpen: true, openTime: '10:00', closeTime: '23:00' },
      sunday: { isOpen: true, openTime: '10:00', closeTime: '22:00' }
    },
    amenities: [],
    priceRange: '',
    capacity: 0,
    images: [],
    socialMedia: {
      instagram: '',
      facebook: '',
      vk: ''
    },
    coordinates: {
      latitude: 55.7558,
      longitude: 37.6176
    },
    policies: {
      cancellation: '',
      ageRestriction: 0,
      dresscode: '',
      smoking: false,
      alcohol: false
    }
  });

  const steps = [
    'Основная информация',
    'Контакты и адрес',
    'Режим работы',
    'Удобства и политики',
    'Фотографии'
  ];

  const categories = [
    { value: 'restaurant', label: 'Ресторан' },
    { value: 'cafe', label: 'Кафе' },
    { value: 'bar', label: 'Бар' },
    { value: 'club', label: 'Клуб' },
    { value: 'lounge', label: 'Лаунж' },
    { value: 'hotel', label: 'Отель' },
    { value: 'spa', label: 'СПА' },
    { value: 'cinema', label: 'Кинотеатр' },
    { value: 'theater', label: 'Театр' },
    { value: 'museum', label: 'Музей' },
    { value: 'park', label: 'Парк' },
    { value: 'other', label: 'Другое' }
  ];

  const availableAmenities = [
    'Wi-Fi',
    'Парковка',
    'Кондиционер',
    'Терраса',
    'Живая музыка',
    'Караоке',
    'Танцпол',
    'VIP зона',
    'Детская зона',
    'Банкетный зал',
    'Проектор',
    'Звуковая система',
    'Фотозона',
    'Гардероб',
    'Доступ для инвалидов'
  ];

  const priceRanges = [
    { value: 'budget', label: '₽ - Бюджетно (до 1000₽)' },
    { value: 'moderate', label: '₽₽ - Умеренно (1000-3000₽)' },
    { value: 'expensive', label: '₽₽₽ - Дорого (3000-5000₽)' },
    { value: 'luxury', label: '₽₽₽₽ - Люкс (от 5000₽)' }
  ];

  const daysOfWeek = [
    { key: 'monday', label: 'Понедельник' },
    { key: 'tuesday', label: 'Вторник' },
    { key: 'wednesday', label: 'Среда' },
    { key: 'thursday', label: 'Четверг' },
    { key: 'friday', label: 'Пятница' },
    { key: 'saturday', label: 'Суббота' },
    { key: 'sunday', label: 'Воскресенье' }
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/partner/venues/create');
      return;
    }

    if (user?.role !== 'partner') {
      router.push('/partner/apply');
      return;
    }
  }, [isAuthenticated, user, router]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof VenueFormData],
        [field]: value
      }
    }));
  };

  const handleWorkingHoursChange = (day: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      workingHours: {
        ...prev.workingHours,
        [day]: {
          ...prev.workingHours[day],
          [field]: value
        }
      }
    }));
  };

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...files].slice(0, 10) // Max 10 images
    }));
  };

  const handleImageRemove = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0:
        return !!(formData.name && formData.description && formData.category);
      case 1:
        return !!(formData.address && formData.city && formData.phone);
      case 2:
        return true; // Working hours are optional
      case 3:
        return !!(formData.priceRange && formData.capacity > 0);
      case 4:
        return formData.images.length > 0;
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
      setError(null);
    } else {
      setError('Пожалуйста, заполните все обязательные поля');
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setError(null);
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) {
      setError('Пожалуйста, заполните все обязательные поля');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await partnerService.createVenue(formData);

      router.push('/partner/venues?created=true');
    } catch (err: any) {
      setError('Ошибка создания заведения');
      console.error('Venue creation error:', err);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Название заведения *"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Например: Кафе 'Встреча'"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Описание *"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Расскажите о вашем заведении, его особенностях и атмосфере"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Категория *</InputLabel>
                <Select
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                >
                  {categories.map((category) => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Вместимость (человек) *"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', parseInt(e.target.value) || 0)}
                inputProps={{ min: 1 }}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Адрес *"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Улица, дом, офис"
                InputProps={{
                  startAdornment: <LocationIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Город *"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="Москва"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Телефон *"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="+7 (999) 123-45-67"
                InputProps={{
                  startAdornment: <PhoneIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="email"
                label="Email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Веб-сайт"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                placeholder="https://venue.com"
                InputProps={{
                  startAdornment: <WebsiteIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Социальные сети
              </Typography>
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Instagram"
                value={formData.socialMedia.instagram}
                onChange={(e) => handleNestedInputChange('socialMedia', 'instagram', e.target.value)}
                placeholder="@username"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Facebook"
                value={formData.socialMedia.facebook}
                onChange={(e) => handleNestedInputChange('socialMedia', 'facebook', e.target.value)}
                placeholder="facebook.com/page"
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="VK"
                value={formData.socialMedia.vk}
                onChange={(e) => handleNestedInputChange('socialMedia', 'vk', e.target.value)}
                placeholder="vk.com/page"
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                <ScheduleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                Режим работы
              </Typography>
            </Grid>

            {daysOfWeek.map((day) => (
              <Grid item xs={12} key={day.key}>
                <Paper sx={{ p: 2 }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={3}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={formData.workingHours[day.key].isOpen}
                            onChange={(e) => handleWorkingHoursChange(day.key, 'isOpen', e.target.checked)}
                          />
                        }
                        label={day.label}
                      />
                    </Grid>

                    {formData.workingHours[day.key].isOpen && (
                      <>
                        <Grid item xs={6} sm={3}>
                          <TextField
                            fullWidth
                            type="time"
                            label="Открытие"
                            value={formData.workingHours[day.key].openTime}
                            onChange={(e) => handleWorkingHoursChange(day.key, 'openTime', e.target.value)}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>

                        <Grid item xs={6} sm={3}>
                          <TextField
                            fullWidth
                            type="time"
                            label="Закрытие"
                            value={formData.workingHours[day.key].closeTime}
                            onChange={(e) => handleWorkingHoursChange(day.key, 'closeTime', e.target.value)}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>
                      </>
                    )}

                    {!formData.workingHours[day.key].isOpen && (
                      <Grid item xs={12} sm={6}>
                        <Typography color="text.secondary">
                          Выходной день
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                </Paper>
              </Grid>
            ))}
          </Grid>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Ценовая категория *</InputLabel>
                <Select
                  value={formData.priceRange}
                  onChange={(e) => handleInputChange('priceRange', e.target.value)}
                  startAdornment={<AttachMoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />}
                >
                  {priceRanges.map((range) => (
                    <MenuItem key={range.value} value={range.value}>
                      {range.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Удобства
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {availableAmenities.map((amenity) => (
                  <Chip
                    key={amenity}
                    label={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    color={formData.amenities.includes(amenity) ? 'primary' : 'default'}
                    variant={formData.amenities.includes(amenity) ? 'filled' : 'outlined'}
                  />
                ))}
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Политики заведения
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="number"
                label="Возрастное ограничение"
                value={formData.policies.ageRestriction}
                onChange={(e) => handleNestedInputChange('policies', 'ageRestriction', parseInt(e.target.value) || 0)}
                inputProps={{ min: 0, max: 21 }}
                helperText="0 - без ограничений"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Дресс-код"
                value={formData.policies.dresscode}
                onChange={(e) => handleNestedInputChange('policies', 'dresscode', e.target.value)}
                placeholder="Например: Smart casual"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.policies.smoking}
                    onChange={(e) => handleNestedInputChange('policies', 'smoking', e.target.checked)}
                  />
                }
                label="Разрешено курение"
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.policies.alcohol}
                    onChange={(e) => handleNestedInputChange('policies', 'alcohol', e.target.checked)}
                  />
                }
                label="Подача алкоголя"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Политика отмены"
                value={formData.policies.cancellation}
                onChange={(e) => handleNestedInputChange('policies', 'cancellation', e.target.value)}
                placeholder="Опишите условия отмены бронирования"
              />
            </Grid>
          </Grid>
        );

      case 4:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Фотографии заведения *
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Загрузите до 10 фотографий вашего заведения. Первая фотография будет использована как главная.
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Paper
                sx={{
                  p: 3,
                  border: '2px dashed',
                  borderColor: 'primary.main',
                  textAlign: 'center',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  }
                }}
                component="label"
              >
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                  style={{ display: 'none' }}
                />
                <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Нажмите для загрузки фотографий
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Поддерживаются форматы: JPG, PNG, WebP
                </Typography>
              </Paper>
            </Grid>

            {formData.images.length > 0 && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Загруженные фотографии ({formData.images.length}/10)
                </Typography>
                <Grid container spacing={2}>
                  {formData.images.map((image, index) => (
                    <Grid item xs={6} sm={4} md={3} key={index}>
                      <Paper sx={{ position: 'relative', aspectRatio: '1' }}>
                        <Box
                          sx={{
                            width: '100%',
                            height: '100%',
                            backgroundImage: `url(${URL.createObjectURL(image)})`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center',
                            borderRadius: 1
                          }}
                        />
                        <IconButton
                          sx={{
                            position: 'absolute',
                            top: 4,
                            right: 4,
                            backgroundColor: 'rgba(0,0,0,0.7)',
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'rgba(0,0,0,0.9)'
                            }
                          }}
                          size="small"
                          onClick={() => handleImageRemove(index)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                        {index === 0 && (
                          <Chip
                            label="Главная"
                            size="small"
                            color="primary"
                            sx={{
                              position: 'absolute',
                              bottom: 4,
                              left: 4
                            }}
                          />
                        )}
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Grid>
            )}
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Layout>
      <Head>
        <title>Создать заведение - Партнерский портал</title>
        <meta name="description" content="Добавьте свое заведение в партнерский портал LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={() => router.back()} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Создать заведение
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Добавьте информацию о вашем заведении для привлечения клиентов
            </Typography>
          </Box>
        </Box>

        {/* Stepper */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Form Content */}
        <Paper sx={{ p: 4, mb: 4 }}>
          {renderStepContent(activeStep)}
        </Paper>

        {/* Navigation */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            onClick={handleBack}
            disabled={activeStep === 0}
            variant="outlined"
          >
            Назад
          </Button>

          <Box sx={{ display: 'flex', gap: 2 }}>
            {activeStep < steps.length - 1 ? (
              <Button
                onClick={handleNext}
                variant="contained"
                disabled={!validateStep(activeStep)}
              >
                Далее
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                variant="contained"
                disabled={loading || !validateStep(activeStep)}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? 'Создание...' : 'Создать заведение'}
              </Button>
            )}
          </Box>
        </Box>
      </Container>
    </Layout>
  );
};

export default CreateVenuePage;