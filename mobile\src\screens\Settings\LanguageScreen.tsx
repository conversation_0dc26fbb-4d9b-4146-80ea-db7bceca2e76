import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface LanguageScreenProps {}

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  isRTL?: boolean;
  isPopular?: boolean;
  completeness: number; // Percentage of translation completeness
}

const LanguageScreen: React.FC<LanguageScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<string>('ru');
  const [error, setError] = useState<string | null>(null);

  const languages: Language[] = [
    {
      code: 'ru',
      name: 'Русский',
      nativeName: 'Русский',
      flag: '🇷🇺',
      isPopular: true,
      completeness: 100
    },
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
      isPopular: true,
      completeness: 100
    },
    {
      code: 'es',
      name: 'Spanish',
      nativeName: 'Español',
      flag: '🇪🇸',
      isPopular: true,
      completeness: 95
    },
    {
      code: 'fr',
      name: 'French',
      nativeName: 'Français',
      flag: '🇫🇷',
      isPopular: true,
      completeness: 90
    },
    {
      code: 'de',
      name: 'German',
      nativeName: 'Deutsch',
      flag: '🇩🇪',
      isPopular: true,
      completeness: 88
    },
    {
      code: 'it',
      name: 'Italian',
      nativeName: 'Italiano',
      flag: '🇮🇹',
      completeness: 85
    },
    {
      code: 'pt',
      name: 'Portuguese',
      nativeName: 'Português',
      flag: '🇵🇹',
      completeness: 82
    },
    {
      code: 'zh',
      name: 'Chinese',
      nativeName: '中文',
      flag: '🇨🇳',
      completeness: 78
    },
    {
      code: 'ja',
      name: 'Japanese',
      nativeName: '日本語',
      flag: '🇯🇵',
      completeness: 75
    },
    {
      code: 'ko',
      name: 'Korean',
      nativeName: '한국어',
      flag: '🇰🇷',
      completeness: 70
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      flag: '🇸🇦',
      isRTL: true,
      completeness: 65
    },
    {
      code: 'hi',
      name: 'Hindi',
      nativeName: 'हिन्दी',
      flag: '🇮🇳',
      completeness: 60
    },
    {
      code: 'tr',
      name: 'Turkish',
      nativeName: 'Türkçe',
      flag: '🇹🇷',
      completeness: 55
    },
    {
      code: 'pl',
      name: 'Polish',
      nativeName: 'Polski',
      flag: '🇵🇱',
      completeness: 50
    },
    {
      code: 'nl',
      name: 'Dutch',
      nativeName: 'Nederlands',
      flag: '🇳🇱',
      completeness: 45
    }
  ];

  useEffect(() => {
    loadCurrentLanguage();
  }, []);

  const loadCurrentLanguage = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load from AsyncStorage
      const savedLanguage = await AsyncStorage.getItem('selectedLanguage');
      if (savedLanguage) {
        setCurrentLanguage(savedLanguage);
      }

      // TODO: Load from API/user preferences
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (err: any) {
      setError('Ошибка загрузки языковых настроек');
      console.error('Language loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleLanguageSelect = async (languageCode: string) => {
    if (languageCode === currentLanguage) return;

    try {
      setSaving(true);

      // Save to AsyncStorage
      await AsyncStorage.setItem('selectedLanguage', languageCode);

      // TODO: Apply language change to app
      // This would typically involve updating i18n configuration
      await new Promise(resolve => setTimeout(resolve, 1000));

      setCurrentLanguage(languageCode);

      const selectedLanguage = languages.find(lang => lang.code === languageCode);
      Alert.alert(
        'Язык изменен',
        `Язык интерфейса изменен на ${selectedLanguage?.nativeName}. Изменения применятся при следующем запуске приложения.`,
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );

    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить язык');
    } finally {
      setSaving(false);
    }
  };

  const getCompletenessColor = (completeness: number): string => {
    if (completeness >= 90) return '#4CAF50';
    if (completeness >= 70) return '#FF9800';
    return '#F44336';
  };

  const getCompletenessLabel = (completeness: number): string => {
    if (completeness >= 90) return 'Полный перевод';
    if (completeness >= 70) return 'Частичный перевод';
    return 'Базовый перевод';
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка языков...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadCurrentLanguage}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const popularLanguages = languages.filter(lang => lang.isPopular);
  const otherLanguages = languages.filter(lang => !lang.isPopular);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Язык</Text>
        <View style={styles.headerRight}>
          {saving && <ActivityIndicator size="small" color="#E91E63" />}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Language */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Текущий язык</Text>
          {(() => {
            const current = languages.find(lang => lang.code === currentLanguage);
            return current ? (
              <View style={styles.currentLanguageCard}>
                <Text style={styles.currentLanguageFlag}>{current.flag}</Text>
                <View style={styles.currentLanguageInfo}>
                  <Text style={styles.currentLanguageName}>{current.nativeName}</Text>
                  <Text style={styles.currentLanguageEnglish}>{current.name}</Text>
                </View>
                <View style={styles.currentLanguageBadge}>
                  <Text style={styles.currentLanguageBadgeText}>Активный</Text>
                </View>
              </View>
            ) : null;
          })()}
        </View>

        {/* Popular Languages */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Популярные языки</Text>
          <Text style={styles.sectionDescription}>
            Наиболее часто используемые языки с полным переводом
          </Text>

          {popularLanguages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageItem,
                currentLanguage === language.code && styles.languageItemSelected
              ]}
              onPress={() => handleLanguageSelect(language.code)}
              disabled={saving}
            >
              <Text style={styles.languageFlag}>{language.flag}</Text>

              <View style={styles.languageInfo}>
                <Text style={styles.languageName}>{language.nativeName}</Text>
                <Text style={styles.languageEnglish}>{language.name}</Text>
              </View>

              <View style={styles.languageStatus}>
                <View style={[
                  styles.completenessIndicator,
                  { backgroundColor: getCompletenessColor(language.completeness) }
                ]}>
                  <Text style={styles.completenessText}>{language.completeness}%</Text>
                </View>

                {currentLanguage === language.code && (
                  <Icon name="check-circle" size={24} color="#4CAF50" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Other Languages */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Другие языки</Text>
          <Text style={styles.sectionDescription}>
            Дополнительные языки с различной степенью перевода
          </Text>

          {otherLanguages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageItem,
                currentLanguage === language.code && styles.languageItemSelected,
                language.completeness < 70 && styles.languageItemIncomplete
              ]}
              onPress={() => handleLanguageSelect(language.code)}
              disabled={saving}
            >
              <Text style={styles.languageFlag}>{language.flag}</Text>

              <View style={styles.languageInfo}>
                <Text style={[
                  styles.languageName,
                  language.completeness < 70 && styles.languageNameIncomplete
                ]}>
                  {language.nativeName}
                </Text>
                <Text style={styles.languageEnglish}>{language.name}</Text>
                <Text style={styles.completenessLabel}>
                  {getCompletenessLabel(language.completeness)}
                </Text>
              </View>

              <View style={styles.languageStatus}>
                <View style={[
                  styles.completenessIndicator,
                  { backgroundColor: getCompletenessColor(language.completeness) }
                ]}>
                  <Text style={styles.completenessText}>{language.completeness}%</Text>
                </View>

                {currentLanguage === language.code && (
                  <Icon name="check-circle" size={24} color="#4CAF50" />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Help Section */}
        <View style={styles.section}>
          <View style={styles.helpCard}>
            <Icon name="translate" size={24} color="#E91E63" />
            <View style={styles.helpContent}>
              <Text style={styles.helpTitle}>Помочь с переводом</Text>
              <Text style={styles.helpDescription}>
                Хотите помочь перевести приложение на ваш язык? Свяжитесь с нами!
              </Text>
            </View>
            <TouchableOpacity style={styles.helpButton}>
              <Icon name="email" size={16} color="#E91E63" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
    alignItems: 'flex-end',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    lineHeight: 20,
  },
  currentLanguageCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E91E63' + '30',
  },
  currentLanguageFlag: {
    fontSize: 32,
    marginRight: 16,
  },
  currentLanguageInfo: {
    flex: 1,
  },
  currentLanguageName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  currentLanguageEnglish: {
    fontSize: 14,
    color: '#666666',
  },
  currentLanguageBadge: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  currentLanguageBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  languageItemSelected: {
    backgroundColor: '#f8fff8',
    borderRadius: 8,
    marginHorizontal: -4,
  },
  languageItemIncomplete: {
    opacity: 0.7,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: 16,
    width: 32,
    textAlign: 'center',
  },
  languageInfo: {
    flex: 1,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  languageNameIncomplete: {
    color: '#666666',
  },
  languageEnglish: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  completenessLabel: {
    fontSize: 12,
    color: '#999999',
  },
  languageStatus: {
    alignItems: 'flex-end',
    gap: 8,
  },
  completenessIndicator: {
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 40,
    alignItems: 'center',
  },
  completenessText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  helpCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  helpContent: {
    flex: 1,
    marginLeft: 12,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  helpDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  helpButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    padding: 8,
  },
  bottomPadding: {
    height: 40,
  },
});

export default LanguageScreen;