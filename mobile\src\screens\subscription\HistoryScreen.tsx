import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  RefreshControl,
  Alert
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface HistoryScreenProps {}

interface PaymentTransaction {
  id: string;
  type: 'subscription' | 'feature' | 'refund';
  status: 'completed' | 'pending' | 'failed' | 'refunded';
  amount: number;
  currency: string;
  description: string;
  subscriptionType?: string;
  date: string;
  paymentMethod: string;
  transactionId: string;
  receiptUrl?: string;
}

const HistoryScreen: React.FC<HistoryScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [filter, setFilter] = useState<'all' | 'completed' | 'pending' | 'failed'>('all');

  useFocusEffect(
    useCallback(() => {
      loadTransactionHistory();
    }, [])
  );

  const loadTransactionHistory = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTransactions: PaymentTransaction[] = [
        {
          id: '1',
          type: 'subscription',
          status: 'completed',
          amount: 990,
          currency: 'RUB',
          description: 'Premium подписка на 1 месяц',
          subscriptionType: 'Premium Monthly',
          date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: 'Visa •••• 1234',
          transactionId: 'TXN_001234567890',
          receiptUrl: 'https://example.com/receipt/1'
        },
        {
          id: '2',
          type: 'feature',
          status: 'completed',
          amount: 199,
          currency: 'RUB',
          description: 'Буст профиля',
          date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: 'Mastercard •••• 5678',
          transactionId: 'TXN_001234567891'
        },
        {
          id: '3',
          type: 'subscription',
          status: 'failed',
          amount: 990,
          currency: 'RUB',
          description: 'Premium подписка на 1 месяц',
          subscriptionType: 'Premium Monthly',
          date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: 'Visa •••• 1234',
          transactionId: 'TXN_001234567892'
        },
        {
          id: '4',
          type: 'subscription',
          status: 'completed',
          amount: 5990,
          currency: 'RUB',
          description: 'Premium подписка на 1 год',
          subscriptionType: 'Premium Yearly',
          date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: 'Visa •••• 1234',
          transactionId: 'TXN_001234567893',
          receiptUrl: 'https://example.com/receipt/4'
        },
        {
          id: '5',
          type: 'refund',
          status: 'completed',
          amount: -990,
          currency: 'RUB',
          description: 'Возврат за Premium подписку',
          date: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
          paymentMethod: 'Visa •••• 1234',
          transactionId: 'TXN_001234567894'
        }
      ];
      
      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error loading transaction history:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить историю платежей');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactionHistory();
    setRefreshing(false);
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'failed': return '#F44336';
      case 'refunded': return '#2196F3';
      default: return '#666666';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return 'Завершен';
      case 'pending': return 'В обработке';
      case 'failed': return 'Ошибка';
      case 'refunded': return 'Возврат';
      default: return status;
    }
  };

  const getTypeIcon = (type: string): string => {
    switch (type) {
      case 'subscription': return 'star';
      case 'feature': return 'flash-on';
      case 'refund': return 'undo';
      default: return 'payment';
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number, currency: string): string => {
    const sign = amount >= 0 ? '+' : '';
    return `${sign}${amount.toLocaleString('ru-RU')} ${currency === 'RUB' ? '₽' : currency}`;
  };

  const handleTransactionPress = (transaction: PaymentTransaction) => {
    if (transaction.receiptUrl) {
      Alert.alert(
        'Чек',
        'Открыть чек в браузере?',
        [
          { text: 'Отмена', style: 'cancel' },
          { text: 'Открыть', onPress: () => {
            // TODO: Open receipt URL
            console.log('Opening receipt:', transaction.receiptUrl);
          }}
        ]
      );
    } else {
      Alert.alert('Информация', `ID транзакции: ${transaction.transactionId}`);
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true;
    return transaction.status === filter;
  });

  const renderFilterButton = (filterType: typeof filter, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        filter === filterType && styles.filterButtonActive
      ]}
      onPress={() => setFilter(filterType)}
    >
      <Text style={[
        styles.filterButtonText,
        filter === filterType && styles.filterButtonTextActive
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderTransaction = (transaction: PaymentTransaction) => (
    <TouchableOpacity
      key={transaction.id}
      style={styles.transactionCard}
      onPress={() => handleTransactionPress(transaction)}
    >
      <View style={styles.transactionHeader}>
        <View style={styles.transactionIcon}>
          <Icon 
            name={getTypeIcon(transaction.type)} 
            size={24} 
            color="#E91E63" 
          />
        </View>
        
        <View style={styles.transactionInfo}>
          <Text style={styles.transactionDescription}>
            {transaction.description}
          </Text>
          <Text style={styles.transactionDate}>
            {formatDate(transaction.date)}
          </Text>
          <Text style={styles.transactionMethod}>
            {transaction.paymentMethod}
          </Text>
        </View>
        
        <View style={styles.transactionAmount}>
          <Text style={[
            styles.amountText,
            { color: transaction.amount >= 0 ? '#4CAF50' : '#F44336' }
          ]}>
            {formatAmount(transaction.amount, transaction.currency)}
          </Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(transaction.status) }
          ]}>
            <Text style={styles.statusText}>
              {getStatusText(transaction.status)}
            </Text>
          </View>
        </View>
      </View>
      
      {transaction.receiptUrl && (
        <View style={styles.receiptIndicator}>
          <Icon name="receipt" size={16} color="#666666" />
          <Text style={styles.receiptText}>Чек доступен</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка истории...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>История платежей</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Filters */}
      <View style={styles.filtersContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filtersContent}
        >
          {renderFilterButton('all', 'Все')}
          {renderFilterButton('completed', 'Завершенные')}
          {renderFilterButton('pending', 'В обработке')}
          {renderFilterButton('failed', 'Ошибки')}
        </ScrollView>
      </View>

      {/* Transactions List */}
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredTransactions.length > 0 ? (
          filteredTransactions.map(renderTransaction)
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="receipt-long" size={64} color="#cccccc" />
            <Text style={styles.emptyTitle}>Нет транзакций</Text>
            <Text style={styles.emptyDescription}>
              {filter === 'all' 
                ? 'У вас пока нет платежей'
                : `Нет транзакций со статусом "${getStatusText(filter)}"`
              }
            </Text>
          </View>
        )}
        
        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  filtersContainer: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  filtersContent: {
    paddingHorizontal: 20,
    gap: 12,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f5f5f5',
  },
  filterButtonActive: {
    backgroundColor: '#E91E63',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterButtonTextActive: {
    color: '#ffffff',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  transactionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  transactionMethod: {
    fontSize: 12,
    color: '#999999',
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  receiptIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  receiptText: {
    fontSize: 12,
    color: '#666666',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomPadding: {
    height: 40,
  },
});

export default HistoryScreen;
