import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface SubscriptionScreenProps {}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  features: string[];
  isPopular?: boolean;
  color: string;
  icon: string;
}

interface CurrentSubscription {
  planId: string;
  planName: string;
  status: 'active' | 'expired' | 'cancelled' | 'trial';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: {
    type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
    last4?: string;
    brand?: string;
  };
  nextBillingDate?: string;
  trialDaysLeft?: number;
}

interface UsageStats {
  likesUsed: number;
  likesLimit: number;
  superLikesUsed: number;
  superLikesLimit: number;
  boostsUsed: number;
  boostsLimit: number;
  messagesUnlimited: boolean;
  profileViewsUnlimited: boolean;
}

const SubscriptionScreen: React.FC<SubscriptionScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: 'basic',
      name: 'Базовый',
      description: 'Основные функции для знакомств',
      price: {
        monthly: 299,
        yearly: 2990,
        currency: 'RUB'
      },
      features: [
        '50 лайков в день',
        '5 супер-лайков в месяц',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы'
      ],
      color: '#4CAF50',
      icon: 'favorite'
    },
    {
      id: 'premium',
      name: 'Премиум',
      description: 'Расширенные возможности',
      price: {
        monthly: 599,
        yearly: 5990,
        currency: 'RUB'
      },
      features: [
        'Безлимитные лайки',
        '25 супер-лайков в месяц',
        '5 бустов в месяц',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы',
        'Приоритет в показе',
        'Чтение сообщений'
      ],
      isPopular: true,
      color: '#E91E63',
      icon: 'star'
    },
    {
      id: 'vip',
      name: 'VIP',
      description: 'Максимальные возможности',
      price: {
        monthly: 999,
        yearly: 9990,
        currency: 'RUB'
      },
      features: [
        'Безлимитные лайки',
        'Безлимитные супер-лайки',
        'Безлимитные бусты',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы',
        'Приоритет в показе',
        'Чтение сообщений',
        'Невидимый режим',
        'Расширенные фильтры',
        'Персональный менеджер'
      ],
      color: '#FF9800',
      icon: 'diamond'
    }
  ];

  useFocusEffect(
    useCallback(() => {
      loadSubscriptionData();
    }, [])
  );

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock current subscription
      const mockSubscription: CurrentSubscription = {
        planId: 'basic',
        planName: 'Базовый',
        status: 'active',
        startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        autoRenew: true,
        paymentMethod: {
          type: 'card',
          last4: '1234',
          brand: 'Visa'
        },
        nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
      };

      // Mock usage stats
      const mockUsageStats: UsageStats = {
        likesUsed: 32,
        likesLimit: 50,
        superLikesUsed: 3,
        superLikesLimit: 5,
        boostsUsed: 0,
        boostsLimit: 0,
        messagesUnlimited: false,
        profileViewsUnlimited: false
      };

      setCurrentSubscription(mockSubscription);
      setUsageStats(mockUsageStats);
      setSubscriptionPlans(plans);
    } catch (err: any) {
      setError('Ошибка загрузки данных подписки');
      console.error('Subscription loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    setShowUpgradeModal(true);
  };

  const handleSubscribe = async () => {
    if (!selectedPlan) return;

    try {
      setProcessing(true);

      // TODO: Implement actual payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      Alert.alert(
        'Подписка оформлена',
        `Вы успешно подписались на план "${selectedPlan.name}"`,
        [
          {
            text: 'OK',
            onPress: () => {
              setShowUpgradeModal(false);
              loadSubscriptionData();
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось оформить подписку');
    } finally {
      setProcessing(false);
    }
  };

  const handleCancelSubscription = () => {
    Alert.alert(
      'Отменить подписку',
      'Вы уверены, что хотите отменить подписку? Доступ к премиум-функциям будет ограничен.',
      [
        { text: 'Нет', style: 'cancel' },
        {
          text: 'Отменить подписку',
          style: 'destructive',
          onPress: async () => {
            try {
              setProcessing(true);
              // TODO: Implement actual cancellation
              await new Promise(resolve => setTimeout(resolve, 1000));

              Alert.alert('Подписка отменена', 'Ваша подписка будет отменена в конце текущего периода');
              loadSubscriptionData();
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось отменить подписку');
            } finally {
              setProcessing(false);
            }
          }
        }
      ]
    );
  };

  const handleToggleAutoRenew = async () => {
    if (!currentSubscription) return;

    try {
      setProcessing(true);
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setCurrentSubscription(prev => prev ? { ...prev, autoRenew: !prev.autoRenew } : null);

      Alert.alert(
        'Настройки обновлены',
        `Автопродление ${!currentSubscription.autoRenew ? 'включено' : 'отключено'}`
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить настройки');
    } finally {
      setProcessing(false);
    }
  };

  const handleManagePayment = () => {
    navigation.navigate('PaymentMethods');
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return '#4CAF50';
      case 'trial': return '#2196F3';
      case 'expired': return '#F44336';
      case 'cancelled': return '#FF9800';
      default: return '#666666';
    }
  };

  const getStatusLabel = (status: string): string => {
    switch (status) {
      case 'active': return 'Активна';
      case 'trial': return 'Пробный период';
      case 'expired': return 'Истекла';
      case 'cancelled': return 'Отменена';
      default: return status;
    }
  };

  const calculateYearlySavings = (plan: SubscriptionPlan): number => {
    const monthlyTotal = plan.price.monthly * 12;
    const yearlyPrice = plan.price.yearly;
    return Math.round(((monthlyTotal - yearlyPrice) / monthlyTotal) * 100);
  };

  const getUsagePercentage = (used: number, limit: number): number => {
    if (limit === 0) return 0;
    return Math.min((used / limit) * 100, 100);
  };

  const renderCurrentSubscription = () => {
    if (!currentSubscription) return null;

    return (
      <View style={styles.currentSubscriptionCard}>
        <View style={styles.subscriptionHeader}>
          <View style={styles.subscriptionInfo}>
            <Text style={styles.subscriptionTitle}>{currentSubscription.planName}</Text>
            <View style={styles.statusContainer}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(currentSubscription.status) }]} />
              <Text style={styles.statusText}>{getStatusLabel(currentSubscription.status)}</Text>
            </View>
          </View>
          <Icon name="card-membership" size={32} color="#E91E63" />
        </View>

        <View style={styles.subscriptionDetails}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Действует до:</Text>
            <Text style={styles.detailValue}>{formatDate(currentSubscription.endDate)}</Text>
          </View>

          {currentSubscription.nextBillingDate && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Следующее списание:</Text>
              <Text style={styles.detailValue}>{formatDate(currentSubscription.nextBillingDate)}</Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Способ оплаты:</Text>
            <Text style={styles.detailValue}>
              {currentSubscription.paymentMethod.brand} •••• {currentSubscription.paymentMethod.last4}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Автопродление:</Text>
            <TouchableOpacity
              style={styles.autoRenewToggle}
              onPress={handleToggleAutoRenew}
              disabled={processing}
            >
              <Text style={[
                styles.autoRenewText,
                { color: currentSubscription.autoRenew ? '#4CAF50' : '#F44336' }
              ]}>
                {currentSubscription.autoRenew ? 'Включено' : 'Отключено'}
              </Text>
              <Icon
                name={currentSubscription.autoRenew ? 'toggle-on' : 'toggle-off'}
                size={24}
                color={currentSubscription.autoRenew ? '#4CAF50' : '#F44336'}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.subscriptionActions}>
          <TouchableOpacity
            style={styles.manageButton}
            onPress={handleManagePayment}
          >
            <Icon name="payment" size={16} color="#E91E63" />
            <Text style={styles.manageButtonText}>Способы оплаты</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.cancelButton}
            onPress={handleCancelSubscription}
            disabled={processing}
          >
            <Icon name="cancel" size={16} color="#F44336" />
            <Text style={styles.cancelButtonText}>Отменить</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderUsageStats = () => {
    if (!usageStats) return null;

    return (
      <View style={styles.usageStatsCard}>
        <Text style={styles.sectionTitle}>Использование</Text>

        <View style={styles.usageItem}>
          <View style={styles.usageHeader}>
            <Text style={styles.usageLabel}>Лайки</Text>
            <Text style={styles.usageCount}>
              {usageStats.likesUsed} / {usageStats.likesLimit === 0 ? '∞' : usageStats.likesLimit}
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${getUsagePercentage(usageStats.likesUsed, usageStats.likesLimit)}%`,
                  backgroundColor: usageStats.likesLimit === 0 ? '#4CAF50' : '#E91E63'
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.usageItem}>
          <View style={styles.usageHeader}>
            <Text style={styles.usageLabel}>Супер-лайки</Text>
            <Text style={styles.usageCount}>
              {usageStats.superLikesUsed} / {usageStats.superLikesLimit === 0 ? '∞' : usageStats.superLikesLimit}
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${getUsagePercentage(usageStats.superLikesUsed, usageStats.superLikesLimit)}%`,
                  backgroundColor: usageStats.superLikesLimit === 0 ? '#4CAF50' : '#2196F3'
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.usageItem}>
          <View style={styles.usageHeader}>
            <Text style={styles.usageLabel}>Бусты</Text>
            <Text style={styles.usageCount}>
              {usageStats.boostsUsed} / {usageStats.boostsLimit === 0 ? '∞' : usageStats.boostsLimit}
            </Text>
          </View>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${getUsagePercentage(usageStats.boostsUsed, usageStats.boostsLimit)}%`,
                  backgroundColor: usageStats.boostsLimit === 0 ? '#4CAF50' : '#FF9800'
                }
              ]}
            />
          </View>
        </View>

        <View style={styles.unlimitedFeatures}>
          <View style={styles.unlimitedItem}>
            <Icon
              name={usageStats.messagesUnlimited ? 'check-circle' : 'cancel'}
              size={20}
              color={usageStats.messagesUnlimited ? '#4CAF50' : '#F44336'}
            />
            <Text style={styles.unlimitedText}>Безлимитные сообщения</Text>
          </View>

          <View style={styles.unlimitedItem}>
            <Icon
              name={usageStats.profileViewsUnlimited ? 'check-circle' : 'cancel'}
              size={20}
              color={usageStats.profileViewsUnlimited ? '#4CAF50' : '#F44336'}
            />
            <Text style={styles.unlimitedText}>Просмотр профилей</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderSubscriptionPlans = () => {
    return (
      <View style={styles.plansSection}>
        <Text style={styles.sectionTitle}>Планы подписки</Text>

        <View style={styles.billingToggle}>
          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'monthly' && styles.billingOptionActive
            ]}
            onPress={() => setBillingPeriod('monthly')}
          >
            <Text style={[
              styles.billingOptionText,
              billingPeriod === 'monthly' && styles.billingOptionTextActive
            ]}>
              Месяц
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.billingOption,
              billingPeriod === 'yearly' && styles.billingOptionActive
            ]}
            onPress={() => setBillingPeriod('yearly')}
          >
            <Text style={[
              styles.billingOptionText,
              billingPeriod === 'yearly' && styles.billingOptionTextActive
            ]}>
              Год
            </Text>
            <View style={styles.savingsBadge}>
              <Text style={styles.savingsText}>-17%</Text>
            </View>
          </TouchableOpacity>
        </View>

        {subscriptionPlans.map((plan) => (
          <TouchableOpacity
            key={plan.id}
            style={[
              styles.planCard,
              plan.isPopular && styles.popularPlanCard,
              currentSubscription?.planId === plan.id && styles.currentPlanCard
            ]}
            onPress={() => handleUpgrade(plan)}
            disabled={currentSubscription?.planId === plan.id}
          >
            {plan.isPopular && (
              <View style={styles.popularBadge}>
                <Text style={styles.popularBadgeText}>ПОПУЛЯРНЫЙ</Text>
              </View>
            )}

            <View style={styles.planHeader}>
              <View style={[styles.planIcon, { backgroundColor: plan.color + '20' }]}>
                <Icon name={plan.icon} size={24} color={plan.color} />
              </View>
              <View style={styles.planInfo}>
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planDescription}>{plan.description}</Text>
              </View>
            </View>

            <View style={styles.planPricing}>
              <Text style={styles.planPrice}>
                {billingPeriod === 'monthly' ? plan.price.monthly : Math.round(plan.price.yearly / 12)} ₽
              </Text>
              <Text style={styles.planPeriod}>
                {billingPeriod === 'monthly' ? '/месяц' : '/месяц при годовой оплате'}
              </Text>
              {billingPeriod === 'yearly' && (
                <Text style={styles.yearlyTotal}>
                  {plan.price.yearly} ₽/год • Экономия {calculateYearlySavings(plan)}%
                </Text>
              )}
            </View>

            <View style={styles.planFeatures}>
              {plan.features.map((feature, index) => (
                <View key={index} style={styles.featureItem}>
                  <Icon name="check" size={16} color={plan.color} />
                  <Text style={styles.featureText}>{feature}</Text>
                </View>
              ))}
            </View>

            <View style={styles.planAction}>
              {currentSubscription?.planId === plan.id ? (
                <View style={styles.currentPlanButton}>
                  <Text style={styles.currentPlanButtonText}>Текущий план</Text>
                </View>
              ) : (
                <View style={[styles.upgradeButton, { backgroundColor: plan.color }]}>
                  <Text style={styles.upgradeButtonText}>
                    {currentSubscription ? 'Перейти' : 'Выбрать'}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка подписки...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadSubscriptionData}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Подписка</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Subscription */}
        {currentSubscription && renderCurrentSubscription()}

        {/* Usage Stats */}
        {usageStats && renderUsageStats()}

        {/* Subscription Plans */}
        {renderSubscriptionPlans()}

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Upgrade Modal */}
      <Modal
        visible={showUpgradeModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowUpgradeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Подтверждение подписки</Text>
              <TouchableOpacity onPress={() => setShowUpgradeModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            {selectedPlan && (
              <View style={styles.modalBody}>
                <View style={styles.selectedPlanInfo}>
                  <View style={[styles.selectedPlanIcon, { backgroundColor: selectedPlan.color + '20' }]}>
                    <Icon name={selectedPlan.icon} size={32} color={selectedPlan.color} />
                  </View>
                  <Text style={styles.selectedPlanName}>{selectedPlan.name}</Text>
                  <Text style={styles.selectedPlanPrice}>
                    {billingPeriod === 'monthly'
                      ? `${selectedPlan.price.monthly} ₽/месяц`
                      : `${selectedPlan.price.yearly} ₽/год`
                    }
                  </Text>
                </View>

                <View style={styles.modalFeatures}>
                  <Text style={styles.modalFeaturesTitle}>Что входит:</Text>
                  {selectedPlan.features.slice(0, 5).map((feature, index) => (
                    <View key={index} style={styles.modalFeatureItem}>
                      <Icon name="check" size={16} color={selectedPlan.color} />
                      <Text style={styles.modalFeatureText}>{feature}</Text>
                    </View>
                  ))}
                </View>

                <View style={styles.modalActions}>
                  <TouchableOpacity
                    style={styles.modalCancelButton}
                    onPress={() => setShowUpgradeModal(false)}
                    disabled={processing}
                  >
                    <Text style={styles.modalCancelButtonText}>Отмена</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.modalSubscribeButton, { backgroundColor: selectedPlan.color }]}
                    onPress={handleSubscribe}
                    disabled={processing}
                  >
                    {processing ? (
                      <ActivityIndicator size="small" color="#ffffff" />
                    ) : (
                      <Text style={styles.modalSubscribeButtonText}>Подписаться</Text>
                    )}
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  currentSubscriptionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    margin: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#E91E63' + '30',
  },
  subscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  subscriptionInfo: {
    flex: 1,
  },
  subscriptionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: '#666666',
  },
  subscriptionDetails: {
    gap: 12,
    marginBottom: 20,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 14,
    color: '#666666',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
  },
  autoRenewToggle: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  autoRenewText: {
    fontSize: 14,
    fontWeight: '500',
  },
  subscriptionActions: {
    flexDirection: 'row',
    gap: 12,
  },
  manageButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  manageButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
  cancelButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F44336' + '10',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#F44336',
  },
  usageStatsCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  usageItem: {
    marginBottom: 16,
  },
  usageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  usageLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
  },
  usageCount: {
    fontSize: 14,
    color: '#666666',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  unlimitedFeatures: {
    gap: 12,
    marginTop: 8,
  },
  unlimitedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  unlimitedText: {
    fontSize: 14,
    color: '#333333',
  },
  plansSection: {
    paddingHorizontal: 20,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 4,
    marginBottom: 20,
  },
  billingOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    position: 'relative',
  },
  billingOptionActive: {
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  billingOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  billingOptionTextActive: {
    color: '#333333',
  },
  savingsBadge: {
    position: 'absolute',
    top: -8,
    right: 8,
    backgroundColor: '#4CAF50',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  savingsText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  planCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    position: 'relative',
  },
  popularPlanCard: {
    borderColor: '#E91E63',
    borderWidth: 2,
  },
  currentPlanCard: {
    backgroundColor: '#f8fff8',
    borderColor: '#4CAF50',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  popularBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  planIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  planDescription: {
    fontSize: 14,
    color: '#666666',
  },
  planPricing: {
    marginBottom: 16,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
  },
  planPeriod: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  yearlyTotal: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 4,
  },
  planFeatures: {
    gap: 8,
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  planAction: {
    marginTop: 'auto',
  },
  upgradeButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  currentPlanButton: {
    backgroundColor: '#4CAF50' + '20',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  currentPlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  modalBody: {
    padding: 20,
  },
  selectedPlanInfo: {
    alignItems: 'center',
    marginBottom: 24,
  },
  selectedPlanIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  selectedPlanName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  selectedPlanPrice: {
    fontSize: 18,
    fontWeight: '600',
    color: '#E91E63',
  },
  modalFeatures: {
    marginBottom: 24,
  },
  modalFeaturesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  modalFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  modalFeatureText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
  modalSubscribeButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalSubscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default SubscriptionScreen;