import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface SubscriptionScreenProps {}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  features: string[];
  isPopular?: boolean;
  color: string;
  icon: string;
}

interface CurrentSubscription {
  planId: string;
  planName: string;
  status: 'active' | 'expired' | 'cancelled' | 'trial';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: {
    type: 'card' | 'paypal' | 'apple_pay' | 'google_pay';
    last4?: string;
    brand?: string;
  };
  nextBillingDate?: string;
  trialDaysLeft?: number;
}

interface UsageStats {
  likesUsed: number;
  likesLimit: number;
  superLikesUsed: number;
  superLikesLimit: number;
  boostsUsed: number;
  boostsLimit: number;
  messagesUnlimited: boolean;
  profileViewsUnlimited: boolean;
}

const SubscriptionScreen: React.FC<SubscriptionScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);
  const [usageStats, setUsageStats] = useState<UsageStats | null>(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const plans: SubscriptionPlan[] = [
    {
      id: 'basic',
      name: 'Базовый',
      description: 'Основные функции для знакомств',
      price: {
        monthly: 299,
        yearly: 2990,
        currency: 'RUB'
      },
      features: [
        '50 лайков в день',
        '5 супер-лайков в месяц',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы'
      ],
      color: '#4CAF50',
      icon: 'favorite'
    },
    {
      id: 'premium',
      name: 'Премиум',
      description: 'Расширенные возможности',
      price: {
        monthly: 599,
        yearly: 5990,
        currency: 'RUB'
      },
      features: [
        'Безлимитные лайки',
        '25 супер-лайков в месяц',
        '5 бустов в месяц',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы',
        'Приоритет в показе',
        'Чтение сообщений'
      ],
      isPopular: true,
      color: '#E91E63',
      icon: 'star'
    },
    {
      id: 'vip',
      name: 'VIP',
      description: 'Максимальные возможности',
      price: {
        monthly: 999,
        yearly: 9990,
        currency: 'RUB'
      },
      features: [
        'Безлимитные лайки',
        'Безлимитные супер-лайки',
        'Безлимитные бусты',
        'Просмотр кто лайкнул',
        'Отмена последнего свайпа',
        'Без рекламы',
        'Приоритет в показе',
        'Чтение сообщений',
        'Невидимый режим',
        'Расширенные фильтры',
        'Персональный менеджер'
      ],
      color: '#FF9800',
      icon: 'diamond'
    }
  ];

  useFocusEffect(
    useCallback(() => {
      loadSubscriptionData();
    }, [])
  );

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock current subscription
      const mockSubscription: CurrentSubscription = {
        planId: 'basic',
        planName: 'Базовый',
        status: 'active',
        startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
        endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
        autoRenew: true,
        paymentMethod: {
          type: 'card',
          last4: '1234',
          brand: 'Visa'
        },
        nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
      };

      // Mock usage stats
      const mockUsageStats: UsageStats = {
        likesUsed: 32,
        likesLimit: 50,
        superLikesUsed: 3,
        superLikesLimit: 5,
        boostsUsed: 0,
        boostsLimit: 0,
        messagesUnlimited: false,
        profileViewsUnlimited: false
      };

      setCurrentSubscription(mockSubscription);
      setUsageStats(mockUsageStats);
      setSubscriptionPlans(plans);
    } catch (err: any) {
      setError('Ошибка загрузки данных подписки');
      console.error('Subscription loading error:', err);
    } finally {
      setLoading(false);
    }
  };