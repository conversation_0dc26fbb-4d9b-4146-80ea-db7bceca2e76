import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  TextInput
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface BlockedUsersScreenProps {}

interface BlockedUser {
  id: string;
  name: string;
  age: number;
  avatar: string;
  blockedAt: string;
  reason?: string;
}

const BlockedUsersScreen: React.FC<BlockedUsersScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadBlockedUsers();
  }, []);

  const loadBlockedUsers = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockData: BlockedUser[] = [
        {
          id: '1',
          name: 'Анна',
          age: 25,
          avatar: 'https://example.com/avatar1.jpg',
          blockedAt: '2024-01-10T10:30:00Z',
          reason: 'Спам'
        },
        {
          id: '2',
          name: 'Михаил',
          age: 30,
          avatar: 'https://example.com/avatar2.jpg',
          blockedAt: '2024-01-08T15:45:00Z',
          reason: 'Неподобающее поведение'
        },
        {
          id: '3',
          name: 'Елена',
          age: 28,
          avatar: 'https://example.com/avatar3.jpg',
          blockedAt: '2024-01-05T09:15:00Z'
        }
      ];

      setBlockedUsers(mockData);
    } catch (error) {
      console.error('Error loading blocked users:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить список заблокированных пользователей');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadBlockedUsers();
    setRefreshing(false);
  };

  const handleUnblockUser = (user: BlockedUser) => {
    Alert.alert(
      'Разблокировать пользователя',
      `Вы уверены, что хотите разблокировать ${user.name}? Этот пользователь снова сможет видеть ваш профиль и писать вам сообщения.`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Разблокировать',
          onPress: () => unblockUser(user.id)
        }
      ]
    );
  };

  const unblockUser = async (userId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setBlockedUsers(prev => prev.filter(user => user.id !== userId));
      Alert.alert('Успешно', 'Пользователь разблокирован');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось разблокировать пользователя');
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const filteredUsers = blockedUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderBlockedUser = ({ item }: { item: BlockedUser }) => (
    <View style={styles.userCard}>
      <Image
        source={{ uri: item.avatar }}
        style={styles.avatar}
        defaultSource={require('../../assets/default-avatar.png')}
      />
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.name}, {item.age}</Text>
        <Text style={styles.blockedDate}>
          Заблокирован {formatDate(item.blockedAt)}
        </Text>
        {item.reason && (
          <Text style={styles.blockReason}>Причина: {item.reason}</Text>
        )}
      </View>
      <TouchableOpacity
        style={styles.unblockButton}
        onPress={() => handleUnblockUser(item)}
      >
        <Icon name="block" size={20} color="#E91E63" />
        <Text style={styles.unblockButtonText}>Разблокировать</Text>
      </TouchableOpacity>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="block" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Нет заблокированных пользователей</Text>
      <Text style={styles.emptyDescription}>
        Заблокированные пользователи будут отображаться здесь
      </Text>
    </View>
  );

  const renderSearchEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="search-off" size={64} color="#cccccc" />
      <Text style={styles.emptyTitle}>Пользователи не найдены</Text>
      <Text style={styles.emptyDescription}>
        Попробуйте изменить поисковый запрос
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Заблокированные</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Search */}
      {blockedUsers.length > 0 && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Icon name="search" size={20} color="#666666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Поиск по имени..."
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Icon name="clear" size={20} color="#666666" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Users List */}
      <FlatList
        data={filteredUsers}
        renderItem={renderBlockedUser}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshing={refreshing}
        onRefresh={handleRefresh}
        ListEmptyComponent={
          searchQuery.length > 0 ? renderSearchEmptyState() :
          blockedUsers.length === 0 ? renderEmptyState() : null
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Info */}
      {blockedUsers.length > 0 && (
        <View style={styles.infoContainer}>
          <Icon name="info" size={20} color="#666666" />
          <Text style={styles.infoText}>
            Заблокированные пользователи не могут видеть ваш профиль и писать вам сообщения
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 12,
    marginLeft: 12,
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e0e0e0',
  },
  userInfo: {
    flex: 1,
    marginLeft: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  blockedDate: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  blockReason: {
    fontSize: 12,
    color: '#ff6666',
    fontStyle: 'italic',
  },
  unblockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E91E63',
  },
  unblockButtonText: {
    fontSize: 12,
    color: '#E91E63',
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f0f8ff',
    borderRadius: 12,
    padding: 16,
    margin: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
});

export default BlockedUsersScreen;