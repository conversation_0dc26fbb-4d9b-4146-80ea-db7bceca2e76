import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Linking,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface HelpCenterScreenProps {}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  isExpanded?: boolean;
}

interface HelpCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  itemCount: number;
}

const HelpCenterScreen: React.FC<HelpCenterScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [faqItems, setFaqItems] = useState<FAQItem[]>([]);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const helpCategories: HelpCategory[] = [
    {
      id: 'all',
      name: 'Все вопросы',
      icon: 'help',
      description: 'Все часто задаваемые вопросы',
      itemCount: 0
    },
    {
      id: 'account',
      name: 'Аккаунт',
      icon: 'person',
      description: 'Регистрация, профиль, настройки',
      itemCount: 5
    },
    {
      id: 'matching',
      name: 'Знакомства',
      icon: 'favorite',
      description: 'Лайки, совпадения, алгоритм',
      itemCount: 8
    },
    {
      id: 'messaging',
      name: 'Сообщения',
      icon: 'message',
      description: 'Чаты, отправка сообщений',
      itemCount: 4
    },
    {
      id: 'premium',
      name: 'Premium',
      icon: 'star',
      description: 'Подписки, платежи, функции',
      itemCount: 6
    },
    {
      id: 'safety',
      name: 'Безопасность',
      icon: 'security',
      description: 'Блокировка, жалобы, конфиденциальность',
      itemCount: 7
    },
    {
      id: 'technical',
      name: 'Технические',
      icon: 'settings',
      description: 'Ошибки, проблемы с приложением',
      itemCount: 5
    }
  ];

  const mockFAQItems: FAQItem[] = [
    {
      id: '1',
      category: 'account',
      question: 'Как создать аккаунт?',
      answer: 'Для создания аккаунта нажмите "Регистрация" на главном экране, введите номер телефона или email, подтвердите код и заполните профиль.'
    },
    {
      id: '2',
      category: 'account',
      question: 'Как изменить фотографии в профиле?',
      answer: 'Перейдите в раздел "Профиль", нажмите на фотографию, которую хотите изменить, и выберите новое изображение из галереи или сделайте новое фото.'
    },
    {
      id: '3',
      category: 'matching',
      question: 'Как работает алгоритм подбора?',
      answer: 'Алгоритм учитывает ваши предпочтения, возраст, местоположение, интересы и активность в приложении для показа наиболее подходящих анкет.'
    },
    {
      id: '4',
      category: 'matching',
      question: 'Что такое супер-лайк?',
      answer: 'Супер-лайк - это способ показать особый интерес к человеку. Получатель увидит, что вы поставили именно супер-лайк, что увеличивает шансы на взаимность.'
    },
    {
      id: '5',
      category: 'messaging',
      question: 'Почему я не могу отправить сообщение?',
      answer: 'Отправлять сообщения можно только после взаимного лайка (совпадения). Убедитесь, что у вас есть совпадение с этим человеком.'
    },
    {
      id: '6',
      category: 'premium',
      question: 'Что включает Premium подписка?',
      answer: 'Premium включает: безлимитные лайки, супер-лайки, возможность видеть кто лайкнул вас, буст профиля, отмену действий и другие функции.'
    },
    {
      id: '7',
      category: 'safety',
      question: 'Как заблокировать пользователя?',
      answer: 'Откройте профиль пользователя, нажмите на три точки в правом верхнем углу и выберите "Заблокировать". Заблокированный пользователь не сможет вас найти.'
    },
    {
      id: '8',
      category: 'technical',
      question: 'Приложение работает медленно, что делать?',
      answer: 'Попробуйте перезапустить приложение, проверить интернет-соединение, обновить приложение до последней версии или перезагрузить устройство.'
    }
  ];

  useFocusEffect(
    useCallback(() => {
      loadFAQItems();
    }, [])
  );

  const loadFAQItems = async () => {
    try {
      setLoading(true);

      // TODO: Load from API
      await new Promise(resolve => setTimeout(resolve, 500));

      setFaqItems(mockFAQItems);
    } catch (error) {
      console.error('Error loading FAQ items:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить вопросы');
    } finally {
      setLoading(false);
    }
  };

  const toggleFAQItem = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Связаться с поддержкой',
      'Выберите способ связи:',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Email', onPress: () => navigation.navigate('ContactSupport', { method: 'email' }) },
        { text: 'Чат', onPress: () => navigation.navigate('ContactSupport', { method: 'chat' }) }
      ]
    );
  };

  const handleCallSupport = () => {
    const phoneNumber = '+7 (800) 555-35-35';
    Linking.openURL(`tel:${phoneNumber}`);
  };

  const handleEmailSupport = () => {
    const email = '<EMAIL>';
    Linking.openURL(`mailto:${email}`);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка справки...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Центр помощи</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search */}
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Icon name="search" size={20} color="#666666" />
            <TextInput
              style={styles.searchInput}
              placeholder="Поиск по справке..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
          <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
            <Icon name="search" size={20} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Быстрые действия</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickAction} onPress={handleContactSupport}>
              <Icon name="chat" size={24} color="#E91E63" />
              <Text style={styles.quickActionText}>Написать в поддержку</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction} onPress={handleCallSupport}>
              <Icon name="phone" size={24} color="#E91E63" />
              <Text style={styles.quickActionText}>Позвонить</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickAction} onPress={handleEmailSupport}>
              <Icon name="email" size={24} color="#E91E63" />
              <Text style={styles.quickActionText}>Email</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Contact Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Контактная информация</Text>
          <View style={styles.contactInfo}>
            <View style={styles.contactItem}>
              <Icon name="schedule" size={20} color="#666666" />
              <Text style={styles.contactText}>Поддержка работает 24/7</Text>
            </View>
            <View style={styles.contactItem}>
              <Icon name="phone" size={20} color="#666666" />
              <Text style={styles.contactText}>+7 (800) 555-35-35</Text>
            </View>
            <View style={styles.contactItem}>
              <Icon name="email" size={20} color="#666666" />
              <Text style={styles.contactText}><EMAIL></Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 20,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingHorizontal: 16,
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    paddingVertical: 16,
    marginLeft: 12,
  },
  searchButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    padding: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  quickAction: {
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 20,
    flex: 1,
    marginHorizontal: 4,
  },
  quickActionText: {
    fontSize: 12,
    color: '#333333',
    marginTop: 12,
    textAlign: 'center',
  },
  contactInfo: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 20,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 16,
  },
});

export default HelpCenterScreen;