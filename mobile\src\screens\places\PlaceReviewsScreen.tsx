import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  TextInput,
  Alert,
  RefreshControl
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface PlaceReviewsScreenProps {}

interface RouteParams {
  placeId: string;
  placeName: string;
}

interface Review {
  id: string;
  user: {
    id: string;
    name: string;
    avatar?: string;
  };
  rating: number;
  comment: string;
  photos?: string[];
  createdAt: string;
  isHelpful: boolean;
  helpfulCount: number;
  isOwn: boolean;
}

interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

const PlaceReviewsScreen: React.FC<PlaceReviewsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { placeId, placeName } = (route.params as RouteParams) || {};

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewStats, setReviewStats] = useState<ReviewStats | null>(null);
  const [filter, setFilter] = useState<'all' | 'photos' | 'recent'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'rating' | 'helpful'>('recent');

  useFocusEffect(
    useCallback(() => {
      loadReviews();
    }, [placeId, filter, sortBy])
  );

  const loadReviews = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockReviewStats: ReviewStats = {
        averageRating: 4.3,
        totalReviews: 127,
        ratingDistribution: {
          5: 65,
          4: 32,
          3: 18,
          2: 8,
          1: 4
        }
      };

      const mockReviews: Review[] = [
        {
          id: '1',
          user: {
            id: 'user1',
            name: 'Анна К.',
            avatar: 'https://example.com/avatar1.jpg'
          },
          rating: 5,
          comment: 'Отличное место! Очень уютная атмосфера, вкусная еда и приветливый персонал. Обязательно вернемся сюда еще раз.',
          photos: ['https://example.com/review1.jpg', 'https://example.com/review2.jpg'],
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          isHelpful: false,
          helpfulCount: 12,
          isOwn: false
        },
        {
          id: '2',
          user: {
            id: 'user2',
            name: 'Михаил С.'
          },
          rating: 4,
          comment: 'Хорошее кафе, но немного дороговато. Кофе вкусный, интерьер современный.',
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          isHelpful: true,
          helpfulCount: 8,
          isOwn: false
        },
        {
          id: '3',
          user: {
            id: 'current_user',
            name: 'Вы'
          },
          rating: 5,
          comment: 'Мой любимый ресторан в городе! Всегда свежие продукты и отличное обслуживание.',
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          isHelpful: false,
          helpfulCount: 5,
          isOwn: true
        }
      ];
      
      setReviewStats(mockReviewStats);
      setReviews(mockReviews);
    } catch (error) {
      console.error('Error loading reviews:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить отзывы');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReviews();
    setRefreshing(false);
  };

  const handleWriteReview = () => {
    navigation.navigate('WriteReview', { placeId, placeName });
  };

  const handleHelpfulPress = async (reviewId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setReviews(prev => prev.map(review => 
        review.id === reviewId 
          ? { 
              ...review, 
              isHelpful: !review.isHelpful,
              helpfulCount: review.isHelpful ? review.helpfulCount - 1 : review.helpfulCount + 1
            }
          : review
      ));
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отметить отзыв как полезный');
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 30) {
      return `${diffInDays} дн. назад`;
    } else {
      return date.toLocaleDateString('ru-RU');
    }
  };

  const renderStars = (rating: number, size: number = 16) => {
    return (
      <View style={styles.starsContainer}>
        {[1, 2, 3, 4, 5].map(star => (
          <Icon
            key={star}
            name={star <= rating ? 'star' : 'star-border'}
            size={size}
            color={star <= rating ? '#FFD700' : '#cccccc'}
          />
        ))}
      </View>
    );
  };

  const renderRatingDistribution = () => {
    if (!reviewStats) return null;

    return (
      <View style={styles.ratingDistribution}>
        {[5, 4, 3, 2, 1].map(rating => {
          const count = reviewStats.ratingDistribution[rating as keyof typeof reviewStats.ratingDistribution];
          const percentage = (count / reviewStats.totalReviews) * 100;
          
          return (
            <View key={rating} style={styles.ratingRow}>
              <Text style={styles.ratingNumber}>{rating}</Text>
              <Icon name="star" size={14} color="#FFD700" />
              <View style={styles.ratingBar}>
                <View 
                  style={[styles.ratingBarFill, { width: `${percentage}%` }]} 
                />
              </View>
              <Text style={styles.ratingCount}>{count}</Text>
            </View>
          );
        })}
      </View>
    );
  };

  const renderReview = (review: Review) => (
    <View key={review.id} style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <View style={styles.userInfo}>
          {review.user.avatar ? (
            <Image source={{ uri: review.user.avatar }} style={styles.userAvatar} />
          ) : (
            <View style={styles.userAvatarPlaceholder}>
              <Icon name="person" size={20} color="#666666" />
            </View>
          )}
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{review.user.name}</Text>
            <Text style={styles.reviewDate}>{formatDate(review.createdAt)}</Text>
          </View>
        </View>
        {renderStars(review.rating)}
      </View>
      
      <Text style={styles.reviewComment}>{review.comment}</Text>
      
      {review.photos && review.photos.length > 0 && (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.reviewPhotos}
        >
          {review.photos.map((photo, index) => (
            <Image key={index} source={{ uri: photo }} style={styles.reviewPhoto} />
          ))}
        </ScrollView>
      )}
      
      <View style={styles.reviewActions}>
        <TouchableOpacity
          style={[
            styles.helpfulButton,
            review.isHelpful && styles.helpfulButtonActive
          ]}
          onPress={() => handleHelpfulPress(review.id)}
        >
          <Icon 
            name={review.isHelpful ? 'thumb-up' : 'thumb-up-off-alt'} 
            size={16} 
            color={review.isHelpful ? '#E91E63' : '#666666'} 
          />
          <Text style={[
            styles.helpfulText,
            review.isHelpful && styles.helpfulTextActive
          ]}>
            Полезно ({review.helpfulCount})
          </Text>
        </TouchableOpacity>
        
        {review.isOwn && (
          <TouchableOpacity style={styles.editButton}>
            <Icon name="edit" size={16} color="#666666" />
            <Text style={styles.editText}>Редактировать</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const filteredReviews = reviews.filter(review => {
    switch (filter) {
      case 'photos':
        return review.photos && review.photos.length > 0;
      case 'recent':
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return new Date(review.createdAt) > dayAgo;
      default:
        return true;
    }
  });

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка отзывов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>Отзывы</Text>
          <Text style={styles.headerSubtitle}>{placeName}</Text>
        </View>
        <TouchableOpacity 
          style={styles.writeReviewButton}
          onPress={handleWriteReview}
        >
          <Icon name="edit" size={24} color="#E91E63" />
        </TouchableOpacity>
      </View>

      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Rating Summary */}
        {reviewStats && (
          <View style={styles.ratingSummary}>
            <View style={styles.averageRating}>
              <Text style={styles.averageRatingNumber}>
                {reviewStats.averageRating.toFixed(1)}
              </Text>
              {renderStars(Math.round(reviewStats.averageRating), 20)}
              <Text style={styles.totalReviews}>
                {reviewStats.totalReviews} отзывов
              </Text>
            </View>
            {renderRatingDistribution()}
          </View>
        )}

        {/* Filters */}
        <View style={styles.filtersContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
              onPress={() => setFilter('all')}
            >
              <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>
                Все
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterButton, filter === 'photos' && styles.filterButtonActive]}
              onPress={() => setFilter('photos')}
            >
              <Text style={[styles.filterText, filter === 'photos' && styles.filterTextActive]}>
                С фото
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterButton, filter === 'recent' && styles.filterButtonActive]}
              onPress={() => setFilter('recent')}
            >
              <Text style={[styles.filterText, filter === 'recent' && styles.filterTextActive]}>
                Недавние
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Reviews */}
        <View style={styles.reviewsContainer}>
          {filteredReviews.length > 0 ? (
            filteredReviews.map(renderReview)
          ) : (
            <View style={styles.emptyContainer}>
              <Icon name="rate-review" size={64} color="#cccccc" />
              <Text style={styles.emptyTitle}>Нет отзывов</Text>
              <Text style={styles.emptyDescription}>
                Станьте первым, кто оставит отзыв об этом месте
              </Text>
              <TouchableOpacity 
                style={styles.writeFirstReviewButton}
                onPress={handleWriteReview}
              >
                <Text style={styles.writeFirstReviewText}>Написать отзыв</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  writeReviewButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  ratingSummary: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  averageRating: {
    alignItems: 'center',
    marginBottom: 20,
  },
  averageRatingNumber: {
    fontSize: 48,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  totalReviews: {
    fontSize: 14,
    color: '#666666',
  },
  ratingDistribution: {
    gap: 8,
  },
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ratingNumber: {
    fontSize: 14,
    color: '#333333',
    width: 12,
  },
  ratingBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  ratingBarFill: {
    height: '100%',
    backgroundColor: '#FFD700',
  },
  ratingCount: {
    fontSize: 12,
    color: '#666666',
    width: 24,
    textAlign: 'right',
  },
  filtersContainer: {
    paddingVertical: 16,
    paddingLeft: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: '#E91E63',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  filterTextActive: {
    color: '#ffffff',
  },
  reviewsContainer: {
    paddingHorizontal: 20,
  },
  reviewCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userAvatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  reviewDate: {
    fontSize: 12,
    color: '#999999',
  },
  reviewComment: {
    fontSize: 14,
    color: '#333333',
    lineHeight: 20,
    marginBottom: 12,
  },
  reviewPhotos: {
    marginBottom: 12,
  },
  reviewPhoto: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
  },
  reviewActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  helpfulButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    gap: 4,
  },
  helpfulButtonActive: {
    backgroundColor: '#E91E63' + '20',
  },
  helpfulText: {
    fontSize: 12,
    color: '#666666',
  },
  helpfulTextActive: {
    color: '#E91E63',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  editText: {
    fontSize: 12,
    color: '#666666',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 20,
  },
  writeFirstReviewButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  writeFirstReviewText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  bottomPadding: {
    height: 40,
  },
});

export default PlaceReviewsScreen;
