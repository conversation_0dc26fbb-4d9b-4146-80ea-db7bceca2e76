import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface ReportScreenProps {}

interface RouteParams {
  userId?: string;
  reportType?: 'user' | 'content' | 'bug' | 'other';
}

const ReportScreen: React.FC<ReportScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { userId, reportType = 'other' } = (route.params as RouteParams) || {};

  const [selectedReason, setSelectedReason] = useState<string>('');
  const [description, setDescription] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const reportReasons = {
    user: [
      { id: 'fake_profile', title: 'Поддельный профиль', description: 'Использует чужие фото или ложную информацию' },
      { id: 'inappropriate_behavior', title: 'Неподобающее поведение', description: 'Оскорбления, домогательства или угрозы' },
      { id: 'spam', title: 'Спам', description: 'Рассылает рекламу или нежелательные сообщения' },
      { id: 'underage', title: 'Несовершеннолетний', description: 'Пользователь младше 18 лет' },
      { id: 'scam', title: 'Мошенничество', description: 'Просит деньги или пытается обмануть' },
      { id: 'other', title: 'Другое', description: 'Другая причина жалобы' }
    ],
    content: [
      { id: 'inappropriate_photo', title: 'Неподходящее фото', description: 'Обнаженка, насилие или неприемлемый контент' },
      { id: 'copyright', title: 'Нарушение авторских прав', description: 'Использование чужих фотографий без разрешения' },
      { id: 'misleading', title: 'Вводящий в заблуждение контент', description: 'Ложная или неточная информация' },
      { id: 'other', title: 'Другое', description: 'Другая причина жалобы на контент' }
    ],
    bug: [
      { id: 'app_crash', title: 'Приложение вылетает', description: 'Приложение неожиданно закрывается' },
      { id: 'feature_not_working', title: 'Функция не работает', description: 'Какая-то функция работает неправильно' },
      { id: 'slow_performance', title: 'Медленная работа', description: 'Приложение работает медленно' },
      { id: 'ui_issue', title: 'Проблема с интерфейсом', description: 'Элементы отображаются неправильно' },
      { id: 'other', title: 'Другое', description: 'Другая техническая проблема' }
    ],
    other: [
      { id: 'suggestion', title: 'Предложение', description: 'Идея по улучшению приложения' },
      { id: 'question', title: 'Вопрос', description: 'Общий вопрос о приложении' },
      { id: 'feedback', title: 'Отзыв', description: 'Общий отзыв о приложении' },
      { id: 'other', title: 'Другое', description: 'Другая причина обращения' }
    ]
  };

  const currentReasons = reportReasons[reportType] || reportReasons.other;

  const handleSubmit = async () => {
    if (!selectedReason) {
      Alert.alert('Ошибка', 'Выберите причину жалобы');
      return;
    }

    if (!description.trim()) {
      Alert.alert('Ошибка', 'Опишите проблему подробнее');
      return;
    }

    try {
      setSubmitting(true);

      // TODO: Implement report submission
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

      Alert.alert(
        'Жалоба отправлена',
        'Спасибо за обращение! Мы рассмотрим вашу жалобу в течение 24 часов.',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить жалобу. Попробуйте позже.');
    } finally {
      setSubmitting(false);
    }
  };

  const getTitle = () => {
    switch (reportType) {
      case 'user':
        return 'Пожаловаться на пользователя';
      case 'content':
        return 'Пожаловаться на контент';
      case 'bug':
        return 'Сообщить об ошибке';
      default:
        return 'Обратиться в поддержку';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{getTitle()}</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Reasons */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Выберите причину</Text>
          {currentReasons.map((reason) => (
            <TouchableOpacity
              key={reason.id}
              style={[
                styles.reasonItem,
                selectedReason === reason.id && styles.reasonItemSelected
              ]}
              onPress={() => setSelectedReason(reason.id)}
            >
              <View style={styles.reasonContent}>
                <Text style={[
                  styles.reasonTitle,
                  selectedReason === reason.id && styles.reasonTitleSelected
                ]}>
                  {reason.title}
                </Text>
                <Text style={[
                  styles.reasonDescription,
                  selectedReason === reason.id && styles.reasonDescriptionSelected
                ]}>
                  {reason.description}
                </Text>
              </View>
              <View style={[
                styles.radioButton,
                selectedReason === reason.id && styles.radioButtonSelected
              ]}>
                {selectedReason === reason.id && (
                  <View style={styles.radioButtonInner} />
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Опишите проблему подробнее</Text>
          <TextInput
            style={styles.textArea}
            placeholder="Расскажите подробнее о проблеме..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={6}
            textAlignVertical="top"
            maxLength={1000}
          />
          <Text style={styles.characterCount}>
            {description.length}/1000 символов
          </Text>
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, (!selectedReason || !description.trim() || submitting) && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={!selectedReason || !description.trim() || submitting}
        >
          {submitting ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <Text style={styles.submitButtonText}>Отправить жалобу</Text>
          )}
        </TouchableOpacity>

        {/* Info */}
        <View style={styles.infoContainer}>
          <Icon name="info" size={20} color="#666666" />
          <Text style={styles.infoText}>
            Все жалобы рассматриваются в течение 24 часов. Ложные жалобы могут привести к блокировке аккаунта.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  reasonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  reasonItemSelected: {
    backgroundColor: '#E91E63' + '10',
    borderColor: '#E91E63',
  },
  reasonContent: {
    flex: 1,
  },
  reasonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  reasonTitleSelected: {
    color: '#E91E63',
  },
  reasonDescription: {
    fontSize: 14,
    color: '#666666',
  },
  reasonDescriptionSelected: {
    color: '#E91E63',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#cccccc',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  radioButtonSelected: {
    borderColor: '#E91E63',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#E91E63',
  },
  textArea: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    minHeight: 120,
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 8,
  },
  submitButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginVertical: 20,
  },
  submitButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f0f8ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
});

export default ReportScreen;