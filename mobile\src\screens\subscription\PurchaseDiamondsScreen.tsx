import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  Image
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface PurchaseDiamondsScreenProps {}

interface DiamondPackage {
  id: string;
  diamonds: number;
  price: number;
  currency: string;
  originalPrice?: number;
  discount?: number;
  isPopular?: boolean;
  bonus?: number;
  description: string;
}

const PurchaseDiamondsScreen: React.FC<PurchaseDiamondsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [purchasing, setPurchasing] = useState<string | null>(null);
  const [packages, setPackages] = useState<DiamondPackage[]>([]);
  const [currentBalance, setCurrentBalance] = useState(0);

  useFocusEffect(
    useCallback(() => {
      loadPackages();
      loadCurrentBalance();
    }, [])
  );

  const loadPackages = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual packages from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockPackages: DiamondPackage[] = [
        {
          id: 'small',
          diamonds: 100,
          price: 99,
          currency: 'RUB',
          description: 'Для начала знакомств'
        },
        {
          id: 'medium',
          diamonds: 250,
          price: 199,
          currency: 'RUB',
          originalPrice: 249,
          discount: 20,
          bonus: 25,
          description: 'Популярный выбор'
        },
        {
          id: 'large',
          diamonds: 500,
          price: 349,
          currency: 'RUB',
          originalPrice: 499,
          discount: 30,
          bonus: 100,
          isPopular: true,
          description: 'Лучшее предложение'
        },
        {
          id: 'xlarge',
          diamonds: 1000,
          price: 599,
          currency: 'RUB',
          originalPrice: 999,
          discount: 40,
          bonus: 300,
          description: 'Максимальная выгода'
        },
        {
          id: 'mega',
          diamonds: 2500,
          price: 1299,
          currency: 'RUB',
          originalPrice: 2499,
          discount: 48,
          bonus: 1000,
          description: 'VIP пакет'
        }
      ];
      
      setPackages(mockPackages);
    } catch (error) {
      console.error('Error loading packages:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить пакеты алмазов');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentBalance = async () => {
    try {
      // TODO: Load actual balance from API
      await new Promise(resolve => setTimeout(resolve, 500));
      setCurrentBalance(150);
    } catch (error) {
      console.error('Error loading balance:', error);
    }
  };

  const handlePurchase = async (packageItem: DiamondPackage) => {
    try {
      setPurchasing(packageItem.id);
      
      // TODO: Implement actual purchase logic
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const totalDiamonds = packageItem.diamonds + (packageItem.bonus || 0);
      
      Alert.alert(
        'Покупка успешна!',
        `Вы получили ${totalDiamonds} алмазов`,
        [
          {
            text: 'OK',
            onPress: () => {
              setCurrentBalance(prev => prev + totalDiamonds);
              navigation.goBack();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error purchasing diamonds:', error);
      Alert.alert('Ошибка', 'Не удалось совершить покупку');
    } finally {
      setPurchasing(null);
    }
  };

  const formatPrice = (price: number, currency: string): string => {
    return `${price} ${currency === 'RUB' ? '₽' : currency}`;
  };

  const renderPackage = (packageItem: DiamondPackage) => {
    const isPurchasing = purchasing === packageItem.id;
    const totalDiamonds = packageItem.diamonds + (packageItem.bonus || 0);
    
    return (
      <TouchableOpacity
        key={packageItem.id}
        style={[
          styles.packageCard,
          packageItem.isPopular && styles.packageCardPopular,
          isPurchasing && styles.packageCardPurchasing
        ]}
        onPress={() => handlePurchase(packageItem)}
        disabled={isPurchasing}
      >
        {packageItem.isPopular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>ПОПУЛЯРНЫЙ</Text>
          </View>
        )}
        
        {packageItem.discount && (
          <View style={styles.discountBadge}>
            <Text style={styles.discountBadgeText}>-{packageItem.discount}%</Text>
          </View>
        )}
        
        <View style={styles.packageHeader}>
          <View style={styles.diamondIcon}>
            <Icon name="diamond" size={32} color="#FFD700" />
          </View>
          <Text style={styles.packageDiamonds}>{packageItem.diamonds}</Text>
          {packageItem.bonus && (
            <Text style={styles.packageBonus}>+{packageItem.bonus} бонус</Text>
          )}
        </View>
        
        <Text style={styles.packageDescription}>{packageItem.description}</Text>
        
        <View style={styles.packagePricing}>
          {packageItem.originalPrice && (
            <Text style={styles.originalPrice}>
              {formatPrice(packageItem.originalPrice, packageItem.currency)}
            </Text>
          )}
          <Text style={styles.currentPrice}>
            {formatPrice(packageItem.price, packageItem.currency)}
          </Text>
        </View>
        
        <View style={styles.packageFooter}>
          <Text style={styles.totalDiamonds}>
            Всего: {totalDiamonds} алмазов
          </Text>
          
          {isPurchasing ? (
            <View style={styles.purchasingIndicator}>
              <ActivityIndicator size="small" color="#ffffff" />
              <Text style={styles.purchasingText}>Покупка...</Text>
            </View>
          ) : (
            <View style={styles.purchaseButton}>
              <Text style={styles.purchaseButtonText}>Купить</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка пакетов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Купить алмазы</Text>
        <View style={styles.headerRight}>
          <View style={styles.balanceContainer}>
            <Icon name="diamond" size={16} color="#FFD700" />
            <Text style={styles.balanceText}>{currentBalance}</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info Section */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Icon name="diamond" size={48} color="#FFD700" />
            <Text style={styles.infoTitle}>Алмазы</Text>
            <Text style={styles.infoDescription}>
              Используйте алмазы для отправки подарков, супер-лайков и других премиум функций
            </Text>
          </View>
        </View>

        {/* Packages */}
        <View style={styles.packagesSection}>
          <Text style={styles.sectionTitle}>Выберите пакет</Text>
          <View style={styles.packagesGrid}>
            {packages.map(renderPackage)}
          </View>
        </View>

        {/* Features */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Что можно делать с алмазами</Text>
          
          <View style={styles.featureItem}>
            <Icon name="card-giftcard" size={24} color="#E91E63" />
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Отправлять подарки</Text>
              <Text style={styles.featureDescription}>
                Удивляйте понравившихся людей виртуальными подарками
              </Text>
            </View>
          </View>
          
          <View style={styles.featureItem}>
            <Icon name="star" size={24} color="#FFD700" />
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Супер-лайки</Text>
              <Text style={styles.featureDescription}>
                Покажите особый интерес и увеличьте шансы на взаимность
              </Text>
            </View>
          </View>
          
          <View style={styles.featureItem}>
            <Icon name="flash-on" size={24} color="#FF9800" />
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Буст профиля</Text>
              <Text style={styles.featureDescription}>
                Станьте более заметными в поиске на 30 минут
              </Text>
            </View>
          </View>
          
          <View style={styles.featureItem}>
            <Icon name="undo" size={24} color="#2196F3" />
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Отмена действий</Text>
              <Text style={styles.featureDescription}>
                Верните случайно отклоненную анкету
              </Text>
            </View>
          </View>
        </View>

        {/* Security */}
        <View style={styles.securitySection}>
          <View style={styles.securityCard}>
            <Icon name="security" size={24} color="#4CAF50" />
            <Text style={styles.securityText}>
              Все платежи защищены и обрабатываются через безопасные платежные системы
            </Text>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 80,
    alignItems: 'flex-end',
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  balanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  content: {
    flex: 1,
  },
  infoSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  infoCard: {
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    borderRadius: 16,
    padding: 24,
  },
  infoTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 16,
    marginBottom: 12,
  },
  infoDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  packagesSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
  },
  packagesGrid: {
    gap: 16,
  },
  packageCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  packageCardPopular: {
    borderColor: '#E91E63',
    backgroundColor: '#E91E63' + '05',
  },
  packageCardPurchasing: {
    opacity: 0.7,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 20,
    right: 20,
    backgroundColor: '#E91E63',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#ffffff',
  },
  discountBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#F44336',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  discountBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#ffffff',
  },
  packageHeader: {
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  diamondIcon: {
    marginBottom: 8,
  },
  packageDiamonds: {
    fontSize: 32,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  packageBonus: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4CAF50',
  },
  packageDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
  },
  packagePricing: {
    alignItems: 'center',
    marginBottom: 16,
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
    marginBottom: 4,
  },
  currentPrice: {
    fontSize: 24,
    fontWeight: '700',
    color: '#E91E63',
  },
  packageFooter: {
    alignItems: 'center',
  },
  totalDiamonds: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  purchaseButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  purchaseButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  purchasingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#cccccc',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    gap: 8,
  },
  purchasingText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  featuresSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderTopWidth: 8,
    borderTopColor: '#f5f5f5',
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  featureContent: {
    flex: 1,
    marginLeft: 16,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  securitySection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  securityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E8F5E8',
    borderRadius: 12,
    padding: 16,
  },
  securityText: {
    flex: 1,
    fontSize: 14,
    color: '#2E7D32',
    marginLeft: 12,
    lineHeight: 18,
  },
  bottomPadding: {
    height: 40,
  },
});

export default PurchaseDiamondsScreen;
