import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

interface NotificationSettingsScreenProps {}

interface NotificationSettings {
  pushNotifications: {
    enabled: boolean;
    matches: boolean;
    messages: boolean;
    likes: boolean;
    superLikes: boolean;
    visits: boolean;
    promotions: boolean;
  };
  emailNotifications: {
    enabled: boolean;
    matches: boolean;
    messages: boolean;
    weeklyDigest: boolean;
    promotions: boolean;
    securityAlerts: boolean;
  };
  inAppNotifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    showPreviews: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  frequency: {
    matches: 'instant' | 'hourly' | 'daily' | 'off';
    messages: 'instant' | 'hourly' | 'daily' | 'off';
    likes: 'instant' | 'hourly' | 'daily' | 'off';
  };
}

const NotificationSettingsScreen: React.FC<NotificationSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadNotificationSettings();
      checkNotificationPermissions();
    }, [])
  );

  const loadNotificationSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      let notificationSettings: NotificationSettings;

      if (savedSettings) {
        notificationSettings = JSON.parse(savedSettings);
      } else {
        // Default settings
        notificationSettings = {
          pushNotifications: {
            enabled: true,
            matches: true,
            messages: true,
            likes: true,
            superLikes: true,
            visits: false,
            promotions: false
          },
          emailNotifications: {
            enabled: true,
            matches: true,
            messages: false,
            weeklyDigest: true,
            promotions: false,
            securityAlerts: true
          },
          inAppNotifications: {
            enabled: true,
            sound: true,
            vibration: true,
            showPreviews: true
          },
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00'
          },
          frequency: {
            matches: 'instant',
            messages: 'instant',
            likes: 'hourly'
          }
        };
      }

      // TODO: Load from API/user preferences
      await new Promise(resolve => setTimeout(resolve, 500));

      setSettings(notificationSettings);
    } catch (err: any) {
      setError('Ошибка загрузки настроек уведомлений');
      console.error('Notification settings loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionStatus(status);
    } catch (error) {
      console.error('Permission check error:', error);
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);

      if (status === 'granted') {
        Alert.alert('Разрешения получены', 'Теперь вы будете получать уведомления');
      } else {
        Alert.alert(
          'Разрешения не получены',
          'Для получения уведомлений разрешите их в настройках устройства'
        );
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось запросить разрешения');
    }
  };

  const saveSettings = async (updatedSettings: Partial<NotificationSettings>) => {
    if (!settings) return;

    try {
      setSaving(true);

      const newSettings = { ...settings, ...updatedSettings };

      // Save to AsyncStorage
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(newSettings));

      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 300));

      setSettings(newSettings);
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handlePushToggle = async (key: keyof NotificationSettings['pushNotifications'], value: boolean) => {
    if (!settings) return;

    if (key === 'enabled' && value && permissionStatus !== 'granted') {
      await requestNotificationPermissions();
      if (permissionStatus !== 'granted') return;
    }

    await saveSettings({
      pushNotifications: {
        ...settings.pushNotifications,
        [key]: value
      }
    });
  };

  const handleEmailToggle = async (key: keyof NotificationSettings['emailNotifications'], value: boolean) => {
    if (!settings) return;

    await saveSettings({
      emailNotifications: {
        ...settings.emailNotifications,
        [key]: value
      }
    });
  };

  const handleInAppToggle = async (key: keyof NotificationSettings['inAppNotifications'], value: boolean) => {
    if (!settings) return;

    await saveSettings({
      inAppNotifications: {
        ...settings.inAppNotifications,
        [key]: value
      }
    });
  };

  const handleQuietHoursToggle = async (enabled: boolean) => {
    if (!settings) return;

    await saveSettings({
      quietHours: {
        ...settings.quietHours,
        enabled
      }
    });
  };

  const handleFrequencyChange = async (type: keyof NotificationSettings['frequency'], frequency: string) => {
    if (!settings) return;

    await saveSettings({
      frequency: {
        ...settings.frequency,
        [type]: frequency as 'instant' | 'hourly' | 'daily' | 'off'
      }
    });
  };

  const getFrequencyLabel = (frequency: string): string => {
    switch (frequency) {
      case 'instant': return 'Мгновенно';
      case 'hourly': return 'Каждый час';
      case 'daily': return 'Раз в день';
      case 'off': return 'Отключено';
      default: return frequency;
    }
  };

  const getPermissionStatusColor = (status: string): string => {
    switch (status) {
      case 'granted': return '#4CAF50';
      case 'denied': return '#F44336';
      case 'undetermined': return '#FF9800';
      default: return '#666666';
    }
  };

  const getPermissionStatusLabel = (status: string): string => {
    switch (status) {
      case 'granted': return 'Разрешены';
      case 'denied': return 'Запрещены';
      case 'undetermined': return 'Не определены';
      default: return 'Неизвестно';
    }
  };

  const renderSwitch = (value: boolean, onToggle: (value: boolean) => void, disabled: boolean = false) => (
    <TouchableOpacity
      style={[
        styles.switch,
        value && styles.switchActive,
        disabled && styles.switchDisabled
      ]}
      onPress={() => !disabled && onToggle(!value)}
      disabled={disabled || saving}
    >
      <View style={[
        styles.switchThumb,
        value && styles.switchThumbActive,
        disabled && styles.switchThumbDisabled
      ]} />
    </TouchableOpacity>
  );

  const renderFrequencySelector = (
    type: keyof NotificationSettings['frequency'],
    currentValue: string
  ) => (
    <View style={styles.frequencySelector}>
      {(['instant', 'hourly', 'daily', 'off'] as const).map((frequency) => (
        <TouchableOpacity
          key={frequency}
          style={[
            styles.frequencyOption,
            currentValue === frequency && styles.frequencyOptionActive
          ]}
          onPress={() => handleFrequencyChange(type, frequency)}
          disabled={saving}
        >
          <Text style={[
            styles.frequencyOptionText,
            currentValue === frequency && styles.frequencyOptionTextActive
          ]}>
            {getFrequencyLabel(frequency)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек уведомлений...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !settings) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error || 'Настройки не найдены'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadNotificationSettings}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Уведомления</Text>
        <View style={styles.headerRight}>
          {saving && <ActivityIndicator size="small" color="#E91E63" />}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Permission Status */}
        <View style={styles.section}>
          <View style={styles.permissionStatus}>
            <View style={styles.permissionInfo}>
              <Icon name="notifications" size={24} color={getPermissionStatusColor(permissionStatus)} />
              <View style={styles.permissionDetails}>
                <Text style={styles.permissionTitle}>Разрешения на уведомления</Text>
                <Text style={[
                  styles.permissionStatusText,
                  { color: getPermissionStatusColor(permissionStatus) }
                ]}>
                  {getPermissionStatusLabel(permissionStatus)}
                </Text>
              </View>
            </View>
            {permissionStatus !== 'granted' && (
              <TouchableOpacity
                style={styles.permissionButton}
                onPress={requestNotificationPermissions}
              >
                <Text style={styles.permissionButtonText}>Разрешить</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Push Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Push-уведомления</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Включить push-уведомления</Text>
              <Text style={styles.settingDescription}>Получать уведомления на устройство</Text>
            </View>
            {renderSwitch(
              settings.pushNotifications.enabled,
              (value) => handlePushToggle('enabled', value),
              permissionStatus !== 'granted'
            )}
          </View>

          {settings.pushNotifications.enabled && (
            <>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Новые совпадения</Text>
                  <Text style={styles.settingDescription}>Уведомления о взаимных лайках</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.matches,
                  (value) => handlePushToggle('matches', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Сообщения</Text>
                  <Text style={styles.settingDescription}>Новые сообщения в чатах</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.messages,
                  (value) => handlePushToggle('messages', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Лайки</Text>
                  <Text style={styles.settingDescription}>Кто-то поставил вам лайк</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.likes,
                  (value) => handlePushToggle('likes', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Супер-лайки</Text>
                  <Text style={styles.settingDescription}>Кто-то поставил супер-лайк</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.superLikes,
                  (value) => handlePushToggle('superLikes', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Просмотры профиля</Text>
                  <Text style={styles.settingDescription}>Кто-то посмотрел ваш профиль</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.visits,
                  (value) => handlePushToggle('visits', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Акции и предложения</Text>
                  <Text style={styles.settingDescription}>Специальные предложения и скидки</Text>
                </View>
                {renderSwitch(
                  settings.pushNotifications.promotions,
                  (value) => handlePushToggle('promotions', value)
                )}
              </View>
            </>
          )}
        </View>

        {/* Email Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Email-уведомления</Text>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Включить email-уведомления</Text>
              <Text style={styles.settingDescription}>Получать уведомления на почту</Text>
            </View>
            {renderSwitch(
              settings.emailNotifications.enabled,
              (value) => handleEmailToggle('enabled', value)
            )}
          </View>

          {settings.emailNotifications.enabled && (
            <>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Новые совпадения</Text>
                  <Text style={styles.settingDescription}>Email о взаимных лайках</Text>
                </View>
                {renderSwitch(
                  settings.emailNotifications.matches,
                  (value) => handleEmailToggle('matches', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Сообщения</Text>
                  <Text style={styles.settingDescription}>Email о новых сообщениях</Text>
                </View>
                {renderSwitch(
                  settings.emailNotifications.messages,
                  (value) => handleEmailToggle('messages', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Еженедельная сводка</Text>
                  <Text style={styles.settingDescription}>Статистика активности за неделю</Text>
                </View>
                {renderSwitch(
                  settings.emailNotifications.weeklyDigest,
                  (value) => handleEmailToggle('weeklyDigest', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Акции и предложения</Text>
                  <Text style={styles.settingDescription}>Email с предложениями</Text>
                </View>
                {renderSwitch(
                  settings.emailNotifications.promotions,
                  (value) => handleEmailToggle('promotions', value)
                )}
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Уведомления безопасности</Text>
                  <Text style={styles.settingDescription}>Важные уведомления о безопасности</Text>
                </View>
                {renderSwitch(
                  settings.emailNotifications.securityAlerts,
                  (value) => handleEmailToggle('securityAlerts', value)
                )}
              </View>
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
    alignItems: 'flex-end',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  permissionStatus: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  permissionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  permissionDetails: {
    marginLeft: 12,
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  permissionStatusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  permissionButton: {
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  permissionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  switch: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#e0e0e0',
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  switchActive: {
    backgroundColor: '#E91E63',
  },
  switchDisabled: {
    backgroundColor: '#f5f5f5',
    opacity: 0.5,
  },
  switchThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  switchThumbActive: {
    transform: [{ translateX: 20 }],
  },
  switchThumbDisabled: {
    backgroundColor: '#cccccc',
  },
  frequencySelector: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    padding: 2,
    marginTop: 8,
  },
  frequencyOption: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  frequencyOptionActive: {
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  frequencyOptionText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#666666',
  },
  frequencyOptionTextActive: {
    color: '#333333',
    fontWeight: '600',
  },
});

export default NotificationSettingsScreen;