import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import PushNotification from 'react-native-push-notification';

interface NotificationSettingsScreenProps {}

interface NotificationSettings {
  pushEnabled: boolean;
  likes: boolean;
  matches: boolean;
  messages: boolean;
  meetings: boolean;
  events: boolean;
  promotions: boolean;
  systemUpdates: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  soundEnabled: boolean;
  vibrationEnabled: boolean;
}

const NotificationSettingsScreen: React.FC<NotificationSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings>({
    pushEnabled: true,
    likes: true,
    matches: true,
    messages: true,
    meetings: true,
    events: true,
    promotions: false,
    systemUpdates: true,
    emailNotifications: true,
    smsNotifications: false,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00'
    },
    soundEnabled: true,
    vibrationEnabled: true
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);

      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }

      // Check push notification permissions
      PushNotification.checkPermissions((permissions) => {
        setSettings(prev => ({
          ...prev,
          pushEnabled: permissions.alert && permissions.badge && permissions.sound
        }));
      });
    } catch (error) {
      console.error('Error loading notification settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: NotificationSettings) => {
    try {
      setSaving(true);

      // Save to AsyncStorage
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(newSettings));

      // TODO: Send to API
      // await notificationService.updateSettings(newSettings);

      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving notification settings:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleToggle = (key: keyof NotificationSettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value };

    // If disabling push notifications, disable all push-related settings
    if (key === 'pushEnabled' && !value) {
      newSettings.likes = false;
      newSettings.matches = false;
      newSettings.messages = false;
      newSettings.meetings = false;
      newSettings.events = false;
      newSettings.promotions = false;
      newSettings.systemUpdates = false;
    }

    saveSettings(newSettings);
  };

  const handleQuietHoursToggle = (enabled: boolean) => {
    const newSettings = {
      ...settings,
      quietHours: {
        ...settings.quietHours,
        enabled
      }
    };
    saveSettings(newSettings);
  };

  const requestPushPermissions = () => {
    PushNotification.requestPermissions()
      .then((permissions) => {
        if (permissions.alert && permissions.badge && permissions.sound) {
          handleToggle('pushEnabled', true);
        } else {
          Alert.alert(
            'Разрешения не предоставлены',
            'Для получения уведомлений необходимо разрешить отправку push-уведомлений в настройках устройства.'
          );
        }
      })
      .catch((error) => {
        console.error('Error requesting push permissions:', error);
        Alert.alert('Ошибка', 'Не удалось запросить разрешения');
      });
  };

  const renderSettingItem = (
    title: string,
    description: string,
    value: boolean,
    onToggle: (value: boolean) => void,
    disabled: boolean = false,
    icon?: string
  ) => (
    <View style={[styles.settingItem, disabled && styles.settingItemDisabled]}>
      <View style={styles.settingContent}>
        {icon && (
          <Icon
            name={icon}
            size={24}
            color={disabled ? '#cccccc' : '#E91E63'}
            style={styles.settingIcon}
          />
        )}
        <View style={styles.settingText}>
          <Text style={[styles.settingTitle, disabled && styles.disabledText]}>
            {title}
          </Text>
          <Text style={[styles.settingDescription, disabled && styles.disabledText]}>
            {description}
          </Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onToggle}
        disabled={disabled}
        trackColor={{ false: '#e0e0e0', true: '#E91E63' + '40' }}
        thumbColor={value ? '#E91E63' : '#ffffff'}
      />
    </View>
  );

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Уведомления</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Main Push Notifications */}
        {renderSection(
          'Push-уведомления',
          <View>
            <View style={styles.settingItem}>
              <View style={styles.settingContent}>
                <Icon name="notifications" size={24} color="#E91E63" style={styles.settingIcon} />
                <View style={styles.settingText}>
                  <Text style={styles.settingTitle}>Push-уведомления</Text>
                  <Text style={styles.settingDescription}>
                    Получать уведомления на устройство
                  </Text>
                </View>
              </View>
              <TouchableOpacity
                style={styles.enableButton}
                onPress={settings.pushEnabled ? () => handleToggle('pushEnabled', false) : requestPushPermissions}
              >
                <Text style={[
                  styles.enableButtonText,
                  settings.pushEnabled && styles.enableButtonTextActive
                ]}>
                  {settings.pushEnabled ? 'Включено' : 'Включить'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Notification Types */}
        {renderSection(
          'Типы уведомлений',
          <View>
            {renderSettingItem(
              'Лайки',
              'Когда кто-то ставит вам лайк',
              settings.likes,
              (value) => handleToggle('likes', value),
              !settings.pushEnabled,
              'favorite'
            )}
            {renderSettingItem(
              'Новые матчи',
              'Когда появляется взаимная симпатия',
              settings.matches,
              (value) => handleToggle('matches', value),
              !settings.pushEnabled,
              'people'
            )}
            {renderSettingItem(
              'Сообщения',
              'Новые сообщения в чатах',
              settings.messages,
              (value) => handleToggle('messages', value),
              !settings.pushEnabled,
              'message'
            )}
            {renderSettingItem(
              'Встречи',
              'Приглашения и напоминания о встречах',
              settings.meetings,
              (value) => handleToggle('meetings', value),
              !settings.pushEnabled,
              'event'
            )}
            {renderSettingItem(
              'События',
              'Новые события в вашем городе',
              settings.events,
              (value) => handleToggle('events', value),
              !settings.pushEnabled,
              'celebration'
            )}
            {renderSettingItem(
              'Промо-акции',
              'Специальные предложения и скидки',
              settings.promotions,
              (value) => handleToggle('promotions', value),
              !settings.pushEnabled,
              'local-offer'
            )}
            {renderSettingItem(
              'Системные',
              'Важные обновления приложения',
              settings.systemUpdates,
              (value) => handleToggle('systemUpdates', value),
              !settings.pushEnabled,
              'system-update'
            )}
          </View>
        )}

        {/* Other Notification Methods */}
        {renderSection(
          'Другие способы уведомлений',
          <View>
            {renderSettingItem(
              'Email-уведомления',
              'Получать уведомления на почту',
              settings.emailNotifications,
              (value) => handleToggle('emailNotifications', value),
              false,
              'email'
            )}
            {renderSettingItem(
              'SMS-уведомления',
              'Получать SMS с важными уведомлениями',
              settings.smsNotifications,
              (value) => handleToggle('smsNotifications', value),
              false,
              'sms'
            )}
          </View>
        )}

        {/* Sound & Vibration */}
        {renderSection(
          'Звук и вибрация',
          <View>
            {renderSettingItem(
              'Звуковые уведомления',
              'Воспроизводить звук при получении уведомлений',
              settings.soundEnabled,
              (value) => handleToggle('soundEnabled', value),
              !settings.pushEnabled,
              'volume-up'
            )}
            {renderSettingItem(
              'Вибрация',
              'Вибрировать при получении уведомлений',
              settings.vibrationEnabled,
              (value) => handleToggle('vibrationEnabled', value),
              !settings.pushEnabled,
              'vibration'
            )}
          </View>
        )}

        {/* Quiet Hours */}
        {renderSection(
          'Режим "Не беспокоить"',
          <View>
            {renderSettingItem(
              'Тихие часы',
              `Не получать уведомления с ${settings.quietHours.startTime} до ${settings.quietHours.endTime}`,
              settings.quietHours.enabled,
              handleQuietHoursToggle,
              !settings.pushEnabled,
              'do-not-disturb'
            )}

            {settings.quietHours.enabled && settings.pushEnabled && (
              <TouchableOpacity
                style={styles.timeSettingButton}
                onPress={() => {
                  // TODO: Open time picker
                  Alert.alert('Настройка времени', 'Функция будет доступна в следующем обновлении');
                }}
              >
                <Icon name="schedule" size={20} color="#E91E63" />
                <Text style={styles.timeSettingText}>
                  Настроить время ({settings.quietHours.startTime} - {settings.quietHours.endTime})
                </Text>
                <Icon name="chevron-right" size={20} color="#666666" />
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Test Notification */}
        <View style={styles.section}>
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => {
              if (settings.pushEnabled) {
                PushNotification.localNotification({
                  title: 'Тестовое уведомление',
                  message: 'Уведомления работают корректно!',
                  playSound: settings.soundEnabled,
                  vibrate: settings.vibrationEnabled
                });
              } else {
                Alert.alert('Уведомления отключены', 'Включите push-уведомления для тестирования');
              }
            }}
            disabled={!settings.pushEnabled}
          >
            <Icon name="notifications-active" size={20} color={settings.pushEnabled ? "#E91E63" : "#cccccc"} />
            <Text style={[
              styles.testButtonText,
              !settings.pushEnabled && styles.disabledText
            ]}>
              Отправить тестовое уведомление
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {saving && (
        <View style={styles.savingOverlay}>
          <ActivityIndicator size="small" color="#E91E63" />
          <Text style={styles.savingText}>Сохранение...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
    marginHorizontal: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  settingItemDisabled: {
    opacity: 0.5,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  disabledText: {
    color: '#cccccc',
  },
  enableButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  enableButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
  enableButtonTextActive: {
    color: '#4CAF50',
  },
  timeSettingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginTop: 8,
    backgroundColor: '#f5f5f5',
  },
  timeSettingText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingVertical: 16,
    marginHorizontal: 20,
  },
  testButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 8,
  },
  bottomPadding: {
    height: 40,
  },
  savingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  savingText: {
    fontSize: 16,
    color: '#E91E63',
    marginLeft: 12,
  },
});

export default NotificationSettingsScreen;