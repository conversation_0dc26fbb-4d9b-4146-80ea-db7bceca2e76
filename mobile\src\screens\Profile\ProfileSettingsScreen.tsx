import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Switch,
  Alert
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface ProfileSettingsScreenProps {}

interface ProfileSettings {
  showAge: boolean;
  showDistance: boolean;
  showOnlineStatus: boolean;
  allowMessages: boolean;
  allowSuperLikes: boolean;
  showMeInSearch: boolean;
  privateProfile: boolean;
  verifiedOnly: boolean;
  ageRange: {
    min: number;
    max: number;
  };
  maxDistance: number;
  interestedIn: 'men' | 'women' | 'both';
}

interface SettingItem {
  id: keyof ProfileSettings;
  title: string;
  description: string;
  type: 'switch' | 'navigation' | 'range';
  icon: string;
  value?: boolean;
}

const ProfileSettingsScreen: React.FC<ProfileSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<ProfileSettings>({
    showAge: true,
    showDistance: true,
    showOnlineStatus: true,
    allowMessages: true,
    allowSuperLikes: true,
    showMeInSearch: true,
    privateProfile: false,
    verifiedOnly: false,
    ageRange: { min: 18, max: 35 },
    maxDistance: 50,
    interestedIn: 'both'
  });

  const settingsSections = [
    {
      title: 'Видимость профиля',
      items: [
        {
          id: 'showAge' as keyof ProfileSettings,
          title: 'Показывать возраст',
          description: 'Другие пользователи будут видеть ваш возраст',
          type: 'switch' as const,
          icon: 'cake',
          value: settings.showAge
        },
        {
          id: 'showDistance' as keyof ProfileSettings,
          title: 'Показывать расстояние',
          description: 'Отображать расстояние до вас в профиле',
          type: 'switch' as const,
          icon: 'location-on',
          value: settings.showDistance
        },
        {
          id: 'showOnlineStatus' as keyof ProfileSettings,
          title: 'Статус онлайн',
          description: 'Показывать когда вы были в сети',
          type: 'switch' as const,
          icon: 'circle',
          value: settings.showOnlineStatus
        }
      ]
    },
    {
      title: 'Взаимодействие',
      items: [
        {
          id: 'allowMessages' as keyof ProfileSettings,
          title: 'Разрешить сообщения',
          description: 'Получать сообщения от совпадений',
          type: 'switch' as const,
          icon: 'message',
          value: settings.allowMessages
        },
        {
          id: 'allowSuperLikes' as keyof ProfileSettings,
          title: 'Разрешить супер-лайки',
          description: 'Получать супер-лайки от других пользователей',
          type: 'switch' as const,
          icon: 'star',
          value: settings.allowSuperLikes
        }
      ]
    },
    {
      title: 'Приватность',
      items: [
        {
          id: 'showMeInSearch' as keyof ProfileSettings,
          title: 'Показывать в поиске',
          description: 'Ваш профиль будет виден другим пользователям',
          type: 'switch' as const,
          icon: 'search',
          value: settings.showMeInSearch
        },
        {
          id: 'privateProfile' as keyof ProfileSettings,
          title: 'Приватный профиль',
          description: 'Только совпадения могут видеть ваш профиль',
          type: 'switch' as const,
          icon: 'lock',
          value: settings.privateProfile
        },
        {
          id: 'verifiedOnly' as keyof ProfileSettings,
          title: 'Только верифицированные',
          description: 'Показывать только верифицированных пользователей',
          type: 'switch' as const,
          icon: 'verified',
          value: settings.verifiedOnly
        }
      ]
    },
    {
      title: 'Предпочтения',
      items: [
        {
          id: 'ageRange' as keyof ProfileSettings,
          title: 'Возрастной диапазон',
          description: `${settings.ageRange.min} - ${settings.ageRange.max} лет`,
          type: 'navigation' as const,
          icon: 'tune'
        },
        {
          id: 'maxDistance' as keyof ProfileSettings,
          title: 'Максимальное расстояние',
          description: `До ${settings.maxDistance} км`,
          type: 'navigation' as const,
          icon: 'location-on'
        },
        {
          id: 'interestedIn' as keyof ProfileSettings,
          title: 'Интересуют',
          description: settings.interestedIn === 'men' ? 'Мужчины' : 
                      settings.interestedIn === 'women' ? 'Женщины' : 'Все',
          type: 'navigation' as const,
          icon: 'people'
        }
      ]
    }
  ];

  useFocusEffect(
    useCallback(() => {
      loadSettings();
    }, [])
  );

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual settings from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const savedSettings = await AsyncStorage.getItem('profileSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить настройки');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async (newSettings: ProfileSettings) => {
    try {
      setSaving(true);
      
      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      await AsyncStorage.setItem('profileSettings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handleSwitchToggle = (settingId: keyof ProfileSettings, value: boolean) => {
    const newSettings = { ...settings, [settingId]: value };
    saveSettings(newSettings);
  };

  const handleNavigationPress = (settingId: keyof ProfileSettings) => {
    switch (settingId) {
      case 'ageRange':
        navigation.navigate('AgeRangeSettings', {
          currentRange: settings.ageRange,
          onSave: (range: { min: number; max: number }) => {
            saveSettings({ ...settings, ageRange: range });
          }
        });
        break;
      case 'maxDistance':
        navigation.navigate('DistanceSettings', {
          currentDistance: settings.maxDistance,
          onSave: (distance: number) => {
            saveSettings({ ...settings, maxDistance: distance });
          }
        });
        break;
      case 'interestedIn':
        navigation.navigate('InterestedInSettings', {
          currentValue: settings.interestedIn,
          onSave: (value: 'men' | 'women' | 'both') => {
            saveSettings({ ...settings, interestedIn: value });
          }
        });
        break;
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Удалить аккаунт',
      'Вы уверены, что хотите удалить свой аккаунт? Это действие нельзя отменить.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: () => {
            navigation.navigate('DeleteAccount');
          }
        }
      ]
    );
  };

  const renderSettingItem = (item: SettingItem) => {
    return (
      <View key={item.id} style={styles.settingItem}>
        <View style={styles.settingIcon}>
          <Icon name={item.icon} size={24} color="#E91E63" />
        </View>
        
        <View style={styles.settingContent}>
          <Text style={styles.settingTitle}>{item.title}</Text>
          <Text style={styles.settingDescription}>{item.description}</Text>
        </View>
        
        {item.type === 'switch' && (
          <Switch
            value={item.value || false}
            onValueChange={(value) => handleSwitchToggle(item.id, value)}
            trackColor={{ false: '#f0f0f0', true: '#E91E63' + '40' }}
            thumbColor={item.value ? '#E91E63' : '#cccccc'}
            disabled={saving}
          />
        )}
        
        {item.type === 'navigation' && (
          <TouchableOpacity
            style={styles.navigationButton}
            onPress={() => handleNavigationPress(item.id)}
          >
            <Icon name="chevron-right" size={24} color="#666666" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка настроек...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Настройки профиля</Text>
        <View style={styles.headerRight}>
          {saving && <ActivityIndicator size="small" color="#E91E63" />}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {settingsSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            {section.items.map(renderSettingItem)}
          </View>
        ))}

        {/* Danger Zone */}
        <View style={styles.dangerZone}>
          <Text style={styles.dangerZoneTitle}>Опасная зона</Text>
          
          <TouchableOpacity 
            style={styles.dangerButton}
            onPress={handleDeleteAccount}
          >
            <Icon name="delete-forever" size={24} color="#F44336" />
            <View style={styles.dangerButtonContent}>
              <Text style={styles.dangerButtonTitle}>Удалить аккаунт</Text>
              <Text style={styles.dangerButtonDescription}>
                Навсегда удалить ваш аккаунт и все данные
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color="#F44336" />
          </TouchableOpacity>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
    alignItems: 'flex-end',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderBottomWidth: 8,
    borderBottomColor: '#f5f5f5',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 18,
  },
  navigationButton: {
    padding: 8,
  },
  dangerZone: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  dangerZoneTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#F44336',
    marginBottom: 16,
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: '#F44336' + '10',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#F44336' + '20',
  },
  dangerButtonContent: {
    flex: 1,
    marginLeft: 12,
  },
  dangerButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F44336',
    marginBottom: 4,
  },
  dangerButtonDescription: {
    fontSize: 14,
    color: '#F44336',
    opacity: 0.8,
  },
  bottomPadding: {
    height: 40,
  },
});

export default ProfileSettingsScreen;
