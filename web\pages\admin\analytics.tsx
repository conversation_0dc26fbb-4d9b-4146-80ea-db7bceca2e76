import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  DatePicker,
  Alert,
  Skeleton,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  Favorite as FavoriteIcon,
  Chat as ChatIcon,
  Event as EventIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { analyticsService } from '../../services/analyticsService';
import { AnalyticsData, Analytics<PERSON>ilter, <PERSON>ric<PERSON><PERSON> } from '../../types/analytics.types';

interface AdminAnalyticsPageProps {}

const AdminAnalyticsPage: React.FC<AdminAnalyticsPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/admin/analytics');
      return;
    }

    if (user?.role !== 'admin' && user?.role !== 'moderator') {
      router.push('/');
      return;
    }

    loadAnalytics();
  }, [isAuthenticated, user, router, dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);

      const analyticsData = await analyticsService.getAnalytics({
        dateRange,
        includeCharts: true,
        includeTopUsers: true,
        includeTopPlaces: true
      });

      setAnalytics(analyticsData);
    } catch (err: any) {
      setError('Ошибка загрузки аналитики');
      console.error('Analytics loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = async () => {
    try {
      const blob = await analyticsService.exportAnalytics({ dateRange });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics_${dateRange}_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      setError('Ошибка экспорта данных');
    }
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (current: number, previous: number): { value: number; isPositive: boolean } => {
    if (previous === 0) return { value: 0, isPositive: true };
    const change = ((current - previous) / previous) * 100;
    return { value: Math.abs(change), isPositive: change >= 0 };
  };

  const renderMetricCard = (metric: MetricCard) => {
    const change = formatPercentage(metric.current, metric.previous);

    return (
      <Card key={metric.title}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Avatar sx={{ bgcolor: metric.color + '20' }}>
              {metric.icon}
            </Avatar>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {change.isPositive ? (
                <TrendingUpIcon color="success" fontSize="small" />
              ) : (
                <TrendingDownIcon color="error" fontSize="small" />
              )}
              <Typography
                variant="body2"
                color={change.isPositive ? 'success.main' : 'error.main'}
                fontWeight="600"
              >
                {change.value.toFixed(1)}%
              </Typography>
            </Box>
          </Box>

          <Typography variant="h4" component="div" gutterBottom>
            {formatNumber(metric.current)}
          </Typography>

          <Typography variant="body2" color="text.secondary">
            {metric.title}
          </Typography>

          <Typography variant="caption" color="text.secondary">
            {metric.description}
          </Typography>
        </CardContent>
      </Card>
    );
  };

  const renderTopUsers = () => {
    if (!analytics?.topUsers?.length) return null;

    return (
      <Card>
        <CardHeader title="Топ пользователи" />
        <CardContent>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Пользователь</TableCell>
                  <TableCell align="right">Лайки</TableCell>
                  <TableCell align="right">Совпадения</TableCell>
                  <TableCell align="right">Сообщения</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.topUsers.map((user, index) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar src={user.avatar} sx={{ width: 32, height: 32 }}>
                          {user.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="500">
                            {user.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.age} лет, {user.location}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell align="right">{user.stats.likes}</TableCell>
                    <TableCell align="right">{user.stats.matches}</TableCell>
                    <TableCell align="right">{user.stats.messages}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  };

  const renderTopPlaces = () => {
    if (!analytics?.topPlaces?.length) return null;

    return (
      <Card>
        <CardHeader title="Популярные места" />
        <CardContent>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Место</TableCell>
                  <TableCell align="right">Встречи</TableCell>
                  <TableCell align="right">Рейтинг</TableCell>
                  <TableCell align="right">Отзывы</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.topPlaces.map((place, index) => (
                  <TableRow key={place.id}>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="500">
                          {place.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {place.category} • {place.location}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">{place.stats.meetings}</TableCell>
                    <TableCell align="right">
                      <Chip
                        label={place.stats.rating.toFixed(1)}
                        color="primary"
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">{place.stats.reviews}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <Layout>
        <Head>
          <title>Аналитика - Админ-панель</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>
        <Container maxWidth="xl" sx={{ py: 4 }}>
          <Box sx={{ mb: 4 }}>
            <Skeleton variant="text" width={300} height={40} />
            <Skeleton variant="text" width={500} height={24} sx={{ mt: 1 }} />
          </Box>
          <Grid container spacing={3}>
            {[1, 2, 3, 4].map((item) => (
              <Grid item xs={12} sm={6} md={3} key={item}>
                <Skeleton variant="rectangular" height={150} />
              </Grid>
            ))}
          </Grid>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Аналитика - Админ-панель</title>
        <meta name="description" content="Аналитика и статистика платформы LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Аналитика
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Статистика и метрики платформы
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Период</InputLabel>
              <Select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value as any)}
                label="Период"
              >
                <MenuItem value="7d">7 дней</MenuItem>
                <MenuItem value="30d">30 дней</MenuItem>
                <MenuItem value="90d">90 дней</MenuItem>
                <MenuItem value="1y">1 год</MenuItem>
              </Select>
            </FormControl>

            <Tooltip title="Обновить данные">
              <IconButton onClick={loadAnalytics}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>

            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExportData}
            >
              Экспорт
            </Button>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Metrics Cards */}
        {analytics?.metrics && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {analytics.metrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                {renderMetricCard(metric)}
              </Grid>
            ))}
          </Grid>
        )}

        {/* Charts and Tables */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            {renderTopUsers()}
          </Grid>

          <Grid item xs={12} md={6}>
            {renderTopPlaces()}
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default AdminAnalyticsPage;