import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  Share,
  Linking,
  Dimensions
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import MapView, { Marker } from 'react-native-maps';
import LinearGradient from 'react-native-linear-gradient';

const { width, height } = Dimensions.get('window');

interface EventDetailScreenProps {}

interface EventDetail {
  id: string;
  title: string;
  description: string;
  category: string;
  date: string;
  time: string;
  location: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
  organizer: {
    id: string;
    name: string;
    avatar: string;
    isVerified: boolean;
    rating: number;
    eventsCount: number;
  };
  imageUrl: string;
  price: number;
  currency: string;
  attendeesCount: number;
  maxAttendees: number;
  isAttending: boolean;
  isFavorite: boolean;
  tags: string[];
  ageRestriction?: number;
  requirements?: string;
  isOnline: boolean;
  onlineLink?: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  attendees: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
  reviews: Array<{
    id: string;
    userId: string;
    userName: string;
    userAvatar: string;
    rating: number;
    comment: string;
    date: string;
  }>;
  averageRating: number;
  reviewsCount: number;
}

const EventDetailScreen: React.FC<EventDetailScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { eventId } = route.params as { eventId: string };

  const [loading, setLoading] = useState(true);
  const [event, setEvent] = useState<EventDetail | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    loadEventDetail();
  }, [eventId]);

  const loadEventDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockEvent: EventDetail = {
        id: eventId,
        title: 'Speed Dating в центре города',
        description: 'Встреча для знакомства с интересными людьми в уютной атмосфере. Приходите и найдите свою вторую половинку! Мероприятие проводится в формате скоростных свиданий, где каждый участник сможет пообщаться с несколькими потенциальными партнерами.',
        category: 'dating',
        date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
        time: '19:00',
        location: {
          name: 'Кафе "Встреча"',
          address: 'ул. Тверская, 15, Москва',
          latitude: 55.7558,
          longitude: 37.6176
        },
        organizer: {
          id: 'org1',
          name: 'LikesLove Events',
          avatar: 'https://example.com/organizer1.jpg',
          isVerified: true,
          rating: 4.8,
          eventsCount: 25
        },
        imageUrl: 'https://example.com/event1.jpg',
        price: 1500,
        currency: 'RUB',
        attendeesCount: 12,
        maxAttendees: 20,
        isAttending: false,
        isFavorite: false,
        tags: ['знакомства', 'общение', 'вечер'],
        ageRestriction: 18,
        requirements: 'Приходите с хорошим настроением и открытым сердцем!',
        isOnline: false,
        status: 'upcoming',
        attendees: [
          { id: '1', name: 'Анна', avatar: 'https://example.com/user1.jpg' },
          { id: '2', name: 'Мария', avatar: 'https://example.com/user2.jpg' },
          { id: '3', name: 'Елена', avatar: 'https://example.com/user3.jpg' },
          { id: '4', name: 'Дмитрий', avatar: 'https://example.com/user4.jpg' },
          { id: '5', name: 'Александр', avatar: 'https://example.com/user5.jpg' }
        ],
        reviews: [
          {
            id: '1',
            userId: 'user1',
            userName: 'Анна К.',
            userAvatar: 'https://example.com/user1.jpg',
            rating: 5,
            comment: 'Отличное мероприятие! Познакомилась с интересными людьми.',
            date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            userId: 'user2',
            userName: 'Дмитрий М.',
            userAvatar: 'https://example.com/user2.jpg',
            rating: 4,
            comment: 'Хорошая организация, уютная атмосфера.',
            date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        averageRating: 4.5,
        reviewsCount: 8
      };

      setEvent(mockEvent);
    } catch (err: any) {
      setError('Ошибка загрузки события');
      console.error('Event detail loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAttendEvent = async () => {
    if (!event) return;

    try {
      setActionLoading(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setEvent(prev => prev ? {
        ...prev,
        isAttending: !prev.isAttending,
        attendeesCount: prev.isAttending
          ? prev.attendeesCount - 1
          : prev.attendeesCount + 1
      } : null);

      Alert.alert(
        'Успех',
        event.isAttending ? 'Вы отменили участие в событии' : 'Вы записались на событие'
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить статус участия');
    } finally {
      setActionLoading(false);
    }
  };

  const handleFavoriteEvent = async () => {
    if (!event) return;

    try {
      // TODO: Implement actual API call
      setEvent(prev => prev ? { ...prev, isFavorite: !prev.isFavorite } : null);
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось добавить в избранное');
    }
  };

  const handleShareEvent = async () => {
    if (!event) return;

    try {
      await Share.share({
        message: `${event.title}\n${event.description}\n\nДата: ${formatDate(event.date)} в ${event.time}\nМесто: ${event.location.name}`,
        title: event.title
      });
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  const handleOpenLocation = () => {
    if (!event || event.isOnline) return;

    const url = `https://maps.google.com/?q=${event.location.latitude},${event.location.longitude}`;
    Linking.openURL(url);
  };

  const handleOpenOnlineLink = () => {
    if (!event || !event.isOnline || !event.onlineLink) return;

    Linking.openURL(event.onlineLink);
  };

  const handleContactOrganizer = () => {
    if (!event) return;

    navigation.navigate('Chat', { userId: event.organizer.id });
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Сегодня';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Завтра';
    } else {
      return date.toLocaleDateString('ru-RU', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    }
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 24) {
      return `${diffInHours} ч. назад`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} дн. назад`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return '#4CAF50';
      case 'ongoing':
        return '#FF9800';
      case 'completed':
        return '#9E9E9E';
      case 'cancelled':
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'Предстоящее';
      case 'ongoing':
        return 'Идет сейчас';
      case 'completed':
        return 'Завершено';
      case 'cancelled':
        return 'Отменено';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#E91E63" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка события...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !event) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error || 'Событие не найдено'}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadEventDetail}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#E91E63" />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header Image */}
        <View style={styles.imageContainer}>
          <View style={styles.imagePlaceholder}>
            <Icon name="event" size={64} color="#E91E63" />
          </View>

          {/* Header Overlay */}
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageOverlay}
          >
            <View style={styles.headerActions}>
              <TouchableOpacity
                style={styles.headerButton}
                onPress={() => navigation.goBack()}
              >
                <Icon name="arrow-back" size={24} color="#ffffff" />
              </TouchableOpacity>

              <View style={styles.headerRightActions}>
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={handleShareEvent}
                >
                  <Icon name="share" size={24} color="#ffffff" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={handleFavoriteEvent}
                >
                  <Icon
                    name={event.isFavorite ? 'favorite' : 'favorite-border'}
                    size={24}
                    color={event.isFavorite ? '#E91E63' : '#ffffff'}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Status Badge */}
            <View style={styles.statusBadge}>
              <View style={[styles.statusDot, { backgroundColor: getStatusColor(event.status) }]} />
              <Text style={styles.statusText}>{getStatusLabel(event.status)}</Text>
            </View>
          </LinearGradient>
        </View>

        {/* Event Info */}
        <View style={styles.eventInfo}>
          <Text style={styles.eventTitle}>{event.title}</Text>

          <View style={styles.eventMeta}>
            <View style={styles.metaItem}>
              <Icon name="schedule" size={16} color="#666666" />
              <Text style={styles.metaText}>
                {formatDate(event.date)} в {event.time}
              </Text>
            </View>

            <View style={styles.metaItem}>
              <Icon name={event.isOnline ? 'videocam' : 'location-on'} size={16} color="#666666" />
              <Text style={styles.metaText}>
                {event.isOnline ? 'Онлайн' : event.location.name}
              </Text>
            </View>

            <View style={styles.metaItem}>
              <Icon name="people" size={16} color="#666666" />
              <Text style={styles.metaText}>
                {event.attendeesCount}/{event.maxAttendees} участников
              </Text>
            </View>
          </View>

          {/* Price */}
          <View style={styles.priceContainer}>
            {event.price > 0 ? (
              <Text style={styles.price}>{event.price}₽</Text>
            ) : (
              <Text style={styles.priceFree}>Бесплатно</Text>
            )}
          </View>

          {/* Tags */}
          <View style={styles.tagsContainer}>
            {event.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Описание</Text>
          <Text style={styles.description}>{event.description}</Text>
        </View>

        {/* Requirements */}
        {event.requirements && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Требования</Text>
            <Text style={styles.requirements}>{event.requirements}</Text>
          </View>
        )}

        {/* Age Restriction */}
        {event.ageRestriction && (
          <View style={styles.section}>
            <View style={styles.ageRestriction}>
              <Icon name="warning" size={20} color="#FF9800" />
              <Text style={styles.ageRestrictionText}>
                Возрастное ограничение: {event.ageRestriction}+
              </Text>
            </View>
          </View>
        )}

        {/* Organizer */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Организатор</Text>
          <TouchableOpacity style={styles.organizerCard} onPress={handleContactOrganizer}>
            <View style={styles.organizerAvatar}>
              <Icon name="business" size={24} color="#E91E63" />
            </View>

            <View style={styles.organizerInfo}>
              <View style={styles.organizerHeader}>
                <Text style={styles.organizerName}>{event.organizer.name}</Text>
                {event.organizer.isVerified && (
                  <Icon name="verified" size={16} color="#4CAF50" />
                )}
              </View>

              <View style={styles.organizerStats}>
                <View style={styles.organizerStat}>
                  <Icon name="star" size={14} color="#FFD700" />
                  <Text style={styles.organizerStatText}>{event.organizer.rating}</Text>
                </View>
                <Text style={styles.organizerStatText}>
                  {event.organizer.eventsCount} событий
                </Text>
              </View>
            </View>

            <Icon name="chevron-right" size={20} color="#666666" />
          </TouchableOpacity>
        </View>

        {/* Location/Online Link */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {event.isOnline ? 'Ссылка на событие' : 'Место проведения'}
          </Text>

          {event.isOnline ? (
            <TouchableOpacity style={styles.onlineLink} onPress={handleOpenOnlineLink}>
              <Icon name="videocam" size={20} color="#E91E63" />
              <Text style={styles.onlineLinkText}>Присоединиться к событию</Text>
              <Icon name="open-in-new" size={16} color="#666666" />
            </TouchableOpacity>
          ) : (
            <View>
              <TouchableOpacity style={styles.locationCard} onPress={handleOpenLocation}>
                <View style={styles.locationInfo}>
                  <Text style={styles.locationName}>{event.location.name}</Text>
                  <Text style={styles.locationAddress}>{event.location.address}</Text>
                </View>
                <Icon name="directions" size={20} color="#E91E63" />
              </TouchableOpacity>

              <View style={styles.mapContainer}>
                <MapView
                  style={styles.map}
                  initialRegion={{
                    latitude: event.location.latitude,
                    longitude: event.location.longitude,
                    latitudeDelta: 0.01,
                    longitudeDelta: 0.01,
                  }}
                  scrollEnabled={false}
                  zoomEnabled={false}
                >
                  <Marker
                    coordinate={{
                      latitude: event.location.latitude,
                      longitude: event.location.longitude,
                    }}
                    title={event.location.name}
                  />
                </MapView>
              </View>
            </View>
          )}
        </View>

        {/* Attendees */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Участники ({event.attendeesCount})
          </Text>

          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.attendeesContainer}>
              {event.attendees.map((attendee) => (
                <TouchableOpacity key={attendee.id} style={styles.attendeeItem}>
                  <View style={styles.attendeeAvatar}>
                    <Icon name="person" size={20} color="#E91E63" />
                  </View>
                  <Text style={styles.attendeeName}>{attendee.name}</Text>
                </TouchableOpacity>
              ))}

              {event.attendeesCount > event.attendees.length && (
                <View style={styles.attendeeItem}>
                  <View style={styles.attendeeAvatarMore}>
                    <Text style={styles.attendeeMoreText}>
                      +{event.attendeesCount - event.attendees.length}
                    </Text>
                  </View>
                  <Text style={styles.attendeeName}>Еще</Text>
                </View>
              )}
            </View>
          </ScrollView>
        </View>

        {/* Reviews */}
        {event.reviews.length > 0 && (
          <View style={styles.section}>
            <View style={styles.reviewsHeader}>
              <Text style={styles.sectionTitle}>Отзывы</Text>
              <View style={styles.rating}>
                <Icon name="star" size={16} color="#FFD700" />
                <Text style={styles.ratingText}>
                  {event.averageRating} ({event.reviewsCount})
                </Text>
              </View>
            </View>

            {event.reviews.map((review) => (
              <View key={review.id} style={styles.reviewItem}>
                <View style={styles.reviewHeader}>
                  <View style={styles.reviewerAvatar}>
                    <Icon name="person" size={16} color="#E91E63" />
                  </View>

                  <View style={styles.reviewerInfo}>
                    <Text style={styles.reviewerName}>{review.userName}</Text>
                    <View style={styles.reviewRating}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Icon
                          key={star}
                          name="star"
                          size={12}
                          color={star <= review.rating ? '#FFD700' : '#e0e0e0'}
                        />
                      ))}
                    </View>
                  </View>

                  <Text style={styles.reviewDate}>{formatTimeAgo(review.date)}</Text>
                </View>

                <Text style={styles.reviewComment}>{review.comment}</Text>
              </View>
            ))}
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={[
            styles.attendButton,
            event.isAttending && styles.attendButtonActive,
            (event.status === 'completed' || event.status === 'cancelled') && styles.attendButtonDisabled
          ]}
          onPress={handleAttendEvent}
          disabled={actionLoading || event.status === 'completed' || event.status === 'cancelled'}
        >
          {actionLoading ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <>
              <Icon
                name={event.isAttending ? 'check' : 'add'}
                size={20}
                color="#ffffff"
              />
              <Text style={styles.attendButtonText}>
                {event.isAttending ? 'Участвую' : 'Участвовать'}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    position: 'relative',
  },
  imagePlaceholder: {
    flex: 1,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    padding: 8,
  },
  headerRightActions: {
    flexDirection: 'row',
    gap: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  eventInfo: {
    padding: 20,
  },
  eventTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 16,
    lineHeight: 32,
  },
  eventMeta: {
    marginBottom: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  metaText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  priceContainer: {
    marginBottom: 16,
  },
  price: {
    fontSize: 20,
    fontWeight: '700',
    color: '#E91E63',
  },
  priceFree: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4CAF50',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#E91E63',
    fontWeight: '500',
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 24,
  },
  requirements: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 24,
  },
  ageRestriction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    borderRadius: 12,
    padding: 16,
  },
  ageRestrictionText: {
    fontSize: 14,
    color: '#FF9800',
    fontWeight: '500',
    marginLeft: 8,
  },
  organizerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
  },
  organizerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  organizerInfo: {
    flex: 1,
  },
  organizerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  organizerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginRight: 6,
  },
  organizerStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  organizerStat: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  organizerStatText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
  },
  onlineLink: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 12,
    padding: 16,
  },
  onlineLinkText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#E91E63',
    marginLeft: 8,
    flex: 1,
  },
  locationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  locationAddress: {
    fontSize: 14,
    color: '#666666',
  },
  mapContainer: {
    height: 150,
    borderRadius: 12,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  attendeesContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  attendeeItem: {
    alignItems: 'center',
    width: 60,
  },
  attendeeAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  attendeeAvatarMore: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  attendeeName: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
  attendeeMoreText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 4,
  },
  reviewItem: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E91E63' + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  reviewerInfo: {
    flex: 1,
  },
  reviewerName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 2,
  },
  reviewRating: {
    flexDirection: 'row',
    gap: 2,
  },
  reviewDate: {
    fontSize: 12,
    color: '#999999',
  },
  reviewComment: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  bottomPadding: {
    height: 100,
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    padding: 20,
  },
  attendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  attendButtonActive: {
    backgroundColor: '#4CAF50',
  },
  attendButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  attendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default EventDetailScreen;