import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface DataManagementScreenProps {}

interface DataUsage {
  photos: {
    count: number;
    size: string;
  };
  messages: {
    count: number;
    size: string;
  };
  cache: {
    size: string;
  };
  total: {
    size: string;
  };
}

interface ExportData {
  profile: boolean;
  photos: boolean;
  messages: boolean;
  matches: boolean;
  preferences: boolean;
}

const DataManagementScreen: React.FC<DataManagementScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [dataUsage, setDataUsage] = useState<DataUsage | null>(null);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportData, setExportData] = useState<ExportData>({
    profile: true,
    photos: true,
    messages: true,
    matches: true,
    preferences: true
  });
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadDataUsage();
    }, [])
  );

  const loadDataUsage = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockDataUsage: DataUsage = {
        photos: {
          count: 47,
          size: '156.3 МБ'
        },
        messages: {
          count: 1247,
          size: '23.7 МБ'
        },
        cache: {
          size: '89.2 МБ'
        },
        total: {
          size: '269.2 МБ'
        }
      };

      setDataUsage(mockDataUsage);
    } catch (err: any) {
      setError('Ошибка загрузки данных');
      console.error('Data usage loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleClearCache = () => {
    Alert.alert(
      'Очистить кэш',
      'Это удалит временные файлы и может освободить место на устройстве. Продолжить?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Очистить',
          onPress: async () => {
            try {
              setProcessing(true);

              // TODO: Implement actual cache clearing
              await new Promise(resolve => setTimeout(resolve, 2000));

              Alert.alert('Успех', 'Кэш очищен');
              loadDataUsage();
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось очистить кэш');
            } finally {
              setProcessing(false);
            }
          }
        }
      ]
    );
  };

  const handleExportData = async () => {
    try {
      setProcessing(true);

      const selectedData = Object.entries(exportData)
        .filter(([_, selected]) => selected)
        .map(([key, _]) => key);

      if (selectedData.length === 0) {
        Alert.alert('Ошибка', 'Выберите данные для экспорта');
        return;
      }

      // TODO: Implement actual data export
      await new Promise(resolve => setTimeout(resolve, 3000));

      Alert.alert(
        'Экспорт завершен',
        'Ваши данные экспортированы. Ссылка для скачивания отправлена на email.',
        [
          {
            text: 'OK',
            onPress: () => setShowExportModal(false)
          }
        ]
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось экспортировать данные');
    } finally {
      setProcessing(false);
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Удалить аккаунт',
      'Это действие нельзя отменить. Все ваши данные будут безвозвратно удалены.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: () => {
            Alert.alert(
              'Подтвердите удаление',
              'Вы действительно хотите удалить свой аккаунт? Это действие необратимо.',
              [
                { text: 'Отмена', style: 'cancel' },
                {
                  text: 'Да, удалить',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      setProcessing(true);

                      // TODO: Implement actual account deletion
                      await new Promise(resolve => setTimeout(resolve, 2000));

                      Alert.alert(
                        'Аккаунт удален',
                        'Ваш аккаунт был успешно удален',
                        [
                          {
                            text: 'OK',
                            onPress: () => {
                              // TODO: Navigate to login screen
                              navigation.reset({
                                index: 0,
                                routes: [{ name: 'Login' }],
                              });
                            }
                          }
                        ]
                      );
                    } catch (error) {
                      Alert.alert('Ошибка', 'Не удалось удалить аккаунт');
                    } finally {
                      setProcessing(false);
                    }
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };