import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Switch
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface DataManagementScreenProps {}

interface DataUsage {
  photos: number;
  messages: number;
  cache: number;
  total: number;
}

const DataManagementScreen: React.FC<DataManagementScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [dataUsage, setDataUsage] = useState<DataUsage>({
    photos: 0,
    messages: 0,
    cache: 0,
    total: 0
  });
  const [autoBackup, setAutoBackup] = useState(true);
  const [dataCompression, setDataCompression] = useState(false);
  const [clearing, setClearing] = useState(false);

  useEffect(() => {
    loadDataUsage();
  }, []);

  const loadDataUsage = async () => {
    try {
      setLoading(true);
      // TODO: Implement actual data usage calculation
      await new Promise(resolve => setTimeout(resolve, 1000));

      setDataUsage({
        photos: 245.6,
        messages: 89.3,
        cache: 156.7,
        total: 491.6
      });
    } catch (error) {
      console.error('Error loading data usage:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatSize = (sizeInMB: number): string => {
    if (sizeInMB >= 1024) {
      return `${(sizeInMB / 1024).toFixed(1)} ГБ`;
    }
    return `${sizeInMB.toFixed(1)} МБ`;
  };

  const handleClearCache = () => {
    Alert.alert(
      'Очистить кэш',
      'Это действие удалит временные файлы и может замедлить работу приложения при следующем запуске. Продолжить?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Очистить',
          style: 'destructive',
          onPress: clearCache
        }
      ]
    );
  };

  const clearCache = async () => {
    try {
      setClearing(true);
      // TODO: Implement cache clearing
      await new Promise(resolve => setTimeout(resolve, 2000));

      setDataUsage(prev => ({
        ...prev,
        cache: 0,
        total: prev.total - prev.cache
      }));

      Alert.alert('Успешно', 'Кэш очищен');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось очистить кэш');
    } finally {
      setClearing(false);
    }
  };

  const handleExportData = () => {
    Alert.alert(
      'Экспорт данных',
      'Мы подготовим архив с вашими данными и отправим ссылку на email. Это может занять до 24 часов.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Запросить',
          onPress: requestDataExport
        }
      ]
    );
  };

  const requestDataExport = async () => {
    try {
      // TODO: Implement data export request
      Alert.alert(
        'Запрос отправлен',
        'Мы подготовим ваши данные и отправим ссылку для скачивания на ваш email в течение 24 часов.'
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить запрос на экспорт данных');
    }
  };

  const handleDeleteAllData = () => {
    Alert.alert(
      'Удалить все данные',
      'ВНИМАНИЕ! Это действие удалит ВСЕ ваши данные безвозвратно, включая профиль, сообщения, фото и настройки. Это действие нельзя отменить!',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'УДАЛИТЬ ВСЕ',
          style: 'destructive',
          onPress: confirmDeleteAllData
        }
      ]
    );
  };

  const confirmDeleteAllData = () => {
    Alert.alert(
      'Последнее предупреждение',
      'Вы уверены, что хотите удалить ВСЕ данные? Восстановить их будет невозможно.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'ДА, УДАЛИТЬ ВСЕ',
          style: 'destructive',
          onPress: deleteAllData
        }
      ]
    );
  };

  const deleteAllData = async () => {
    try {
      // TODO: Implement complete data deletion
      Alert.alert(
        'Данные удалены',
        'Все ваши данные были удалены. Приложение будет перезапущено.',
        [
          {
            text: 'OK',
            onPress: () => {
              // TODO: Reset app to initial state
            }
          }
        ]
      );
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось удалить данные');
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Анализ данных...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Управление данными</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Data Usage Overview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Использование памяти</Text>
          <View style={styles.usageCard}>
            <View style={styles.totalUsage}>
              <Text style={styles.totalUsageLabel}>Общий размер</Text>
              <Text style={styles.totalUsageValue}>{formatSize(dataUsage.total)}</Text>
            </View>

            <View style={styles.usageBreakdown}>
              <View style={styles.usageItem}>
                <Icon name="photo" size={20} color="#E91E63" />
                <Text style={styles.usageLabel}>Фотографии</Text>
                <Text style={styles.usageValue}>{formatSize(dataUsage.photos)}</Text>
              </View>
              <View style={styles.usageItem}>
                <Icon name="message" size={20} color="#2196F3" />
                <Text style={styles.usageLabel}>Сообщения</Text>
                <Text style={styles.usageValue}>{formatSize(dataUsage.messages)}</Text>
              </View>
              <View style={styles.usageItem}>
                <Icon name="cached" size={20} color="#FF9800" />
                <Text style={styles.usageLabel}>Кэш</Text>
                <Text style={styles.usageValue}>{formatSize(dataUsage.cache)}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Data Management Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Настройки данных</Text>

          <View style={styles.optionItem}>
            <View style={styles.optionContent}>
              <Icon name="backup" size={24} color="#4CAF50" />
              <View style={styles.optionText}>
                <Text style={styles.optionTitle}>Автоматическое резервное копирование</Text>
                <Text style={styles.optionDescription}>
                  Автоматически сохранять данные в облаке
                </Text>
              </View>
            </View>
            <Switch
              value={autoBackup}
              onValueChange={setAutoBackup}
              trackColor={{ false: '#cccccc', true: '#E91E63' }}
              thumbColor={autoBackup ? '#ffffff' : '#ffffff'}
            />
          </View>

          <View style={styles.optionItem}>
            <View style={styles.optionContent}>
              <Icon name="compress" size={24} color="#2196F3" />
              <View style={styles.optionText}>
                <Text style={styles.optionTitle}>Сжатие данных</Text>
                <Text style={styles.optionDescription}>
                  Уменьшить размер фото и видео для экономии места
                </Text>
              </View>
            </View>
            <Switch
              value={dataCompression}
              onValueChange={setDataCompression}
              trackColor={{ false: '#cccccc', true: '#E91E63' }}
              thumbColor={dataCompression ? '#ffffff' : '#ffffff'}
            />
          </View>
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Действия</Text>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleClearCache}
            disabled={clearing || dataUsage.cache === 0}
          >
            <Icon name="cleaning-services" size={24} color="#FF9800" />
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Очистить кэш</Text>
              <Text style={styles.actionDescription}>
                Освободить {formatSize(dataUsage.cache)} места
              </Text>
            </View>
            {clearing ? (
              <ActivityIndicator size="small" color="#FF9800" />
            ) : (
              <Icon name="chevron-right" size={24} color="#666666" />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleExportData}
          >
            <Icon name="download" size={24} color="#2196F3" />
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Экспорт данных</Text>
              <Text style={styles.actionDescription}>
                Скачать копию всех ваших данных
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color="#666666" />
          </TouchableOpacity>
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={styles.dangerTitle}>Опасная зона</Text>

          <TouchableOpacity
            style={styles.dangerButton}
            onPress={handleDeleteAllData}
          >
            <Icon name="delete-forever" size={24} color="#ff4444" />
            <View style={styles.actionContent}>
              <Text style={styles.dangerButtonTitle}>Удалить все данные</Text>
              <Text style={styles.dangerButtonDescription}>
                Безвозвратно удалить профиль и все данные
              </Text>
            </View>
            <Icon name="chevron-right" size={24} color="#ff4444" />
          </TouchableOpacity>
        </View>

        {/* Info */}
        <View style={styles.infoContainer}>
          <Icon name="info" size={20} color="#666666" />
          <Text style={styles.infoText}>
            Данные обновляются автоматически. Для точного расчета может потребоваться время.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  dangerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ff4444',
    marginBottom: 16,
  },
  usageCard: {
    backgroundColor: '#f5f5f5',
    borderRadius: 16,
    padding: 20,
  },
  totalUsage: {
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  totalUsageLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  totalUsageValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#E91E63',
  },
  usageBreakdown: {
    gap: 16,
  },
  usageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  usageLabel: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
    flex: 1,
  },
  usageValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    marginLeft: 16,
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666666',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  actionContent: {
    marginLeft: 16,
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: '#666666',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#ffcccc',
  },
  dangerButtonTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ff4444',
    marginBottom: 4,
  },
  dangerButtonDescription: {
    fontSize: 14,
    color: '#ff6666',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#f0f8ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
  },
});

export default DataManagementScreen;