import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  TextInput,
  Modal,
  Switch
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as ImagePicker from 'expo-image-picker';

interface AccountSettingsScreenProps {}

interface UserProfile {
  id: string;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  avatar?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  location: {
    city: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  preferences: {
    language: string;
    timezone: string;
    currency: string;
    units: 'metric' | 'imperial';
  };
  privacy: {
    showAge: boolean;
    showDistance: boolean;
    showOnlineStatus: boolean;
    allowLocationSharing: boolean;
  };
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    marketing: boolean;
  };
}

const AccountSettingsScreen: React.FC<AccountSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showPhoneModal, setShowPhoneModal] = useState(false);
  const [showNameModal, setShowNameModal] = useState(false);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [tempEmail, setTempEmail] = useState('');
  const [tempPhone, setTempPhone] = useState('');
  const [tempFirstName, setTempFirstName] = useState('');
  const [tempLastName, setTempLastName] = useState('');
  const [tempCity, setTempCity] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockProfile: UserProfile = {
        id: 'user123',
        email: '<EMAIL>',
        phone: '+7 (999) 123-45-67',
        firstName: 'Анна',
        lastName: 'Петрова',
        dateOfBirth: '1995-06-15',
        gender: 'female',
        avatar: 'https://example.com/avatar.jpg',
        isEmailVerified: true,
        isPhoneVerified: false,
        location: {
          city: 'Москва',
          country: 'Россия',
          coordinates: {
            latitude: 55.7558,
            longitude: 37.6176
          }
        },
        preferences: {
          language: 'ru',
          timezone: 'Europe/Moscow',
          currency: 'RUB',
          units: 'metric'
        },
        privacy: {
          showAge: true,
          showDistance: true,
          showOnlineStatus: true,
          allowLocationSharing: true
        },
        notifications: {
          email: true,
          push: true,
          sms: false,
          marketing: false
        }
      };

      setProfile(mockProfile);
      setTempEmail(mockProfile.email);
      setTempPhone(mockProfile.phone || '');
      setTempFirstName(mockProfile.firstName);
      setTempLastName(mockProfile.lastName);
      setTempCity(mockProfile.location.city);
    } catch (err: any) {
      setError('Ошибка загрузки профиля');
      console.error('Profile loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const saveProfile = async (updatedProfile: Partial<UserProfile>) => {
    try {
      setSaving(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setProfile(prev => prev ? { ...prev, ...updatedProfile } : null);

      // Save to AsyncStorage
      if (profile) {
        await AsyncStorage.setItem('userProfile', JSON.stringify({ ...profile, ...updatedProfile }));
      }

      Alert.alert('Успех', 'Профиль обновлен');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось обновить профиль');
    } finally {
      setSaving(false);
    }
  };

  const handleEmailUpdate = async () => {
    if (!tempEmail || !tempEmail.includes('@')) {
      Alert.alert('Ошибка', 'Введите корректный email');
      return;
    }

    await saveProfile({
      email: tempEmail,
      isEmailVerified: false // Need to verify new email
    });
    setShowEmailModal(false);
  };

  const handlePhoneUpdate = async () => {
    if (!tempPhone) {
      Alert.alert('Ошибка', 'Введите номер телефона');
      return;
    }

    await saveProfile({
      phone: tempPhone,
      isPhoneVerified: false // Need to verify new phone
    });
    setShowPhoneModal(false);
  };

  const handleNameUpdate = async () => {
    if (!tempFirstName || !tempLastName) {
      Alert.alert('Ошибка', 'Введите имя и фамилию');
      return;
    }

    await saveProfile({
      firstName: tempFirstName,
      lastName: tempLastName
    });
    setShowNameModal(false);
  };

  const handleLocationUpdate = async () => {
    if (!tempCity) {
      Alert.alert('Ошибка', 'Введите город');
      return;
    }

    await saveProfile({
      location: {
        ...profile!.location,
        city: tempCity
      }
    });
    setShowLocationModal(false);
  };

  const handleAvatarChange = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Ошибка', 'Необходимо разрешение для доступа к галерее');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        // TODO: Upload image to server
        await saveProfile({ avatar: result.assets[0].uri });
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить фото');
    }
  };

  const handleVerifyEmail = async () => {
    try {
      setSaving(true);

      // TODO: Implement actual email verification
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert('Успех', 'Письмо для подтверждения отправлено на ваш email');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить письмо');
    } finally {
      setSaving(false);
    }
  };

  const handleVerifyPhone = async () => {
    try {
      setSaving(true);

      // TODO: Implement actual phone verification
      await new Promise(resolve => setTimeout(resolve, 1000));

      Alert.alert('Успех', 'SMS с кодом подтверждения отправлено');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось отправить SMS');
    } finally {
      setSaving(false);
    }
  };

  const handlePrivacyToggle = async (key: keyof UserProfile['privacy'], value: boolean) => {
    if (!profile) return;

    await saveProfile({
      privacy: {
        ...profile.privacy,
        [key]: value
      }
    });
  };

  const handleNotificationToggle = async (key: keyof UserProfile['notifications'], value: boolean) => {
    if (!profile) return;

    await saveProfile({
      notifications: {
        ...profile.notifications,
        [key]: value
      }
    });
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Удаление аккаунта',
      'Вы уверены, что хотите удалить свой аккаунт? Это действие нельзя отменить.',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: () => {
            // TODO: Implement account deletion
            Alert.alert('Информация', 'Функция удаления аккаунта будет доступна в следующем обновлении');
          }
        }
      ]
    );
  };