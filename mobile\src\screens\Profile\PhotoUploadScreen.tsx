import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  Alert,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { Camera } from 'expo-camera';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');
const PHOTO_SIZE = (width - 60) / 3;

interface PhotoUploadScreenProps {}

interface Photo {
  id: string;
  uri: string;
  isMain: boolean;
  order: number;
  isUploading?: boolean;
  uploadProgress?: number;
}

const PhotoUploadScreen: React.FC<PhotoUploadScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [photos, setPhotos] = useState<Photo[]>([]);
  const [uploading, setUploading] = useState(false);
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null);
  const [mediaLibraryPermission, setMediaLibraryPermission] = useState<boolean | null>(null);

  const maxPhotos = 9;

  useFocusEffect(
    useCallback(() => {
      loadPhotos();
      requestPermissions();
    }, [])
  );

  const requestPermissions = async () => {
    try {
      const cameraResult = await Camera.requestCameraPermissionsAsync();
      setCameraPermission(cameraResult.status === 'granted');

      const mediaResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      setMediaLibraryPermission(mediaResult.status === 'granted');
    } catch (error) {
      console.error('Error requesting permissions:', error);
    }
  };

  const loadPhotos = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual photos from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockPhotos: Photo[] = [
        {
          id: '1',
          uri: 'https://example.com/photo1.jpg',
          isMain: true,
          order: 0
        },
        {
          id: '2',
          uri: 'https://example.com/photo2.jpg',
          isMain: false,
          order: 1
        }
      ];
      
      setPhotos(mockPhotos);
    } catch (error) {
      console.error('Error loading photos:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить фотографии');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPhoto = () => {
    if (photos.length >= maxPhotos) {
      Alert.alert('Лимит фотографий', `Максимальное количество фотографий: ${maxPhotos}`);
      return;
    }

    Alert.alert(
      'Добавить фотографию',
      'Выберите источник фотографии',
      [
        { text: 'Отмена', style: 'cancel' },
        { text: 'Камера', onPress: () => openCamera() },
        { text: 'Галерея', onPress: () => openImagePicker() }
      ]
    );
  };

  const openCamera = async () => {
    if (!cameraPermission) {
      Alert.alert('Разрешение', 'Необходимо разрешение на использование камеры');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening camera:', error);
      Alert.alert('Ошибка', 'Не удалось открыть камеру');
    }
  };

  const openImagePicker = async () => {
    if (!mediaLibraryPermission) {
      Alert.alert('Разрешение', 'Необходимо разрешение на доступ к галерее');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error opening image picker:', error);
      Alert.alert('Ошибка', 'Не удалось открыть галерею');
    }
  };

  const processImage = async (uri: string) => {
    try {
      setUploading(true);

      // Обработка изображения
      const manipulatedImage = await ImageManipulator.manipulateAsync(
        uri,
        [
          { resize: { width: 800, height: 800 } }
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      // Создание нового фото
      const newPhoto: Photo = {
        id: Date.now().toString(),
        uri: manipulatedImage.uri,
        isMain: photos.length === 0,
        order: photos.length,
        isUploading: true,
        uploadProgress: 0
      };

      setPhotos(prev => [...prev, newPhoto]);

      // TODO: Implement actual upload to server
      await simulateUpload(newPhoto.id);

    } catch (error) {
      console.error('Error processing image:', error);
      Alert.alert('Ошибка', 'Не удалось обработать изображение');
    } finally {
      setUploading(false);
    }
  };

  const simulateUpload = async (photoId: string) => {
    // Симуляция загрузки с прогрессом
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setPhotos(prev => prev.map(photo => 
        photo.id === photoId 
          ? { ...photo, uploadProgress: progress }
          : photo
      ));
    }

    // Завершение загрузки
    setPhotos(prev => prev.map(photo => 
      photo.id === photoId 
        ? { ...photo, isUploading: false, uploadProgress: undefined }
        : photo
    ));
  };

  const handleDeletePhoto = (photoId: string) => {
    Alert.alert(
      'Удалить фотографию',
      'Вы уверены, что хотите удалить эту фотографию?',
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement actual deletion from server
              await new Promise(resolve => setTimeout(resolve, 500));
              
              setPhotos(prev => {
                const filtered = prev.filter(photo => photo.id !== photoId);
                // Переназначить порядок и главное фото
                return filtered.map((photo, index) => ({
                  ...photo,
                  order: index,
                  isMain: index === 0
                }));
              });
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось удалить фотографию');
            }
          }
        }
      ]
    );
  };

  const handleSetMainPhoto = async (photoId: string) => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setPhotos(prev => prev.map(photo => ({
        ...photo,
        isMain: photo.id === photoId
      })));
      
      Alert.alert('Успех', 'Главная фотография изменена');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить главную фотографию');
    }
  };

  const renderPhotoSlot = (index: number) => {
    const photo = photos[index];
    
    if (!photo) {
      return (
        <TouchableOpacity
          key={`empty-${index}`}
          style={styles.emptyPhotoSlot}
          onPress={handleAddPhoto}
        >
          <Icon name="add-a-photo" size={32} color="#cccccc" />
          <Text style={styles.addPhotoText}>
            {index === 0 ? 'Главное фото' : 'Добавить фото'}
          </Text>
        </TouchableOpacity>
      );
    }

    return (
      <View key={photo.id} style={styles.photoContainer}>
        <Image source={{ uri: photo.uri }} style={styles.photo} />
        
        {photo.isMain && (
          <View style={styles.mainPhotoBadge}>
            <Text style={styles.mainPhotoText}>Главное</Text>
          </View>
        )}
        
        {photo.isUploading && (
          <View style={styles.uploadOverlay}>
            <ActivityIndicator size="small" color="#ffffff" />
            <Text style={styles.uploadProgress}>
              {photo.uploadProgress}%
            </Text>
          </View>
        )}
        
        <View style={styles.photoActions}>
          {!photo.isMain && (
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleSetMainPhoto(photo.id)}
            >
              <Icon name="star" size={16} color="#ffffff" />
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={[styles.actionButton, styles.deleteButton]}
            onPress={() => handleDeletePhoto(photo.id)}
          >
            <Icon name="delete" size={16} color="#ffffff" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка фотографий...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Мои фотографии</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={handleAddPhoto}
          disabled={photos.length >= maxPhotos}
        >
          <Icon 
            name="add" 
            size={24} 
            color={photos.length >= maxPhotos ? "#cccccc" : "#E91E63"} 
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Info */}
        <View style={styles.infoSection}>
          <Icon name="info" size={20} color="#2196F3" />
          <Text style={styles.infoText}>
            Добавьте до {maxPhotos} фотографий. Первая фотография будет главной и отображаться в профиле.
          </Text>
        </View>

        {/* Photos Grid */}
        <View style={styles.photosGrid}>
          {Array.from({ length: maxPhotos }, (_, index) => renderPhotoSlot(index))}
        </View>

        {/* Tips */}
        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>Советы для лучших фотографий:</Text>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Используйте качественные фотографии</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Покажите свое лицо четко</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Добавьте фотографии в полный рост</Text>
          </View>
          <View style={styles.tipItem}>
            <Icon name="check-circle" size={16} color="#4CAF50" />
            <Text style={styles.tipText}>Покажите свои увлечения</Text>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {uploading && (
        <View style={styles.uploadingOverlay}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.uploadingText}>Загрузка фотографии...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
    marginLeft: 12,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 10,
  },
  emptyPhotoSlot: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    marginBottom: 10,
  },
  addPhotoText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
    marginTop: 8,
  },
  photoContainer: {
    width: PHOTO_SIZE,
    height: PHOTO_SIZE,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    marginBottom: 10,
  },
  photo: {
    width: '100%',
    height: '100%',
    backgroundColor: '#f0f0f0',
  },
  mainPhotoBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  mainPhotoText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  uploadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadProgress: {
    fontSize: 12,
    color: '#ffffff',
    marginTop: 8,
  },
  photoActions: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    flexDirection: 'row',
    gap: 4,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    backgroundColor: 'rgba(244, 67, 54, 0.8)',
  },
  tipsSection: {
    margin: 20,
    padding: 20,
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 12,
    flex: 1,
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    fontSize: 16,
    color: '#ffffff',
    marginTop: 16,
  },
  bottomPadding: {
    height: 40,
  },
});

export default PhotoUploadScreen;
