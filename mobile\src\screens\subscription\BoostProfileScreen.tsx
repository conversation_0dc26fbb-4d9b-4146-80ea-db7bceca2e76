import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  Image,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';

const { width } = Dimensions.get('window');

interface BoostProfileScreenProps {}

interface BoostOption {
  id: string;
  duration: number; // minutes
  price: number;
  currency: string;
  diamondCost?: number;
  multiplier: number;
  isPopular?: boolean;
  description: string;
  benefits: string[];
}

const BoostProfileScreen: React.FC<BoostProfileScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [boosting, setBoosting] = useState<string | null>(null);
  const [boostOptions, setBoostOptions] = useState<BoostOption[]>([]);
  const [currentBoost, setCurrentBoost] = useState<{
    isActive: boolean;
    endTime?: string;
    multiplier?: number;
  }>({ isActive: false });
  const [diamondBalance, setDiamondBalance] = useState(0);

  useFocusEffect(
    useCallback(() => {
      loadBoostOptions();
      loadCurrentBoost();
      loadDiamondBalance();
    }, [])
  );

  const loadBoostOptions = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual boost options from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockOptions: BoostOption[] = [
        {
          id: 'boost_30',
          duration: 30,
          price: 199,
          currency: 'RUB',
          diamondCost: 50,
          multiplier: 3,
          description: 'Быстрый буст',
          benefits: [
            'В 3 раза больше просмотров',
            'Приоритет в поиске',
            'Уведомления о лайках'
          ]
        },
        {
          id: 'boost_60',
          duration: 60,
          price: 349,
          currency: 'RUB',
          diamondCost: 80,
          multiplier: 5,
          isPopular: true,
          description: 'Популярный выбор',
          benefits: [
            'В 5 раз больше просмотров',
            'Топ позиция в поиске',
            'Мгновенные уведомления',
            'Показ в "Популярных"'
          ]
        },
        {
          id: 'boost_180',
          duration: 180,
          price: 799,
          currency: 'RUB',
          diamondCost: 200,
          multiplier: 10,
          description: 'Максимальный эффект',
          benefits: [
            'В 10 раз больше просмотров',
            'Гарантированный топ',
            'Приоритетные уведомления',
            'Показ в "Популярных"',
            'Дополнительные супер-лайки'
          ]
        }
      ];
      
      setBoostOptions(mockOptions);
    } catch (error) {
      console.error('Error loading boost options:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить варианты буста');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentBoost = async () => {
    try {
      // TODO: Load current boost status from API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock current boost (inactive)
      setCurrentBoost({ isActive: false });
    } catch (error) {
      console.error('Error loading current boost:', error);
    }
  };

  const loadDiamondBalance = async () => {
    try {
      // TODO: Load actual diamond balance from API
      await new Promise(resolve => setTimeout(resolve, 500));
      setDiamondBalance(150);
    } catch (error) {
      console.error('Error loading diamond balance:', error);
    }
  };

  const handleBoostPurchase = async (option: BoostOption, paymentMethod: 'money' | 'diamonds') => {
    if (paymentMethod === 'diamonds' && diamondBalance < (option.diamondCost || 0)) {
      Alert.alert(
        'Недостаточно алмазов',
        `Для этого буста нужно ${option.diamondCost} алмазов. У вас: ${diamondBalance}`,
        [
          { text: 'Отмена', style: 'cancel' },
          { text: 'Купить алмазы', onPress: () => navigation.navigate('PurchaseDiamonds') }
        ]
      );
      return;
    }

    Alert.alert(
      'Подтвердить покупку',
      `Активировать буст на ${option.duration} минут?\n\n${
        paymentMethod === 'diamonds' 
          ? `Стоимость: ${option.diamondCost} алмазов`
          : `Стоимость: ${option.price} ₽`
      }`,
      [
        { text: 'Отмена', style: 'cancel' },
        { 
          text: 'Активировать', 
          onPress: () => activateBoost(option, paymentMethod)
        }
      ]
    );
  };

  const activateBoost = async (option: BoostOption, paymentMethod: 'money' | 'diamonds') => {
    try {
      setBoosting(option.id);
      
      // TODO: Implement actual boost activation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (paymentMethod === 'diamonds') {
        setDiamondBalance(prev => prev - (option.diamondCost || 0));
      }
      
      const endTime = new Date(Date.now() + option.duration * 60 * 1000).toISOString();
      setCurrentBoost({
        isActive: true,
        endTime,
        multiplier: option.multiplier
      });
      
      Alert.alert(
        'Буст активирован!',
        `Ваш профиль будет показываться в ${option.multiplier} раз чаще в течение ${option.duration} минут`,
        [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]
      );
    } catch (error) {
      console.error('Error activating boost:', error);
      Alert.alert('Ошибка', 'Не удалось активировать буст');
    } finally {
      setBoosting(null);
    }
  };

  const formatPrice = (price: number, currency: string): string => {
    return `${price} ${currency === 'RUB' ? '₽' : currency}`;
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} мин`;
    } else {
      const hours = Math.floor(minutes / 60);
      return `${hours} ч`;
    }
  };

  const renderBoostOption = (option: BoostOption) => {
    const isBoosting = boosting === option.id;
    
    return (
      <View
        key={option.id}
        style={[
          styles.optionCard,
          option.isPopular && styles.optionCardPopular
        ]}
      >
        {option.isPopular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularBadgeText}>ПОПУЛЯРНЫЙ</Text>
          </View>
        )}
        
        <View style={styles.optionHeader}>
          <View style={styles.boostIcon}>
            <Icon name="flash-on" size={32} color="#FFD700" />
          </View>
          <Text style={styles.optionDuration}>{formatDuration(option.duration)}</Text>
          <Text style={styles.optionMultiplier}>x{option.multiplier}</Text>
        </View>
        
        <Text style={styles.optionDescription}>{option.description}</Text>
        
        <View style={styles.benefitsList}>
          {option.benefits.map((benefit, index) => (
            <View key={index} style={styles.benefitItem}>
              <Icon name="check" size={16} color="#4CAF50" />
              <Text style={styles.benefitText}>{benefit}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.optionPricing}>
          <View style={styles.priceOption}>
            <Text style={styles.priceLabel}>Деньгами</Text>
            <Text style={styles.priceValue}>{formatPrice(option.price, option.currency)}</Text>
            <TouchableOpacity
              style={[
                styles.purchaseButton,
                styles.moneyButton,
                isBoosting && styles.purchaseButtonDisabled
              ]}
              onPress={() => handleBoostPurchase(option, 'money')}
              disabled={isBoosting}
            >
              {isBoosting ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Text style={styles.purchaseButtonText}>Купить</Text>
              )}
            </TouchableOpacity>
          </View>
          
          {option.diamondCost && (
            <View style={styles.priceOption}>
              <Text style={styles.priceLabel}>Алмазами</Text>
              <View style={styles.diamondPrice}>
                <Icon name="diamond" size={16} color="#FFD700" />
                <Text style={styles.priceValue}>{option.diamondCost}</Text>
              </View>
              <TouchableOpacity
                style={[
                  styles.purchaseButton,
                  styles.diamondButton,
                  isBoosting && styles.purchaseButtonDisabled,
                  diamondBalance < option.diamondCost && styles.purchaseButtonDisabled
                ]}
                onPress={() => handleBoostPurchase(option, 'diamonds')}
                disabled={isBoosting || diamondBalance < option.diamondCost}
              >
                {isBoosting ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text style={styles.purchaseButtonText}>
                    {diamondBalance < option.diamondCost ? 'Недостаточно' : 'Купить'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderCurrentBoost = () => {
    if (!currentBoost.isActive) return null;
    
    const endTime = new Date(currentBoost.endTime!);
    const now = new Date();
    const remainingMinutes = Math.max(0, Math.floor((endTime.getTime() - now.getTime()) / (1000 * 60)));
    
    return (
      <View style={styles.currentBoostCard}>
        <LinearGradient
          colors={['#FFD700', '#FFA000']}
          style={styles.currentBoostGradient}
        >
          <Icon name="flash-on" size={32} color="#ffffff" />
          <Text style={styles.currentBoostTitle}>Буст активен!</Text>
          <Text style={styles.currentBoostDetails}>
            Множитель: x{currentBoost.multiplier}
          </Text>
          <Text style={styles.currentBoostTime}>
            Осталось: {remainingMinutes} мин
          </Text>
        </LinearGradient>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка вариантов буста...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Буст профиля</Text>
        <View style={styles.headerRight}>
          <View style={styles.balanceContainer}>
            <Icon name="diamond" size={16} color="#FFD700" />
            <Text style={styles.balanceText}>{diamondBalance}</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Boost */}
        {renderCurrentBoost()}
        
        {/* Info Section */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Icon name="flash-on" size={48} color="#FFD700" />
            <Text style={styles.infoTitle}>Буст профиля</Text>
            <Text style={styles.infoDescription}>
              Увеличьте видимость своего профиля и получите больше совпадений
            </Text>
          </View>
        </View>

        {/* Boost Options */}
        <View style={styles.optionsSection}>
          <Text style={styles.sectionTitle}>Выберите продолжительность</Text>
          {boostOptions.map(renderBoostOption)}
        </View>

        {/* How it works */}
        <View style={styles.howItWorksSection}>
          <Text style={styles.sectionTitle}>Как это работает</Text>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <Text style={styles.stepText}>
              Ваш профиль показывается в топе результатов поиска
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <Text style={styles.stepText}>
              Получаете в несколько раз больше просмотров и лайков
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <Text style={styles.stepText}>
              Увеличиваются шансы на новые совпадения
            </Text>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 80,
    alignItems: 'flex-end',
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  balanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  content: {
    flex: 1,
  },
  currentBoostCard: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
  },
  currentBoostGradient: {
    padding: 24,
    alignItems: 'center',
  },
  currentBoostTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#ffffff',
    marginTop: 12,
    marginBottom: 8,
  },
  currentBoostDetails: {
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 4,
  },
  currentBoostTime: {
    fontSize: 14,
    color: '#ffffff',
    opacity: 0.9,
  },
  infoSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  infoCard: {
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    borderRadius: 16,
    padding: 24,
  },
  infoTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 16,
    marginBottom: 12,
  },
  infoDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  optionsSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
  },
  optionCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  optionCardPopular: {
    borderColor: '#FFD700',
    backgroundColor: '#FFF8E1',
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 20,
    right: 20,
    backgroundColor: '#FFD700',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: 8,
    alignItems: 'center',
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#333333',
  },
  optionHeader: {
    alignItems: 'center',
    marginBottom: 16,
    marginTop: 8,
  },
  boostIcon: {
    marginBottom: 8,
  },
  optionDuration: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  optionMultiplier: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFD700',
  },
  optionDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
  },
  benefitsList: {
    marginBottom: 20,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  benefitText: {
    fontSize: 14,
    color: '#333333',
    marginLeft: 8,
    flex: 1,
  },
  optionPricing: {
    flexDirection: 'row',
    gap: 12,
  },
  priceOption: {
    flex: 1,
    alignItems: 'center',
  },
  priceLabel: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  priceValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  diamondPrice: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  purchaseButton: {
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 16,
    minWidth: 80,
    alignItems: 'center',
  },
  moneyButton: {
    backgroundColor: '#E91E63',
  },
  diamondButton: {
    backgroundColor: '#FFD700',
  },
  purchaseButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  purchaseButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  howItWorksSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderTopWidth: 8,
    borderTopColor: '#f5f5f5',
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E91E63',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
  },
  stepText: {
    fontSize: 16,
    color: '#333333',
    flex: 1,
    lineHeight: 22,
  },
  bottomPadding: {
    height: 40,
  },
});

export default BoostProfileScreen;
