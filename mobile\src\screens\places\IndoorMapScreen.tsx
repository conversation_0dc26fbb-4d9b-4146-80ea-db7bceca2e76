import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  Dimensions,
  PanResponder,
  Animated
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import * as Location from 'expo-location';
import { Svg, Path, Circle, Text as SvgText, G } from 'react-native-svg';

const { width, height } = Dimensions.get('window');

interface IndoorMapScreenProps {}

interface RouteParams {
  placeId: string;
  placeName: string;
  targetShop?: string;
}

interface IndoorLocation {
  x: number;
  y: number;
  floor: number;
  accuracy: number;
}

interface Shop {
  id: string;
  name: string;
  category: string;
  floor: number;
  x: number;
  y: number;
  description?: string;
}

interface FloorPlan {
  floor: number;
  name: string;
  svgPath: string;
  shops: Shop[];
}

const IndoorMapScreen: React.FC<IndoorMapScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { placeId, placeName, targetShop } = (route.params as RouteParams) || {};

  const [loading, setLoading] = useState(true);
  const [currentFloor, setCurrentFloor] = useState(1);
  const [userLocation, setUserLocation] = useState<IndoorLocation | null>(null);
  const [floorPlans, setFloorPlans] = useState<FloorPlan[]>([]);
  const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);
  const [scale, setScale] = useState(1);
  const [translateX] = useState(new Animated.Value(0));
  const [translateY] = useState(new Animated.Value(0));

  // Mock data for demonstration
  const mockFloorPlans: FloorPlan[] = [
    {
      floor: 1,
      name: '1 этаж',
      svgPath: 'M50,50 L350,50 L350,250 L50,250 Z',
      shops: [
        { id: '1', name: 'Starbucks', category: 'Кафе', floor: 1, x: 100, y: 100, description: 'Кофейня' },
        { id: '2', name: 'H&M', category: 'Одежда', floor: 1, x: 200, y: 150, description: 'Магазин одежды' },
        { id: '3', name: 'Аптека', category: 'Здоровье', floor: 1, x: 300, y: 100, description: 'Аптека' }
      ]
    },
    {
      floor: 2,
      name: '2 этаж',
      svgPath: 'M50,50 L350,50 L350,250 L50,250 Z',
      shops: [
        { id: '4', name: 'Кинотеатр', category: 'Развлечения', floor: 2, x: 150, y: 100, description: 'Кинотеатр' },
        { id: '5', name: 'Ресторан', category: 'Еда', floor: 2, x: 250, y: 150, description: 'Ресторан' }
      ]
    }
  ];

  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      translateX.setValue(gestureState.dx);
      translateY.setValue(gestureState.dy);
    },
    onPanResponderRelease: () => {
      Animated.spring(translateX, { toValue: 0, useNativeDriver: true }).start();
      Animated.spring(translateY, { toValue: 0, useNativeDriver: true }).start();
    },
  });

  useFocusEffect(
    useCallback(() => {
      loadIndoorMap();
      startLocationTracking();
    }, [placeId])
  );

  const loadIndoorMap = async () => {
    try {
      setLoading(true);
      
      // TODO: Load actual indoor map data from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setFloorPlans(mockFloorPlans);
      
      // If target shop is specified, find and select it
      if (targetShop) {
        const shop = mockFloorPlans
          .flatMap(floor => floor.shops)
          .find(s => s.name.toLowerCase().includes(targetShop.toLowerCase()));
        
        if (shop) {
          setSelectedShop(shop);
          setCurrentFloor(shop.floor);
        }
      }
    } catch (error) {
      console.error('Error loading indoor map:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить карту помещения');
    } finally {
      setLoading(false);
    }
  };

  const startLocationTracking = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Разрешение', 'Для навигации внутри помещения нужен доступ к геолокации');
        return;
      }

      // TODO: Implement actual indoor positioning using WiFi/Bluetooth beacons
      // For now, simulate user location
      const mockLocation: IndoorLocation = {
        x: 120,
        y: 120,
        floor: 1,
        accuracy: 3
      };
      
      setUserLocation(mockLocation);
    } catch (error) {
      console.error('Error starting location tracking:', error);
    }
  };

  const handleShopPress = (shop: Shop) => {
    setSelectedShop(shop);
    if (shop.floor !== currentFloor) {
      setCurrentFloor(shop.floor);
    }
  };

  const handleNavigateToShop = (shop: Shop) => {
    setIsNavigating(true);
    setSelectedShop(shop);
    
    // TODO: Implement actual navigation logic
    Alert.alert(
      'Навигация',
      `Построен маршрут до ${shop.name} на ${shop.floor} этаже`,
      [
        { text: 'Отмена', onPress: () => setIsNavigating(false) },
        { text: 'Начать', onPress: () => {
          // Start navigation
          console.log('Starting navigation to:', shop.name);
        }}
      ]
    );
  };

  const getCurrentFloorPlan = (): FloorPlan | undefined => {
    return floorPlans.find(plan => plan.floor === currentFloor);
  };

  const renderFloorSelector = () => (
    <View style={styles.floorSelector}>
      {floorPlans.map((plan) => (
        <TouchableOpacity
          key={plan.floor}
          style={[
            styles.floorButton,
            currentFloor === plan.floor && styles.floorButtonActive
          ]}
          onPress={() => setCurrentFloor(plan.floor)}
        >
          <Text style={[
            styles.floorButtonText,
            currentFloor === plan.floor && styles.floorButtonTextActive
          ]}>
            {plan.name}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderMap = () => {
    const currentPlan = getCurrentFloorPlan();
    if (!currentPlan) return null;

    return (
      <Animated.View
        style={[
          styles.mapContainer,
          {
            transform: [
              { translateX },
              { translateY },
              { scale }
            ]
          }
        ]}
        {...panResponder.panHandlers}
      >
        <Svg width={width} height={height - 200} viewBox="0 0 400 300">
          {/* Floor plan */}
          <Path
            d={currentPlan.svgPath}
            fill="none"
            stroke="#333333"
            strokeWidth="2"
          />
          
          {/* Shops */}
          {currentPlan.shops.map((shop) => (
            <G key={shop.id}>
              <Circle
                cx={shop.x}
                cy={shop.y}
                r="15"
                fill={selectedShop?.id === shop.id ? '#E91E63' : '#2196F3'}
                onPress={() => handleShopPress(shop)}
              />
              <SvgText
                x={shop.x}
                y={shop.y + 25}
                fontSize="10"
                textAnchor="middle"
                fill="#333333"
              >
                {shop.name}
              </SvgText>
            </G>
          ))}
          
          {/* User location */}
          {userLocation && userLocation.floor === currentFloor && (
            <Circle
              cx={userLocation.x}
              cy={userLocation.y}
              r="8"
              fill="#4CAF50"
              stroke="#ffffff"
              strokeWidth="2"
            />
          )}
          
          {/* Navigation path */}
          {isNavigating && selectedShop && userLocation && (
            <Path
              d={`M${userLocation.x},${userLocation.y} L${selectedShop.x},${selectedShop.y}`}
              stroke="#E91E63"
              strokeWidth="3"
              strokeDasharray="5,5"
            />
          )}
        </Svg>
      </Animated.View>
    );
  };

  const renderShopInfo = () => {
    if (!selectedShop) return null;

    return (
      <View style={styles.shopInfoPanel}>
        <View style={styles.shopInfoHeader}>
          <View style={styles.shopInfoMain}>
            <Text style={styles.shopName}>{selectedShop.name}</Text>
            <Text style={styles.shopCategory}>{selectedShop.category}</Text>
            <Text style={styles.shopFloor}>{selectedShop.floor} этаж</Text>
          </View>
          
          <TouchableOpacity
            style={styles.navigateButton}
            onPress={() => handleNavigateToShop(selectedShop)}
          >
            <Icon name="directions" size={20} color="#ffffff" />
            <Text style={styles.navigateButtonText}>Маршрут</Text>
          </TouchableOpacity>
        </View>
        
        {selectedShop.description && (
          <Text style={styles.shopDescription}>{selectedShop.description}</Text>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка карты...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>{placeName}</Text>
          <Text style={styles.headerSubtitle}>Карта помещения</Text>
        </View>
        <TouchableOpacity style={styles.locationButton}>
          <Icon name="my-location" size={24} color="#E91E63" />
        </TouchableOpacity>
      </View>

      {/* Floor Selector */}
      {renderFloorSelector()}

      {/* Map */}
      <View style={styles.mapWrapper}>
        {renderMap()}
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity 
          style={styles.controlButton}
          onPress={() => setScale(Math.min(scale * 1.2, 3))}
        >
          <Icon name="zoom-in" size={24} color="#333333" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.controlButton}
          onPress={() => setScale(Math.max(scale / 1.2, 0.5))}
        >
          <Icon name="zoom-out" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      {/* Shop Info Panel */}
      {renderShopInfo()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  locationButton: {
    padding: 8,
  },
  floorSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#f8f8f8',
  },
  floorButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#ffffff',
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  floorButtonActive: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  floorButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  floorButtonTextActive: {
    color: '#ffffff',
  },
  mapWrapper: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  mapContainer: {
    flex: 1,
  },
  controls: {
    position: 'absolute',
    right: 20,
    top: 200,
    gap: 8,
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  shopInfoPanel: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    padding: 20,
  },
  shopInfoHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  shopInfoMain: {
    flex: 1,
  },
  shopName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  shopCategory: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  shopFloor: {
    fontSize: 12,
    color: '#999999',
  },
  navigateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  navigateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  shopDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
});

export default IndoorMapScreen;
