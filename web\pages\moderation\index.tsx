import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Alert,
  Badge,
  Stack
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Check as ApproveIcon,
  Close as RejectIcon,
  Flag as FlagIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Warning as WarningIcon,
  Image as ImageIcon,
  Person as PersonIcon,
  Chat as ChatIcon,
  Report as ReportIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout/Layout';
import { moderationService } from '../../services/moderationService';
import { ModerationItem, ModerationFilter, ModerationStats } from '../../types/moderation.types';

interface ModerationPageProps {}

const ModerationPage: React.FC<ModerationPageProps> = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [items, setItems] = useState<ModerationItem[]>([]);
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ModerationFilter>({});
  const [selectedItem, setSelectedItem] = useState<ModerationItem | null>(null);
  const [viewDialog, setViewDialog] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const tabs = [
    { label: 'Все', value: 'all', icon: <ReportIcon /> },
    { label: 'Фото', value: 'photo', icon: <ImageIcon /> },
    { label: 'Профили', value: 'profile', icon: <PersonIcon /> },
    { label: 'Сообщения', value: 'message', icon: <ChatIcon /> },
    { label: 'Жалобы', value: 'report', icon: <FlagIcon /> }
  ];

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login?redirect=/moderation');
      return;
    }

    if (user?.role !== 'admin' && user?.role !== 'moderator') {
      router.push('/');
      return;
    }

    loadModerationData();
  }, [isAuthenticated, user, router, activeTab, page, searchQuery, filters]);

  const loadModerationData = async () => {
    try {
      setLoading(true);
      setError(null);

      const contentType = tabs[activeTab].value === 'all' ? undefined : tabs[activeTab].value;

      const [itemsData, statsData] = await Promise.all([
        moderationService.getItems({
          page,
          pageSize,
          search: searchQuery,
          contentType,
          ...filters
        }),
        moderationService.getStats()
      ]);

      setItems(itemsData.items);
      setTotalItems(itemsData.total);
      setStats(statsData);
    } catch (err: any) {
      setError('Ошибка загрузки данных модерации');
      console.error('Moderation data loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setPage(1);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(1);
  };

  const handleFilterChange = (filterKey: keyof ModerationFilter, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterKey]: value
    }));
    setPage(1);
  };

  const handleItemAction = async (item: ModerationItem, action: 'approve' | 'reject' | 'delete') => {
    try {
      switch (action) {
        case 'approve':
          await moderationService.approveItem(item.id);
          break;
        case 'reject':
          await moderationService.rejectItem(item.id);
          break;
        case 'delete':
          await moderationService.deleteItem(item.id);
          break;
      }

      await loadModerationData();
    } catch (error) {
      setError(`Ошибка выполнения действия: ${action}`);
    }
  };

  const handleViewItem = (item: ModerationItem) => {
    setSelectedItem(item);
    setViewDialog(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'approved':
        return 'success';
      case 'rejected':
        return 'error';
      case 'flagged':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'На рассмотрении';
      case 'approved':
        return 'Одобрено';
      case 'rejected':
        return 'Отклонено';
      case 'flagged':
        return 'Помечено';
      default:
        return status;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const renderItemPreview = (item: ModerationItem) => {
    switch (item.contentType) {
      case 'photo':
        return (
          <CardMedia
            component="img"
            height="100"
            image={item.contentUrl || '/placeholder-image.jpg'}
            alt="Фото для модерации"
            sx={{ objectFit: 'cover', borderRadius: 1 }}
          />
        );
      case 'profile':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar src={item.userAvatar} sx={{ width: 60, height: 60 }}>
              {item.userName?.charAt(0)}
            </Avatar>
            <Box>
              <Typography variant="subtitle2">{item.userName}</Typography>
              <Typography variant="caption" color="text.secondary">
                {item.userAge} лет
              </Typography>
            </Box>
          </Box>
        );
      case 'message':
        return (
          <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
              "{item.content}"
            </Typography>
          </Box>
        );
      default:
        return (
          <Typography variant="body2" color="text.secondary">
            {item.content || 'Нет превью'}
          </Typography>
        );
    }
  };

  return (
    <Layout>
      <Head>
        <title>Модерация контента - LikesLove</title>
        <meta name="description" content="Модерация пользовательского контента на платформе LikesLove" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Модерация контента
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Управление пользовательским контентом и жалобами
            </Typography>
          </Box>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Stats */}
        {stats && (
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h6">{stats.pending}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        На рассмотрении
                      </Typography>
                    </Box>
                    <Badge badgeContent={stats.pending} color="warning">
                      <WarningIcon color="warning" />
                    </Badge>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h6">{stats.approved}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Одобрено сегодня
                      </Typography>
                    </Box>
                    <ApproveIcon color="success" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h6">{stats.rejected}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Отклонено сегодня
                      </Typography>
                    </Box>
                    <RejectIcon color="error" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography variant="h6">{stats.flagged}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Помечено
                      </Typography>
                    </Box>
                    <FlagIcon color="error" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}