import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  TextInput,
  Alert,
  Dimensions
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface SendGiftScreenProps {}

interface RouteParams {
  gift?: Gift;
  recipientId?: string;
  recipientName?: string;
  recipientAvatar?: string;
}

interface Gift {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  category: string;
  isPopular?: boolean;
  isNew?: boolean;
}

interface Recipient {
  id: string;
  name: string;
  avatar?: string;
  age: number;
  isOnline: boolean;
}

const SendGiftScreen: React.FC<SendGiftScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {
    gift,
    recipientId,
    recipientName,
    recipientAvatar
  } = (route.params as RouteParams) || {};

  const [loading, setLoading] = useState(false);
  const [selectedGift, setSelectedGift] = useState<Gift | null>(gift || null);
  const [selectedRecipient, setSelectedRecipient] = useState<Recipient | null>(null);
  const [message, setMessage] = useState('');
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [userBalance, setUserBalance] = useState(0);

  useFocusEffect(
    useCallback(() => {
      loadUserData();
      if (recipientId) {
        loadRecipientData();
      }
    }, [recipientId])
  );

  const loadUserData = async () => {
    try {
      // TODO: Load actual user balance from API
      await new Promise(resolve => setTimeout(resolve, 500));
      setUserBalance(150); // Mock balance
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadRecipientData = async () => {
    try {
      if (!recipientId) return;
      
      // TODO: Load actual recipient data from API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockRecipient: Recipient = {
        id: recipientId,
        name: recipientName || 'Пользователь',
        avatar: recipientAvatar,
        age: 25,
        isOnline: true
      };
      
      setSelectedRecipient(mockRecipient);
    } catch (error) {
      console.error('Error loading recipient data:', error);
    }
  };

  const handleSelectRecipient = () => {
    navigation.navigate('SelectRecipient', {
      onSelect: (recipient: Recipient) => {
        setSelectedRecipient(recipient);
      }
    });
  };

  const handleSelectGift = () => {
    navigation.navigate('Gifts', {
      onSelect: (gift: Gift) => {
        setSelectedGift(gift);
      }
    });
  };

  const handleSendGift = async () => {
    if (!selectedGift || !selectedRecipient) {
      Alert.alert('Ошибка', 'Выберите подарок и получателя');
      return;
    }

    if (userBalance < selectedGift.price) {
      Alert.alert(
        'Недостаточно средств',
        'У вас недостаточно алмазов для отправки этого подарка. Пополните баланс?',
        [
          { text: 'Отмена', style: 'cancel' },
          { text: 'Пополнить', onPress: () => navigation.navigate('PurchaseDiamonds') }
        ]
      );
      return;
    }

    Alert.alert(
      'Отправить подарок',
      `Отправить "${selectedGift.name}" пользователю ${selectedRecipient.name} за ${selectedGift.price} алмазов?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Отправить',
          onPress: async () => {
            try {
              setLoading(true);
              
              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 2000));
              
              Alert.alert(
                'Подарок отправлен!',
                `Ваш подарок "${selectedGift.name}" успешно отправлен пользователю ${selectedRecipient.name}`,
                [
                  {
                    text: 'OK',
                    onPress: () => navigation.goBack()
                  }
                ]
              );
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось отправить подарок');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const renderGiftCard = () => {
    if (!selectedGift) {
      return (
        <TouchableOpacity style={styles.selectCard} onPress={handleSelectGift}>
          <Icon name="card-giftcard" size={48} color="#E91E63" />
          <Text style={styles.selectCardTitle}>Выбрать подарок</Text>
          <Text style={styles.selectCardDescription}>
            Нажмите, чтобы выбрать подарок из каталога
          </Text>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.giftCard}>
        <Image source={{ uri: selectedGift.image }} style={styles.giftImage} />
        <View style={styles.giftInfo}>
          <Text style={styles.giftName}>{selectedGift.name}</Text>
          <Text style={styles.giftDescription}>{selectedGift.description}</Text>
          <View style={styles.giftPrice}>
            <Icon name="diamond" size={16} color="#FFD700" />
            <Text style={styles.giftPriceText}>{selectedGift.price} алмазов</Text>
          </View>
        </View>
        <TouchableOpacity 
          style={styles.changeButton}
          onPress={handleSelectGift}
        >
          <Icon name="edit" size={20} color="#666666" />
        </TouchableOpacity>
      </View>
    );
  };

  const renderRecipientCard = () => {
    if (!selectedRecipient) {
      return (
        <TouchableOpacity style={styles.selectCard} onPress={handleSelectRecipient}>
          <Icon name="person-add" size={48} color="#E91E63" />
          <Text style={styles.selectCardTitle}>Выбрать получателя</Text>
          <Text style={styles.selectCardDescription}>
            Нажмите, чтобы выбрать получателя подарка
          </Text>
        </TouchableOpacity>
      );
    }

    return (
      <View style={styles.recipientCard}>
        <View style={styles.recipientAvatar}>
          {selectedRecipient.avatar ? (
            <Image source={{ uri: selectedRecipient.avatar }} style={styles.avatarImage} />
          ) : (
            <Icon name="person" size={32} color="#666666" />
          )}
          {selectedRecipient.isOnline && <View style={styles.onlineIndicator} />}
        </View>
        <View style={styles.recipientInfo}>
          <Text style={styles.recipientName}>
            {selectedRecipient.name}, {selectedRecipient.age}
          </Text>
          <Text style={styles.recipientStatus}>
            {selectedRecipient.isOnline ? 'В сети' : 'Не в сети'}
          </Text>
        </View>
        <TouchableOpacity 
          style={styles.changeButton}
          onPress={handleSelectRecipient}
        >
          <Icon name="edit" size={20} color="#666666" />
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Отправить подарок</Text>
        <View style={styles.headerRight}>
          <View style={styles.balanceContainer}>
            <Icon name="diamond" size={16} color="#FFD700" />
            <Text style={styles.balanceText}>{userBalance}</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Gift Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Подарок</Text>
          {renderGiftCard()}
        </View>

        {/* Recipient Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Получатель</Text>
          {renderRecipientCard()}
        </View>

        {/* Message */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Сообщение (необязательно)</Text>
          <TextInput
            style={styles.messageInput}
            placeholder="Добавьте личное сообщение к подарку..."
            value={message}
            onChangeText={setMessage}
            multiline
            numberOfLines={3}
            maxLength={200}
            textAlignVertical="top"
          />
          <Text style={styles.characterCount}>{message.length}/200</Text>
        </View>

        {/* Anonymous Option */}
        <View style={styles.section}>
          <TouchableOpacity 
            style={styles.anonymousOption}
            onPress={() => setIsAnonymous(!isAnonymous)}
          >
            <View style={styles.anonymousInfo}>
              <Text style={styles.anonymousTitle}>Анонимный подарок</Text>
              <Text style={styles.anonymousDescription}>
                Получатель не увидит, кто отправил подарок
              </Text>
            </View>
            <View style={[
              styles.checkbox,
              isAnonymous && styles.checkboxChecked
            ]}>
              {isAnonymous && <Icon name="check" size={16} color="#ffffff" />}
            </View>
          </TouchableOpacity>
        </View>

        {/* Summary */}
        {selectedGift && selectedRecipient && (
          <View style={styles.summarySection}>
            <Text style={styles.summaryTitle}>Итого</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Подарок:</Text>
              <Text style={styles.summaryValue}>{selectedGift.name}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Получатель:</Text>
              <Text style={styles.summaryValue}>{selectedRecipient.name}</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Стоимость:</Text>
              <View style={styles.summaryPrice}>
                <Icon name="diamond" size={16} color="#FFD700" />
                <Text style={styles.summaryPriceText}>{selectedGift.price} алмазов</Text>
              </View>
            </View>
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Send Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.sendButton,
            (!selectedGift || !selectedRecipient || loading) && styles.sendButtonDisabled
          ]}
          onPress={handleSendGift}
          disabled={!selectedGift || !selectedRecipient || loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#ffffff" />
          ) : (
            <>
              <Icon name="send" size={20} color="#ffffff" />
              <Text style={styles.sendButtonText}>Отправить подарок</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 80,
    alignItems: 'flex-end',
  },
  balanceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    gap: 4,
  },
  balanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  selectCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  selectCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
    marginBottom: 8,
  },
  selectCardDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  giftCard: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  giftImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  giftInfo: {
    flex: 1,
    marginLeft: 16,
  },
  giftName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  giftDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
    lineHeight: 18,
  },
  giftPrice: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  giftPriceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
  },
  recipientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recipientAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  avatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  recipientInfo: {
    flex: 1,
    marginLeft: 16,
  },
  recipientName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  recipientStatus: {
    fontSize: 14,
    color: '#4CAF50',
  },
  changeButton: {
    padding: 8,
  },
  messageInput: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#333333',
    minHeight: 80,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  characterCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 8,
  },
  anonymousOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  anonymousInfo: {
    flex: 1,
  },
  anonymousTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  anonymousDescription: {
    fontSize: 14,
    color: '#666666',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#E91E63',
    borderColor: '#E91E63',
  },
  summarySection: {
    backgroundColor: '#f8f8f8',
    margin: 20,
    borderRadius: 12,
    padding: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  summaryPrice: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  summaryPriceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFD700',
  },
  footer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    backgroundColor: '#ffffff',
  },
  sendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  sendButtonDisabled: {
    backgroundColor: '#cccccc',
  },
  sendButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  bottomPadding: {
    height: 40,
  },
});

export default SendGiftScreen;
