import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Linking
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface SafetyTipsScreenProps {}

interface SafetyTip {
  id: string;
  icon: string;
  title: string;
  description: string;
  tips: string[];
}

const SafetyTipsScreen: React.FC<SafetyTipsScreenProps> = () => {
  const navigation = useNavigation();
  const [expandedTip, setExpandedTip] = useState<string | null>(null);

  const safetyTips: SafetyTip[] = [
    {
      id: 'profile_safety',
      icon: 'person',
      title: 'Безопасность профиля',
      description: 'Как защитить свой профиль от мошенников',
      tips: [
        'Не указывайте в профиле личную информацию: адрес, номер телефона, место работы',
        'Используйте только свои фотографии',
        'Не публикуйте фото с номерами автомобилей или адресами',
        'Регулярно проверяйте настройки приватности',
        'Сообщайте о подозрительных профилях'
      ]
    },
    {
      id: 'communication_safety',
      icon: 'chat',
      title: 'Безопасное общение',
      description: 'Правила безопасного общения в чатах',
      tips: [
        'Не делитесь личной информацией в первых сообщениях',
        'Будьте осторожны с людьми, которые просят деньги',
        'Не переходите на сторонние мессенджеры сразу',
        'Доверяйте своей интуиции - если что-то кажется подозрительным, это так и есть',
        'Сообщайте о неподобающем поведении'
      ]
    },
    {
      id: 'meeting_safety',
      icon: 'location_on',
      title: 'Безопасные встречи',
      description: 'Как безопасно встречаться с новыми людьми',
      tips: [
        'Первая встреча - только в общественном месте',
        'Сообщите друзьям о встрече и поделитесь локацией',
        'Встречайтесь днем в людных местах',
        'Добирайтесь на встречу самостоятельно',
        'Доверяйте своим ощущениям - если чувствуете дискомфорт, уходите'
      ]
    },
    {
      id: 'scam_protection',
      icon: 'security',
      title: 'Защита от мошенников',
      description: 'Как распознать и избежать мошенничества',
      tips: [
        'Никогда не отправляйте деньги незнакомым людям',
        'Остерегайтесь слишком красивых профилей с профессиональными фото',
        'Будьте осторожны с людьми, которые быстро предлагают встретиться',
        'Не переходите по подозрительным ссылкам',
        'Проверяйте профили через поиск в интернете'
      ]
    },
    {
      id: 'privacy_settings',
      icon: 'lock',
      title: 'Настройки приватности',
      description: 'Как настроить приватность для максимальной безопасности',
      tips: [
        'Ограничьте видимость вашего профиля',
        'Настройте фильтры для входящих сообщений',
        'Используйте двухфакторную аутентификацию',
        'Регулярно меняйте пароль',
        'Проверяйте активные сессии в настройках'
      ]
    }
  ];

  const toggleTip = (tipId: string) => {
    setExpandedTip(expandedTip === tipId ? null : tipId);
  };

  const handleEmergencyCall = () => {
    Linking.openURL('tel:112');
  };

  const handleReportIncident = () => {
    navigation.navigate('ReportScreen', { reportType: 'other' });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Советы по безопасности</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Emergency Section */}
        <View style={styles.emergencySection}>
          <View style={styles.emergencyHeader}>
            <Icon name="warning" size={24} color="#ff4444" />
            <Text style={styles.emergencyTitle}>Экстренная ситуация?</Text>
          </View>
          <Text style={styles.emergencyDescription}>
            Если вы находитесь в опасности, немедленно обратитесь в службу экстренного реагирования
          </Text>
          <View style={styles.emergencyButtons}>
            <TouchableOpacity style={styles.emergencyButton} onPress={handleEmergencyCall}>
              <Icon name="phone" size={20} color="#ffffff" />
              <Text style={styles.emergencyButtonText}>Вызвать 112</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.reportButton} onPress={handleReportIncident}>
              <Icon name="report" size={20} color="#ff4444" />
              <Text style={styles.reportButtonText}>Сообщить об инциденте</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Safety Tips */}
        <View style={styles.tipsSection}>
          <Text style={styles.sectionTitle}>Советы по безопасности</Text>
          {safetyTips.map((tip) => (
            <View key={tip.id} style={styles.tipCard}>
              <TouchableOpacity
                style={styles.tipHeader}
                onPress={() => toggleTip(tip.id)}
              >
                <View style={styles.tipHeaderLeft}>
                  <View style={styles.tipIcon}>
                    <Icon name={tip.icon} size={24} color="#E91E63" />
                  </View>
                  <View style={styles.tipHeaderText}>
                    <Text style={styles.tipTitle}>{tip.title}</Text>
                    <Text style={styles.tipDescription}>{tip.description}</Text>
                  </View>
                </View>
                <Icon
                  name={expandedTip === tip.id ? "expand_less" : "expand_more"}
                  size={24}
                  color="#666666"
                />
              </TouchableOpacity>

              {expandedTip === tip.id && (
                <View style={styles.tipContent}>
                  {tip.tips.map((tipText, index) => (
                    <View key={index} style={styles.tipItem}>
                      <View style={styles.tipBullet} />
                      <Text style={styles.tipText}>{tipText}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>

        {/* Additional Resources */}
        <View style={styles.resourcesSection}>
          <Text style={styles.sectionTitle}>Дополнительные ресурсы</Text>
          <View style={styles.resourceCard}>
            <Icon name="phone" size={24} color="#E91E63" />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Горячая линия поддержки</Text>
              <Text style={styles.resourceDescription}>
                Круглосуточная поддержка для пользователей
              </Text>
              <Text style={styles.resourceContact}>+7 (800) 555-35-35</Text>
            </View>
          </View>

          <View style={styles.resourceCard}>
            <Icon name="email" size={24} color="#E91E63" />
            <View style={styles.resourceContent}>
              <Text style={styles.resourceTitle}>Email поддержка</Text>
              <Text style={styles.resourceDescription}>
                Напишите нам о проблемах безопасности
              </Text>
              <Text style={styles.resourceContact}><EMAIL></Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  emergencySection: {
    backgroundColor: '#fff5f5',
    borderRadius: 12,
    padding: 20,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: '#ffcccc',
  },
  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  emergencyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ff4444',
    marginLeft: 12,
  },
  emergencyDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 16,
    lineHeight: 20,
  },
  emergencyButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  emergencyButton: {
    backgroundColor: '#ff4444',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  emergencyButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
    marginLeft: 8,
  },
  reportButton: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#ff4444',
  },
  reportButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ff4444',
    marginLeft: 8,
  },
  tipsSection: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
  },
  tipCard: {
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
  },
  tipHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  tipHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  tipIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E91E63' + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  tipHeaderText: {
    flex: 1,
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  tipDescription: {
    fontSize: 14,
    color: '#666666',
  },
  tipContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  tipBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#E91E63',
    marginTop: 8,
    marginRight: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
    lineHeight: 20,
  },
  resourcesSection: {
    marginBottom: 32,
  },
  resourceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  resourceContent: {
    marginLeft: 16,
    flex: 1,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  resourceDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  resourceContact: {
    fontSize: 14,
    fontWeight: '600',
    color: '#E91E63',
  },
});

export default SafetyTipsScreen;