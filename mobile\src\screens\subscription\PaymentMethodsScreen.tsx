import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  TextInput,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface PaymentMethodsScreenProps {}

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';
  isDefault: boolean;
  details: {
    // For cards
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    holderName?: string;
    // For PayPal
    email?: string;
    // For bank transfer
    bankName?: string;
    accountNumber?: string;
  };
  addedAt: string;
  lastUsed?: string;
  isActive: boolean;
}

interface NewCardData {
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  holderName: string;
}

const PaymentMethodsScreen: React.FC<PaymentMethodsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [newCardData, setNewCardData] = useState<NewCardData>({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: ''
  });
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadPaymentMethods();
    }, [])
  );

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: '1',
          type: 'card',
          isDefault: true,
          details: {
            last4: '1234',
            brand: 'Visa',
            expiryMonth: 12,
            expiryYear: 2026,
            holderName: 'Иван Иванов'
          },
          addedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          lastUsed: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        },
        {
          id: '2',
          type: 'card',
          isDefault: false,
          details: {
            last4: '5678',
            brand: 'Mastercard',
            expiryMonth: 8,
            expiryYear: 2025,
            holderName: 'Иван Иванов'
          },
          addedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          lastUsed: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        },
        {
          id: '3',
          type: 'paypal',
          isDefault: false,
          details: {
            email: '<EMAIL>'
          },
          addedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        }
      ];

      setPaymentMethods(mockPaymentMethods);
    } catch (err: any) {
      setError('Ошибка загрузки способов оплаты');
      console.error('Payment methods loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCard = async () => {
    if (!validateCardData()) return;

    try {
      setProcessing(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newCard: PaymentMethod = {
        id: Date.now().toString(),
        type: 'card',
        isDefault: paymentMethods.length === 0,
        details: {
          last4: newCardData.cardNumber.slice(-4),
          brand: getCardBrand(newCardData.cardNumber),
          expiryMonth: parseInt(newCardData.expiryMonth),
          expiryYear: parseInt(newCardData.expiryYear),
          holderName: newCardData.holderName
        },
        addedAt: new Date().toISOString(),
        isActive: true
      };

      setPaymentMethods(prev => [...prev, newCard]);
      setShowAddCardModal(false);
      setNewCardData({
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        holderName: ''
      });

      Alert.alert('Успех', 'Карта успешно добавлена');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось добавить карту');
    } finally {
      setProcessing(false);
    }
  };

  const validateCardData = (): boolean => {
    if (newCardData.cardNumber.replace(/\s/g, '').length < 16) {
      Alert.alert('Ошибка', 'Введите корректный номер карты');
      return false;
    }
    if (!newCardData.expiryMonth || !newCardData.expiryYear) {
      Alert.alert('Ошибка', 'Введите срок действия карты');
      return false;
    }
    if (newCardData.cvv.length < 3) {
      Alert.alert('Ошибка', 'Введите корректный CVV код');
      return false;
    }
    if (!newCardData.holderName.trim()) {
      Alert.alert('Ошибка', 'Введите имя держателя карты');
      return false;
    }
    return true;
  };

  const getCardBrand = (cardNumber: string): string => {
    const number = cardNumber.replace(/\s/g, '');
    if (number.startsWith('4')) return 'Visa';
    if (number.startsWith('5') || number.startsWith('2')) return 'Mastercard';
    if (number.startsWith('3')) return 'American Express';
    return 'Unknown';
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      setProcessing(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setPaymentMethods(prev => prev.map(method => ({
        ...method,
        isDefault: method.id === methodId
      })));

      Alert.alert('Успех', 'Способ оплаты по умолчанию изменен');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить способ оплаты по умолчанию');
    } finally {
      setProcessing(false);
    }
  };

  const handleDeleteMethod = (methodId: string) => {
    const method = paymentMethods.find(m => m.id === methodId);
    if (!method) return;

    Alert.alert(
      'Удалить способ оплаты',
      `Вы уверены, что хотите удалить ${getMethodDisplayName(method)}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              setProcessing(true);

              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 500));

              setPaymentMethods(prev => prev.filter(m => m.id !== methodId));
              Alert.alert('Успех', 'Способ оплаты удален');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось удалить способ оплаты');
            } finally {
              setProcessing(false);
            }
          }
        }
      ]
    );
  };

  const getMethodDisplayName = (method: PaymentMethod): string => {
    switch (method.type) {
      case 'card':
        return `${method.details.brand} •••• ${method.details.last4}`;
      case 'paypal':
        return `PayPal (${method.details.email})`;
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'bank_transfer':
        return `${method.details.bankName} •••• ${method.details.accountNumber}`;
      default:
        return 'Неизвестный способ оплаты';
    }
  };

  const getMethodIcon = (type: string): string => {
    switch (type) {
      case 'card': return 'credit-card';
      case 'paypal': return 'account-balance-wallet';
      case 'apple_pay': return 'phone-iphone';
      case 'google_pay': return 'phone-android';
      case 'bank_transfer': return 'account-balance';
      default: return 'payment';
    }
  };

  const formatCardNumber = (value: string): string => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string): string => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + (v.length > 2 ? '/' + v.substring(2, 4) : '');
    }
    return v;
  };

  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 30) {
      return `${diffInDays} дн. назад`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `${months} мес. назад`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `${years} г. назад`;
    }
  };

  const handleAddPayPal = () => {
    Alert.alert('PayPal', 'Функция добавления PayPal будет доступна в следующих версиях');
  };

  const handleAddApplePay = () => {
    Alert.alert('Apple Pay', 'Функция добавления Apple Pay будет доступна в следующих версиях');
  };

  const handleAddGooglePay = () => {
    Alert.alert('Google Pay', 'Функция добавления Google Pay будет доступна в следующих версиях');
  };

  const renderPaymentMethod = (method: PaymentMethod) => (
    <View key={method.id} style={[styles.paymentMethodCard, method.isDefault && styles.defaultMethodCard]}>
      <View style={styles.methodHeader}>
        <View style={styles.methodInfo}>
          <Icon name={getMethodIcon(method.type)} size={24} color="#E91E63" />
          <View style={styles.methodDetails}>
            <Text style={styles.methodName}>{getMethodDisplayName(method)}</Text>
            {method.details.holderName && (
              <Text style={styles.holderName}>{method.details.holderName}</Text>
            )}
            {method.type === 'card' && method.details.expiryMonth && method.details.expiryYear && (
              <Text style={styles.expiryDate}>
                Действует до {String(method.details.expiryMonth).padStart(2, '0')}/{method.details.expiryYear}
              </Text>
            )}
          </View>
        </View>

        {method.isDefault && (
          <View style={styles.defaultBadge}>
            <Text style={styles.defaultBadgeText}>По умолчанию</Text>
          </View>
        )}
      </View>

      <View style={styles.methodFooter}>
        <View style={styles.methodMeta}>
          <Text style={styles.addedDate}>Добавлено {formatTimeAgo(method.addedAt)}</Text>
          {method.lastUsed && (
            <Text style={styles.lastUsed}>Последнее использование: {formatTimeAgo(method.lastUsed)}</Text>
          )}
        </View>

        <View style={styles.methodActions}>
          {!method.isDefault && (
            <TouchableOpacity
              style={styles.setDefaultButton}
              onPress={() => handleSetDefault(method.id)}
              disabled={processing}
            >
              <Text style={styles.setDefaultButtonText}>По умолчанию</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteMethod(method.id)}
            disabled={processing}
          >
            <Icon name="delete" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderAddMethodOptions = () => (
    <View style={styles.addMethodsSection}>
      <Text style={styles.sectionTitle}>Добавить способ оплаты</Text>

      <TouchableOpacity
        style={styles.addMethodButton}
        onPress={() => setShowAddCardModal(true)}
      >
        <Icon name="credit-card" size={24} color="#E91E63" />
        <Text style={styles.addMethodButtonText}>Добавить карту</Text>
        <Icon name="chevron-right" size={24} color="#666666" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.addMethodButton}
        onPress={handleAddPayPal}
      >
        <Icon name="account-balance-wallet" size={24} color="#E91E63" />
        <Text style={styles.addMethodButtonText}>PayPal</Text>
        <Icon name="chevron-right" size={24} color="#666666" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.addMethodButton}
        onPress={handleAddApplePay}
      >
        <Icon name="phone-iphone" size={24} color="#E91E63" />
        <Text style={styles.addMethodButtonText}>Apple Pay</Text>
        <Icon name="chevron-right" size={24} color="#666666" />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.addMethodButton}
        onPress={handleAddGooglePay}
      >
        <Icon name="phone-android" size={24} color="#E91E63" />
        <Text style={styles.addMethodButtonText}>Google Pay</Text>
        <Icon name="chevron-right" size={24} color="#666666" />
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка способов оплаты...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={64} color="#cccccc" />
          <Text style={styles.errorTitle}>Ошибка загрузки</Text>
          <Text style={styles.errorDescription}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadPaymentMethods}>
            <Text style={styles.retryButtonText}>Повторить</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Способы оплаты</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Payment Methods */}
        {paymentMethods.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ваши способы оплаты</Text>
            {paymentMethods.map(renderPaymentMethod)}
          </View>
        )}

        {/* Add New Methods */}
        {renderAddMethodOptions()}

        {/* Security Info */}
        <View style={styles.securitySection}>
          <View style={styles.securityHeader}>
            <Icon name="security" size={24} color="#4CAF50" />
            <Text style={styles.securityTitle}>Безопасность</Text>
          </View>
          <Text style={styles.securityDescription}>
            Все данные карт шифруются и хранятся в соответствии с международными стандартами безопасности PCI DSS.
          </Text>
          <Text style={styles.securityDescription}>
            Мы не храним CVV коды и не передаем данные карт третьим лицам.
          </Text>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Add Card Modal */}
      <Modal
        visible={showAddCardModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowAddCardModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Добавить карту</Text>
              <TouchableOpacity onPress={() => setShowAddCardModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Номер карты</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="1234 5678 9012 3456"
                  value={newCardData.cardNumber}
                  onChangeText={(text) => setNewCardData(prev => ({
                    ...prev,
                    cardNumber: formatCardNumber(text)
                  }))}
                  keyboardType="numeric"
                  maxLength={19}
                />
              </View>

              <View style={styles.formRow}>
                <View style={[styles.formGroup, { flex: 1, marginRight: 8 }]}>
                  <Text style={styles.formLabel}>Месяц</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="ММ"
                    value={newCardData.expiryMonth}
                    onChangeText={(text) => setNewCardData(prev => ({
                      ...prev,
                      expiryMonth: text.replace(/[^0-9]/g, '').substring(0, 2)
                    }))}
                    keyboardType="numeric"
                    maxLength={2}
                  />
                </View>

                <View style={[styles.formGroup, { flex: 1, marginLeft: 8, marginRight: 8 }]}>
                  <Text style={styles.formLabel}>Год</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="ГГГГ"
                    value={newCardData.expiryYear}
                    onChangeText={(text) => setNewCardData(prev => ({
                      ...prev,
                      expiryYear: text.replace(/[^0-9]/g, '').substring(0, 4)
                    }))}
                    keyboardType="numeric"
                    maxLength={4}
                  />
                </View>

                <View style={[styles.formGroup, { flex: 1, marginLeft: 8 }]}>
                  <Text style={styles.formLabel}>CVV</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="123"
                    value={newCardData.cvv}
                    onChangeText={(text) => setNewCardData(prev => ({
                      ...prev,
                      cvv: text.replace(/[^0-9]/g, '').substring(0, 4)
                    }))}
                    keyboardType="numeric"
                    maxLength={4}
                    secureTextEntry
                  />
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Имя держателя карты</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="Иван Иванов"
                  value={newCardData.holderName}
                  onChangeText={(text) => setNewCardData(prev => ({
                    ...prev,
                    holderName: text
                  }))}
                  autoCapitalize="words"
                />
              </View>

              <View style={styles.modalActions}>
                <TouchableOpacity
                  style={styles.modalCancelButton}
                  onPress={() => setShowAddCardModal(false)}
                  disabled={processing}
                >
                  <Text style={styles.modalCancelButtonText}>Отмена</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modalAddButton}
                  onPress={handleAddCard}
                  disabled={processing}
                >
                  {processing ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <Text style={styles.modalAddButtonText}>Добавить карту</Text>
                  )}
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
  },
  errorDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 30,
  },
  retryButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 32,
    paddingVertical: 16,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  paymentMethodCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  defaultMethodCard: {
    borderColor: '#E91E63',
    backgroundColor: '#E91E63' + '05',
  },
  methodHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  methodInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  methodDetails: {
    marginLeft: 12,
    flex: 1,
  },
  methodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  holderName: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  expiryDate: {
    fontSize: 12,
    color: '#999999',
  },
  defaultBadge: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  defaultBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  methodFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  methodMeta: {
    flex: 1,
  },
  addedDate: {
    fontSize: 12,
    color: '#999999',
    marginBottom: 2,
  },
  lastUsed: {
    fontSize: 12,
    color: '#999999',
  },
  methodActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  setDefaultButton: {
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  setDefaultButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#E91E63',
  },
  deleteButton: {
    backgroundColor: '#F44336' + '10',
    borderRadius: 8,
    padding: 6,
  },
  addMethodsSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderTopWidth: 8,
    borderTopColor: '#f5f5f5',
  },
  addMethodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  addMethodButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333333',
    flex: 1,
    marginLeft: 12,
  },
  securitySection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    borderTopWidth: 8,
    borderTopColor: '#f5f5f5',
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
  },
  securityDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  modalBody: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  formGroup: {
    marginBottom: 20,
  },
  formRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333333',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333333',
    backgroundColor: '#ffffff',
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
  },
  modalAddButton: {
    flex: 1,
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalAddButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default PaymentMethodsScreen;