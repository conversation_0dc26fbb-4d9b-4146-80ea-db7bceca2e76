import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Modal,
  TextInput,
  Dimensions
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface PaymentMethodsScreenProps {}

interface PaymentMethod {
  id: string;
  type: 'card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer';
  isDefault: boolean;
  details: {
    // For cards
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
    holderName?: string;
    // For PayPal
    email?: string;
    // For bank transfer
    bankName?: string;
    accountNumber?: string;
  };
  addedAt: string;
  lastUsed?: string;
  isActive: boolean;
}

interface NewCardData {
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  cvv: string;
  holderName: string;
}

const PaymentMethodsScreen: React.FC<PaymentMethodsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [newCardData, setNewCardData] = useState<NewCardData>({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    holderName: ''
  });
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadPaymentMethods();
    }, [])
  );

  const loadPaymentMethods = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockPaymentMethods: PaymentMethod[] = [
        {
          id: '1',
          type: 'card',
          isDefault: true,
          details: {
            last4: '1234',
            brand: 'Visa',
            expiryMonth: 12,
            expiryYear: 2026,
            holderName: 'Иван Иванов'
          },
          addedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          lastUsed: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        },
        {
          id: '2',
          type: 'card',
          isDefault: false,
          details: {
            last4: '5678',
            brand: 'Mastercard',
            expiryMonth: 8,
            expiryYear: 2025,
            holderName: 'Иван Иванов'
          },
          addedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          lastUsed: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        },
        {
          id: '3',
          type: 'paypal',
          isDefault: false,
          details: {
            email: '<EMAIL>'
          },
          addedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
          isActive: true
        }
      ];

      setPaymentMethods(mockPaymentMethods);
    } catch (err: any) {
      setError('Ошибка загрузки способов оплаты');
      console.error('Payment methods loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCard = async () => {
    if (!validateCardData()) return;

    try {
      setProcessing(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const newCard: PaymentMethod = {
        id: Date.now().toString(),
        type: 'card',
        isDefault: paymentMethods.length === 0,
        details: {
          last4: newCardData.cardNumber.slice(-4),
          brand: getCardBrand(newCardData.cardNumber),
          expiryMonth: parseInt(newCardData.expiryMonth),
          expiryYear: parseInt(newCardData.expiryYear),
          holderName: newCardData.holderName
        },
        addedAt: new Date().toISOString(),
        isActive: true
      };

      setPaymentMethods(prev => [...prev, newCard]);
      setShowAddCardModal(false);
      setNewCardData({
        cardNumber: '',
        expiryMonth: '',
        expiryYear: '',
        cvv: '',
        holderName: ''
      });

      Alert.alert('Успех', 'Карта успешно добавлена');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось добавить карту');
    } finally {
      setProcessing(false);
    }
  };

  const validateCardData = (): boolean => {
    if (newCardData.cardNumber.replace(/\s/g, '').length < 16) {
      Alert.alert('Ошибка', 'Введите корректный номер карты');
      return false;
    }
    if (!newCardData.expiryMonth || !newCardData.expiryYear) {
      Alert.alert('Ошибка', 'Введите срок действия карты');
      return false;
    }
    if (newCardData.cvv.length < 3) {
      Alert.alert('Ошибка', 'Введите корректный CVV код');
      return false;
    }
    if (!newCardData.holderName.trim()) {
      Alert.alert('Ошибка', 'Введите имя держателя карты');
      return false;
    }
    return true;
  };

  const getCardBrand = (cardNumber: string): string => {
    const number = cardNumber.replace(/\s/g, '');
    if (number.startsWith('4')) return 'Visa';
    if (number.startsWith('5') || number.startsWith('2')) return 'Mastercard';
    if (number.startsWith('3')) return 'American Express';
    return 'Unknown';
  };

  const handleSetDefault = async (methodId: string) => {
    try {
      setProcessing(true);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      setPaymentMethods(prev => prev.map(method => ({
        ...method,
        isDefault: method.id === methodId
      })));

      Alert.alert('Успех', 'Способ оплаты по умолчанию изменен');
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось изменить способ оплаты по умолчанию');
    } finally {
      setProcessing(false);
    }
  };

  const handleDeleteMethod = (methodId: string) => {
    const method = paymentMethods.find(m => m.id === methodId);
    if (!method) return;

    Alert.alert(
      'Удалить способ оплаты',
      `Вы уверены, что хотите удалить ${getMethodDisplayName(method)}?`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Удалить',
          style: 'destructive',
          onPress: async () => {
            try {
              setProcessing(true);

              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 500));

              setPaymentMethods(prev => prev.filter(m => m.id !== methodId));
              Alert.alert('Успех', 'Способ оплаты удален');
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось удалить способ оплаты');
            } finally {
              setProcessing(false);
            }
          }
        }
      ]
    );
  };