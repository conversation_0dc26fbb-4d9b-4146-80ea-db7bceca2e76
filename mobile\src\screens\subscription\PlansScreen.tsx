import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Dimensions,
  Modal
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

interface PlansScreenProps {}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  discount?: {
    percentage: number;
    description: string;
  };
  features: Array<{
    id: string;
    name: string;
    description: string;
    isIncluded: boolean;
    isHighlight?: boolean;
  }>;
  limits: {
    dailyLikes: number | 'unlimited';
    superLikes: number;
    boosts: number;
    rewinds: number;
    whoLikesYou: boolean;
    readReceipts: boolean;
    onlineStatus: boolean;
    prioritySupport: boolean;
  };
  badge?: {
    text: string;
    color: string;
  };
  isPopular?: boolean;
  isCurrentPlan?: boolean;
}

interface CurrentSubscription {
  planId: string;
  planName: string;
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
  nextBillingDate?: string;
  trialDaysLeft?: number;
}

const PlansScreen: React.FC<PlansScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<CurrentSubscription | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);
  const [purchasing, setPurchasing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPlans();
    loadCurrentSubscription();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);

      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockPlans: SubscriptionPlan[] = [
        {
          id: 'free',
          name: 'Базовый',
          description: 'Основные функции знакомств',
          price: {
            monthly: 0,
            yearly: 0,
            currency: 'RUB'
          },
          features: [
            { id: '1', name: 'Просмотр анкет', description: 'Неограниченный просмотр профилей', isIncluded: true },
            { id: '2', name: 'Лайки', description: 'Ограниченное количество лайков в день', isIncluded: true },
            { id: '3', name: 'Сообщения', description: 'Общение с взаимными симпатиями', isIncluded: true },
            { id: '4', name: 'Супер-лайки', description: 'Выделите свой профиль', isIncluded: false },
            { id: '5', name: 'Буст', description: 'Станьте популярнее на 30 минут', isIncluded: false },
            { id: '6', name: 'Кто лайкнул', description: 'Посмотрите, кому вы понравились', isIncluded: false }
          ],
          limits: {
            dailyLikes: 10,
            superLikes: 1,
            boosts: 0,
            rewinds: 0,
            whoLikesYou: false,
            readReceipts: false,
            onlineStatus: false,
            prioritySupport: false
          },
          isCurrentPlan: true
        },
        {
          id: 'premium',
          name: 'Премиум',
          description: 'Расширенные возможности для знакомств',
          price: {
            monthly: 990,
            yearly: 7990,
            currency: 'RUB'
          },
          discount: {
            percentage: 33,
            description: 'Экономия при годовой подписке'
          },
          features: [
            { id: '1', name: 'Неограниченные лайки', description: 'Лайкайте без ограничений', isIncluded: true, isHighlight: true },
            { id: '2', name: 'Супер-лайки', description: '5 супер-лайков в день', isIncluded: true },
            { id: '3', name: 'Буст', description: '1 буст в месяц', isIncluded: true },
            { id: '4', name: 'Кто лайкнул', description: 'Посмотрите всех, кому понравились', isIncluded: true, isHighlight: true },
            { id: '5', name: 'Отмена действий', description: 'Верните случайный свайп', isIncluded: true },
            { id: '6', name: 'Статус прочтения', description: 'Узнайте, прочитали ли сообщение', isIncluded: true },
            { id: '7', name: 'Скрытый режим', description: 'Просматривайте анкеты незаметно', isIncluded: false }
          ],
          limits: {
            dailyLikes: 'unlimited',
            superLikes: 5,
            boosts: 1,
            rewinds: 5,
            whoLikesYou: true,
            readReceipts: true,
            onlineStatus: false,
            prioritySupport: true
          },
          isPopular: true
        },
        {
          id: 'platinum',
          name: 'Платинум',
          description: 'Максимальные возможности для серьезных знакомств',
          price: {
            monthly: 1990,
            yearly: 15990,
            currency: 'RUB'
          },
          discount: {
            percentage: 33,
            description: 'Экономия при годовой подписке'
          },
          badge: {
            text: 'VIP',
            color: '#FFD700'
          },
          features: [
            { id: '1', name: 'Все функции Премиум', description: 'Полный доступ к премиум возможностям', isIncluded: true },
            { id: '2', name: 'Приоритет в показе', description: 'Ваш профиль показывается первым', isIncluded: true, isHighlight: true },
            { id: '3', name: 'Скрытый режим', description: 'Просматривайте анкеты незаметно', isIncluded: true },
            { id: '4', name: 'Статус онлайн', description: 'Показывайте свой статус активности', isIncluded: true },
            { id: '5', name: 'Супер-буст', description: '1 супер-буст в месяц (3 часа)', isIncluded: true, isHighlight: true },
            { id: '6', name: 'Приоритетная поддержка', description: 'Быстрая помощь от службы поддержки', isIncluded: true },
            { id: '7', name: 'Эксклюзивные события', description: 'Доступ к VIP мероприятиям', isIncluded: true }
          ],
          limits: {
            dailyLikes: 'unlimited',
            superLikes: 10,
            boosts: 2,
            rewinds: 'unlimited',
            whoLikesYou: true,
            readReceipts: true,
            onlineStatus: true,
            prioritySupport: true
          }
        }
      ];

      setPlans(mockPlans);
    } catch (err: any) {
      setError('Ошибка загрузки планов подписки');
      console.error('Plans loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentSubscription = async () => {
    try {
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const mockSubscription: CurrentSubscription = {
        planId: 'free',
        planName: 'Базовый',
        status: 'active',
        startDate: '2024-01-01T00:00:00Z',
        endDate: '2024-12-31T23:59:59Z',
        autoRenew: false,
        paymentMethod: 'Бесплатный план'
      };

      setCurrentSubscription(mockSubscription);
    } catch (error) {
      console.error('Current subscription loading error:', error);
    }
  };

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    if (plan.id === 'free') {
      Alert.alert('Информация', 'Вы уже используете бесплатный план');
      return;
    }

    setSelectedPlan(plan);
    setShowPurchaseModal(true);
  };

  const handlePurchase = async () => {
    if (!selectedPlan) return;

    try {
      setPurchasing(true);

      // TODO: Implement actual purchase logic with payment provider
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Update current subscription
      const newSubscription: CurrentSubscription = {
        planId: selectedPlan.id,
        planName: selectedPlan.name,
        status: 'active',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + (billingPeriod === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000).toISOString(),
        autoRenew: true,
        paymentMethod: 'Банковская карта',
        nextBillingDate: new Date(Date.now() + (billingPeriod === 'yearly' ? 365 : 30) * 24 * 60 * 60 * 1000).toISOString()
      };

      setCurrentSubscription(newSubscription);
      setShowPurchaseModal(false);
      setSelectedPlan(null);

      Alert.alert(
        'Поздравляем!',
        `Вы успешно оформили подписку "${selectedPlan.name}". Наслаждайтесь расширенными возможностями!`
      );

      // Update plans to reflect current subscription
      setPlans(prev => prev.map(plan => ({
        ...plan,
        isCurrentPlan: plan.id === selectedPlan.id
      })));

    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось оформить подписку. Попробуйте еще раз.');
    } finally {
      setPurchasing(false);
    }
  };

  const handleManageSubscription = () => {
    navigation.navigate('SubscriptionManagement');
  };

  const formatPrice = (price: number, period: 'monthly' | 'yearly') => {
    if (price === 0) return 'Бесплатно';

    const periodText = period === 'monthly' ? '/мес' : '/год';
    return `${price}₽${periodText}`;
  };

  const calculateYearlySavings = (plan: SubscriptionPlan) => {
    const monthlyTotal = plan.price.monthly * 12;
    const yearlyPrice = plan.price.yearly;
    const savings = monthlyTotal - yearlyPrice;
    const percentage = Math.round((savings / monthlyTotal) * 100);
    return { savings, percentage };
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#4CAF50';
      case 'trial':
        return '#FF9800';
      case 'cancelled':
        return '#F44336';
      case 'expired':
        return '#9E9E9E';
      default:
        return '#9E9E9E';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активна';
      case 'trial':
        return 'Пробный период';
      case 'cancelled':
        return 'Отменена';
      case 'expired':
        return 'Истекла';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка планов...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Планы подписки</Text>
        <TouchableOpacity
          style={styles.manageButton}
          onPress={handleManageSubscription}
        >
          <Icon name="settings" size={24} color="#333333" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Subscription */}
        {currentSubscription && (
          <View style={styles.currentSubscriptionContainer}>
            <Text style={styles.currentSubscriptionTitle}>Текущая подписка</Text>
            <View style={styles.currentSubscriptionCard}>
              <View style={styles.currentSubscriptionHeader}>
                <Text style={styles.currentSubscriptionName}>
                  {currentSubscription.planName}
                </Text>
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(currentSubscription.status) }]}>
                  <Text style={styles.statusBadgeText}>
                    {getStatusLabel(currentSubscription.status)}
                  </Text>
                </View>
              </View>

              <Text style={styles.currentSubscriptionDetails}>
                Действует до: {new Date(currentSubscription.endDate).toLocaleDateString('ru-RU')}
              </Text>

              {currentSubscription.nextBillingDate && (
                <Text style={styles.currentSubscriptionDetails}>
                  Следующее списание: {new Date(currentSubscription.nextBillingDate).toLocaleDateString('ru-RU')}
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Billing Period Toggle */}
        <View style={styles.billingToggleContainer}>
          <Text style={styles.billingToggleTitle}>Период оплаты</Text>
          <View style={styles.billingToggle}>
            <TouchableOpacity
              style={[
                styles.billingToggleButton,
                billingPeriod === 'monthly' && styles.billingToggleButtonActive
              ]}
              onPress={() => setBillingPeriod('monthly')}
            >
              <Text style={[
                styles.billingToggleText,
                billingPeriod === 'monthly' && styles.billingToggleTextActive
              ]}>
                Месяц
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.billingToggleButton,
                billingPeriod === 'yearly' && styles.billingToggleButtonActive
              ]}
              onPress={() => setBillingPeriod('yearly')}
            >
              <Text style={[
                styles.billingToggleText,
                billingPeriod === 'yearly' && styles.billingToggleTextActive
              ]}>
                Год
              </Text>
              <View style={styles.savingsBadge}>
                <Text style={styles.savingsBadgeText}>-33%</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Plans */}
        <View style={styles.plansContainer}>
          {plans.map((plan) => (
            <TouchableOpacity
              key={plan.id}
              style={[
                styles.planCard,
                plan.isPopular && styles.planCardPopular,
                plan.isCurrentPlan && styles.planCardCurrent
              ]}
              onPress={() => handlePlanSelect(plan)}
              disabled={plan.isCurrentPlan}
            >
              {plan.isPopular && (
                <View style={styles.popularBadge}>
                  <Text style={styles.popularBadgeText}>Популярный</Text>
                </View>
              )}

              {plan.badge && (
                <View style={[styles.planBadge, { backgroundColor: plan.badge.color }]}>
                  <Text style={styles.planBadgeText}>{plan.badge.text}</Text>
                </View>
              )}

              <View style={styles.planHeader}>
                <Text style={styles.planName}>{plan.name}</Text>
                <Text style={styles.planDescription}>{plan.description}</Text>
              </View>

              <View style={styles.planPricing}>
                <Text style={styles.planPrice}>
                  {formatPrice(
                    billingPeriod === 'monthly' ? plan.price.monthly : plan.price.yearly,
                    billingPeriod
                  )}
                </Text>

                {billingPeriod === 'yearly' && plan.price.monthly > 0 && (
                  <View style={styles.savingsInfo}>
                    <Text style={styles.originalPrice}>
                      {plan.price.monthly * 12}₽/год
                    </Text>
                    <Text style={styles.savingsText}>
                      Экономия {calculateYearlySavings(plan).savings}₽
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.planFeatures}>
                {plan.features.map((feature) => (
                  <View key={feature.id} style={styles.featureItem}>
                    <Icon
                      name={feature.isIncluded ? 'check' : 'close'}
                      size={16}
                      color={feature.isIncluded ? '#4CAF50' : '#cccccc'}
                    />
                    <Text style={[
                      styles.featureText,
                      !feature.isIncluded && styles.featureTextDisabled,
                      feature.isHighlight && styles.featureTextHighlight
                    ]}>
                      {feature.name}
                    </Text>
                  </View>
                ))}
              </View>

              <View style={styles.planAction}>
                {plan.isCurrentPlan ? (
                  <View style={styles.currentPlanButton}>
                    <Icon name="check" size={20} color="#4CAF50" />
                    <Text style={styles.currentPlanButtonText}>Текущий план</Text>
                  </View>
                ) : (
                  <View style={styles.selectPlanButton}>
                    <Text style={styles.selectPlanButtonText}>
                      {plan.id === 'free' ? 'Перейти на бесплатный' : 'Выбрать план'}
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Benefits */}
        <View style={styles.benefitsContainer}>
          <Text style={styles.benefitsTitle}>Преимущества премиум подписки</Text>

          <View style={styles.benefitItem}>
            <Icon name="favorite" size={24} color="#E91E63" />
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Неограниченные лайки</Text>
              <Text style={styles.benefitDescription}>
                Лайкайте столько профилей, сколько хотите
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Icon name="visibility" size={24} color="#E91E63" />
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Кто лайкнул вас</Text>
              <Text style={styles.benefitDescription}>
                Посмотрите всех, кому понравился ваш профиль
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Icon name="flash-on" size={24} color="#E91E63" />
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Буст профиля</Text>
              <Text style={styles.benefitDescription}>
                Станьте популярнее и получите больше совпадений
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Icon name="undo" size={24} color="#E91E63" />
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Отмена действий</Text>
              <Text style={styles.benefitDescription}>
                Верните случайный свайп влево
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Purchase Modal */}
      <Modal
        visible={showPurchaseModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowPurchaseModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                Подтвердите покупку
              </Text>
              <TouchableOpacity onPress={() => setShowPurchaseModal(false)}>
                <Icon name="close" size={24} color="#333333" />
              </TouchableOpacity>
            </View>

            {selectedPlan && (
              <View style={styles.modalBody}>
                <Text style={styles.modalPlanName}>{selectedPlan.name}</Text>
                <Text style={styles.modalPlanPrice}>
                  {formatPrice(
                    billingPeriod === 'monthly' ? selectedPlan.price.monthly : selectedPlan.price.yearly,
                    billingPeriod
                  )}
                </Text>

                {billingPeriod === 'yearly' && selectedPlan.discount && (
                  <Text style={styles.modalDiscount}>
                    Экономия {selectedPlan.discount.percentage}% при годовой подписке
                  </Text>
                )}

                <Text style={styles.modalDescription}>
                  Подписка будет автоматически продлеваться каждый{' '}
                  {billingPeriod === 'monthly' ? 'месяц' : 'год'}.
                  Вы можете отменить её в любое время.
                </Text>
              </View>
            )}

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={styles.modalCancelButton}
                onPress={() => setShowPurchaseModal(false)}
              >
                <Text style={styles.modalCancelButtonText}>Отмена</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.modalPurchaseButton}
                onPress={handlePurchase}
                disabled={purchasing}
              >
                {purchasing ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <Text style={styles.modalPurchaseButtonText}>Купить</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  manageButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  currentSubscriptionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  currentSubscriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  currentSubscriptionCard: {
    backgroundColor: '#f8f8f8',
    borderRadius: 12,
    padding: 16,
  },
  currentSubscriptionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentSubscriptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  statusBadge: {
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  statusBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  currentSubscriptionDetails: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  billingToggleContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  billingToggleTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    padding: 4,
  },
  billingToggleButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    position: 'relative',
  },
  billingToggleButtonActive: {
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  billingToggleText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  billingToggleTextActive: {
    color: '#333333',
    fontWeight: '600',
  },
  savingsBadge: {
    position: 'absolute',
    top: -4,
    right: 8,
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  savingsBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  plansContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  planCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    position: 'relative',
  },
  planCardPopular: {
    borderColor: '#E91E63',
    transform: [{ scale: 1.02 }],
  },
  planCardCurrent: {
    borderColor: '#4CAF50',
    backgroundColor: '#f8fff8',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    left: 20,
    right: 20,
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 6,
    alignItems: 'center',
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  planBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  planBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ffffff',
  },
  planHeader: {
    marginBottom: 16,
    marginTop: 8,
  },
  planName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 4,
  },
  planDescription: {
    fontSize: 14,
    color: '#666666',
  },
  planPricing: {
    marginBottom: 20,
  },
  planPrice: {
    fontSize: 24,
    fontWeight: '700',
    color: '#E91E63',
    marginBottom: 4,
  },
  savingsInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  savingsText: {
    fontSize: 12,
    color: '#4CAF50',
    fontWeight: '600',
  },
  planFeatures: {
    marginBottom: 20,
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333333',
    flex: 1,
  },
  featureTextDisabled: {
    color: '#cccccc',
  },
  featureTextHighlight: {
    fontWeight: '600',
    color: '#E91E63',
  },
  planAction: {
    marginTop: 8,
  },
  currentPlanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50' + '10',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  currentPlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4CAF50',
  },
  selectPlanButton: {
    backgroundColor: '#E91E63',
    borderRadius: 12,
    paddingVertical: 12,
    alignItems: 'center',
  },
  selectPlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  benefitsContainer: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 20,
    textAlign: 'center',
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 16,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  bottomPadding: {
    height: 40,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    margin: 20,
    width: width - 40,
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
  modalBody: {
    padding: 20,
    alignItems: 'center',
  },
  modalPlanName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 8,
  },
  modalPlanPrice: {
    fontSize: 24,
    fontWeight: '700',
    color: '#E91E63',
    marginBottom: 8,
  },
  modalDiscount: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
    marginBottom: 16,
  },
  modalDescription: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 20,
  },
  modalActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  modalCancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  modalCancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#666666',
  },
  modalPurchaseButton: {
    flex: 1,
    backgroundColor: '#E91E63',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  modalPurchaseButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});

export default PlansScreen;