import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Alert,
  TextInput,
  RefreshControl,
  Image
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface BlockedContactsScreenProps {}

interface BlockedUser {
  id: string;
  name: string;
  age: number;
  avatar?: string;
  blockedAt: string;
  reason?: string;
  mutualFriends?: number;
}

const BlockedContactsScreen: React.FC<BlockedContactsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadBlockedUsers();
    }, [])
  );

  const loadBlockedUsers = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockBlockedUsers: BlockedUser[] = [
        {
          id: '1',
          name: 'Анна К.',
          age: 25,
          avatar: 'https://example.com/avatar1.jpg',
          blockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          reason: 'Неподобающее поведение',
          mutualFriends: 3
        },
        {
          id: '2',
          name: 'Михаил С.',
          age: 30,
          blockedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          reason: 'Спам',
          mutualFriends: 0
        },
        {
          id: '3',
          name: 'Елена В.',
          age: 28,
          avatar: 'https://example.com/avatar3.jpg',
          blockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          mutualFriends: 1
        }
      ];
      
      setBlockedUsers(mockBlockedUsers);
    } catch (error) {
      console.error('Error loading blocked users:', error);
      Alert.alert('Ошибка', 'Не удалось загрузить заблокированных пользователей');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBlockedUsers();
    setRefreshing(false);
  };

  const handleUnblockUser = (userId: string, userName: string) => {
    Alert.alert(
      'Разблокировать пользователя',
      `Вы уверены, что хотите разблокировать ${userName}? Этот пользователь снова сможет видеть ваш профиль и связываться с вами.`,
      [
        { text: 'Отмена', style: 'cancel' },
        {
          text: 'Разблокировать',
          onPress: async () => {
            try {
              setProcessing(userId);
              
              // TODO: Implement actual API call
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              setBlockedUsers(prev => prev.filter(user => user.id !== userId));
              Alert.alert('Успех', `${userName} разблокирован`);
            } catch (error) {
              Alert.alert('Ошибка', 'Не удалось разблокировать пользователя');
            } finally {
              setProcessing(null);
            }
          }
        }
      ]
    );
  };

  const handleViewProfile = (userId: string) => {
    // Navigate to blocked user profile (limited view)
    navigation.navigate('BlockedUserProfile', { userId });
  };

  const formatBlockedDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return 'Сегодня';
    } else if (diffInDays === 1) {
      return 'Вчера';
    } else if (diffInDays < 30) {
      return `${diffInDays} дн. назад`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `${months} мес. назад`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `${years} г. назад`;
    }
  };

  const filteredUsers = blockedUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderBlockedUser = (user: BlockedUser) => (
    <View key={user.id} style={styles.userCard}>
      <TouchableOpacity 
        style={styles.userInfo}
        onPress={() => handleViewProfile(user.id)}
      >
        <View style={styles.avatarContainer}>
          {user.avatar ? (
            <Image source={{ uri: user.avatar }} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Icon name="person" size={24} color="#666666" />
            </View>
          )}
        </View>
        
        <View style={styles.userDetails}>
          <Text style={styles.userName}>{user.name}, {user.age}</Text>
          <Text style={styles.blockedDate}>
            Заблокирован {formatBlockedDate(user.blockedAt)}
          </Text>
          {user.reason && (
            <Text style={styles.blockReason}>Причина: {user.reason}</Text>
          )}
          {user.mutualFriends && user.mutualFriends > 0 && (
            <Text style={styles.mutualFriends}>
              {user.mutualFriends} общих знакомых
            </Text>
          )}
        </View>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[
          styles.unblockButton,
          processing === user.id && styles.unblockButtonDisabled
        ]}
        onPress={() => handleUnblockUser(user.id, user.name)}
        disabled={processing === user.id}
      >
        {processing === user.id ? (
          <ActivityIndicator size="small" color="#E91E63" />
        ) : (
          <>
            <Icon name="block" size={16} color="#E91E63" />
            <Text style={styles.unblockButtonText}>Разблокировать</Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#E91E63" />
          <Text style={styles.loadingText}>Загрузка...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Заблокированные</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Поиск заблокированных..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#999999"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon name="clear" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Content */}
      <ScrollView 
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {filteredUsers.length > 0 ? (
          <>
            <View style={styles.infoSection}>
              <Icon name="info" size={20} color="#2196F3" />
              <Text style={styles.infoText}>
                Заблокированные пользователи не могут видеть ваш профиль, отправлять сообщения или взаимодействовать с вами
              </Text>
            </View>
            
            <View style={styles.usersSection}>
              {filteredUsers.map(renderBlockedUser)}
            </View>
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="block" size={64} color="#cccccc" />
            <Text style={styles.emptyTitle}>
              {searchQuery ? 'Пользователи не найдены' : 'Нет заблокированных пользователей'}
            </Text>
            <Text style={styles.emptyDescription}>
              {searchQuery 
                ? 'Попробуйте изменить поисковый запрос'
                : 'Когда вы заблокируете кого-то, они появятся здесь'
              }
            </Text>
          </View>
        )}

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
    marginTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#f8f8f8',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#E3F2FD',
    margin: 20,
    padding: 16,
    borderRadius: 12,
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: '#1976D2',
    lineHeight: 20,
    marginLeft: 12,
  },
  usersSection: {
    paddingHorizontal: 20,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  blockedDate: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  blockReason: {
    fontSize: 12,
    color: '#F44336',
    marginBottom: 2,
  },
  mutualFriends: {
    fontSize: 12,
    color: '#2196F3',
  },
  unblockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E91E63' + '10',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 4,
  },
  unblockButtonDisabled: {
    opacity: 0.5,
  },
  unblockButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#E91E63',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333333',
    marginTop: 20,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 22,
  },
  bottomPadding: {
    height: 40,
  },
});

export default BlockedContactsScreen;
