import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Platform
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';

interface NotificationSettingsScreenProps {}

interface NotificationSettings {
  pushNotifications: {
    enabled: boolean;
    matches: boolean;
    messages: boolean;
    likes: boolean;
    superLikes: boolean;
    visits: boolean;
    promotions: boolean;
  };
  emailNotifications: {
    enabled: boolean;
    matches: boolean;
    messages: boolean;
    weeklyDigest: boolean;
    promotions: boolean;
    securityAlerts: boolean;
  };
  inAppNotifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    showPreviews: boolean;
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  frequency: {
    matches: 'instant' | 'hourly' | 'daily' | 'off';
    messages: 'instant' | 'hourly' | 'daily' | 'off';
    likes: 'instant' | 'hourly' | 'daily' | 'off';
  };
}

const NotificationSettingsScreen: React.FC<NotificationSettingsScreenProps> = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [error, setError] = useState<string | null>(null);

  useFocusEffect(
    useCallback(() => {
      loadNotificationSettings();
      checkNotificationPermissions();
    }, [])
  );

  const loadNotificationSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load from AsyncStorage
      const savedSettings = await AsyncStorage.getItem('notificationSettings');
      let notificationSettings: NotificationSettings;

      if (savedSettings) {
        notificationSettings = JSON.parse(savedSettings);
      } else {
        // Default settings
        notificationSettings = {
          pushNotifications: {
            enabled: true,
            matches: true,
            messages: true,
            likes: true,
            superLikes: true,
            visits: false,
            promotions: false
          },
          emailNotifications: {
            enabled: true,
            matches: true,
            messages: false,
            weeklyDigest: true,
            promotions: false,
            securityAlerts: true
          },
          inAppNotifications: {
            enabled: true,
            sound: true,
            vibration: true,
            showPreviews: true
          },
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00'
          },
          frequency: {
            matches: 'instant',
            messages: 'instant',
            likes: 'hourly'
          }
        };
      }

      // TODO: Load from API/user preferences
      await new Promise(resolve => setTimeout(resolve, 500));

      setSettings(notificationSettings);
    } catch (err: any) {
      setError('Ошибка загрузки настроек уведомлений');
      console.error('Notification settings loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionStatus(status);
    } catch (error) {
      console.error('Permission check error:', error);
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      setPermissionStatus(status);

      if (status === 'granted') {
        Alert.alert('Разрешения получены', 'Теперь вы будете получать уведомления');
      } else {
        Alert.alert(
          'Разрешения не получены',
          'Для получения уведомлений разрешите их в настройках устройства'
        );
      }
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось запросить разрешения');
    }
  };

  const saveSettings = async (updatedSettings: Partial<NotificationSettings>) => {
    if (!settings) return;

    try {
      setSaving(true);

      const newSettings = { ...settings, ...updatedSettings };

      // Save to AsyncStorage
      await AsyncStorage.setItem('notificationSettings', JSON.stringify(newSettings));

      // TODO: Save to API
      await new Promise(resolve => setTimeout(resolve, 300));

      setSettings(newSettings);
    } catch (error) {
      Alert.alert('Ошибка', 'Не удалось сохранить настройки');
    } finally {
      setSaving(false);
    }
  };

  const handlePushToggle = async (key: keyof NotificationSettings['pushNotifications'], value: boolean) => {
    if (!settings) return;

    if (key === 'enabled' && value && permissionStatus !== 'granted') {
      await requestNotificationPermissions();
      if (permissionStatus !== 'granted') return;
    }

    await saveSettings({
      pushNotifications: {
        ...settings.pushNotifications,
        [key]: value
      }
    });
  };

  const handleEmailToggle = async (key: keyof NotificationSettings['emailNotifications'], value: boolean) => {
    if (!settings) return;

    await saveSettings({
      emailNotifications: {
        ...settings.emailNotifications,
        [key]: value
      }
    });
  };

  const handleInAppToggle = async (key: keyof NotificationSettings['inAppNotifications'], value: boolean) => {
    if (!settings) return;

    await saveSettings({
      inAppNotifications: {
        ...settings.inAppNotifications,
        [key]: value
      }
    });
  };

  const handleQuietHoursToggle = async (enabled: boolean) => {
    if (!settings) return;

    await saveSettings({
      quietHours: {
        ...settings.quietHours,
        enabled
      }
    });
  };

  const handleFrequencyChange = async (type: keyof NotificationSettings['frequency'], frequency: string) => {
    if (!settings) return;

    await saveSettings({
      frequency: {
        ...settings.frequency,
        [type]: frequency as 'instant' | 'hourly' | 'daily' | 'off'
      }
    });
  };